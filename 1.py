import requests,json,time,re,hashlib,uuid
from urllib.parse import quote, quote_plus

def create_image_data(image_url, goods_id, pub_session=None):
    """创建完整的图片数据结构，与淘宝评价系统完全兼容 - 精确匹配实际POST数据格式"""
    if not pub_session:
        pub_session = str(uuid.uuid4())

    # 确保URL格式正确，移除多余空格
    image_url = image_url.replace(" ", "")

    # 按照实际POST数据的精确格式构建statInfo
    # 注意：这里的转义层级必须与实际数据完全匹配

    # 第一层：构建最内层的配置JSON字符串
    music_frame_config_str = json.dumps({
        "type": "music",
        "frameResolutionSize": 256,
        "frameCount": 2,
        "frameInterval": 0.5,
        "frameQuality": 50,
        "frameDecodeTimeout": 1000,
        "frameUploadTimeout": 1500
    }, separators=(',', ':'))

    topic_frame_config_str = json.dumps({
        "type": "topic",
        "frameResolutionSize": 256,
        "frameCount": 5,
        "frameInterval": 0.2,
        "frameQuality": 70,
        "frameDecodeTimeout": 5000,
        "frameUploadTimeout": 4000
    }, separators=(',', ':'))

    # 第二层：构建ab_test_info JSON字符串
    ab_test_info_str = json.dumps({
        "musicFrameConfig": music_frame_config_str,
        "topicFrameConfig": topic_frame_config_str
    }, separators=(',', ':'))

    # 第三层：构建完整的statInfo结构（按实际数据字段顺序）
    stat_info = {
        "ab_test_info": ab_test_info_str,
        "camera_rotation": 0,
        "filter": [{}],
        "fun_id": {},
        "goods_id": str(goods_id),
        "is_hq_record": False,
        "itemsticker_items": [],
        "pub_session": pub_session,
        "source": "user_record",
        "additionalInfo": {
            "imageSource": "1",
            "containExif": False,
            "isScreenshot": False,
            "OS": "Android"
        }
    }

    # 返回完整的图片数据结构
    return {
        "fileSourceTag": "taobao_camera",
        "height": 1440,
        "statInfo": json.dumps(stat_info, separators=(',', ':')),
        "url": image_url,
        "width": 1080
    }
def goToken(cookie):
    """获取淘宝API所需的token"""
    t = str(int(time.time()))
    date = '{}'
    xapi = 'mtop.tmall.kangaroo.core.service.route.aldlampservicefixedresv2'
    xv = '1.0'
    token = ''
    str1 = token + '&' + t + '&12574478&' + date
    str2 = bytes(str1, encoding='utf-8')  # md5
    sign = hashlib.md5(str2).hexdigest()
    data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=2019@weex_h5_0.12.14&data=' + str(
        quote(date, 'utf-8'))
    url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
    head = {
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
        'cookie': cookie,
        'url': url2
    }
    proxies = {"http": "http://127.0.0.1:7891",
               "https": "http://127.0.0.1:7891"}
    try:
        r = requests.get(url2, timeout=20, proxies=proxies,headers=head, stream=False, verify=False)
        if r.text.find('为空') != -1 or r.text.find('过期') != -1:
            set_cookie = str(r.headers.get('Set-Cookie'))
            mk = '_m_h5_tk=' + set_cookie.split('_m_h5_tk=')[1].split(';')[0] + ';'
            enc = '_m_h5_tk_enc=' + set_cookie.split('_m_h5_tk_enc=')[1].split(';')[0] + ';'
            return mk + enc
        else:
            return ''
    except Exception as e:
        print(f"获取token失败: {e}")
        return ''
def to_comment(final_params, cookie):
    """提交评价到淘宝服务器"""
    try:
        # 使用json模块正确序列化数据，避免编码问题
        date = json.dumps(final_params)
        xapi = 'mtop.taobao.rate.component.publish'
        xv = '1.0'
        t = str(int(time.time()))
        token = cookie.split('_m_h5_tk=')[1].split('_')[0]
        str1 = token + '&' + t + '&12574478&' + date
        str2 = bytes(str1, encoding='utf-8')  # md5
        sign = hashlib.md5(str2).hexdigest()
        data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=10022339@ltao_android_10.39.17'
        url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
        head = {
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
            'cookie': cookie,
            'Content-type': 'application/x-www-form-urlencoded; charset=utf-8'
        }
        # 使用json参数而不是data参数，让requests自动处理JSON序列化和编码
        proxies = {"http": "http://127.0.0.1:7891",
                   "https": "http://127.0.0.1:7891"}
        result = requests.post(url2, timeout=20, data={'data': date}, proxies=proxies,headers=head, verify=False)
        return result.json()
    except Exception as e:
        print(f"提交评价失败: {e}")
        return {"ret": ["FAILED::提交评价失败"]}
cookie = 'cna=eD73HFthMiICARsrz+Sezxu3; thw=cn; t=abd73d967394735f92bb87a256e4ca5a; _tb_token_=ee183e33981e5; cookie2=1c7281608be2f2147c1e1bb2776285fb; wk_cookie2=1048ac522e2b01ddba69ef35a8e87d81; wk_unb=UUpgRKryugbCESNt4g%3D%3D; lgc=t_1635766629972_054; cancelledSubSites=empty; dnk=t_1635766629972_054; tracknick=t_1635766629972_054; sn=; _hvn_lgc_=0; havana_lgc2_0=eyJoaWQiOjIyMTI3NDA5NTU1MDAsInNnIjoiYmZlODRhNWYxMjVmYjE4YjVkNTZhMDNmOWYzYjc3MmIiLCJzaXRlIjowLCJ0b2tlbiI6IjFEeEZWRmhnVHNrUFlKcVNlYUkxbHl3In0; aui=2212740955500; useNativeIM=false; unb=2212740955500; uc3=nk2=F6k3Hs%2Fm6MYjoXCLvkVLxyX2Kw%3D%3D&id2=UUpgRKryugbCESNt4g%3D%3D&vt3=F8dD2f%2FBly6g3OwpoVE%3D&lg2=VFC%2FuZ9ayeYq2g%3D%3D; csg=e3e4df51; cookie17=UUpgRKryugbCESNt4g%3D%3D; skt=e9e5b16808f825a9; existShop=MTc0NzY5MjE0OQ%3D%3D; uc4=id4=0%40U2gqy1txckoZ3LAdDde%2B9g0wwgFjXKf4&nk4=0%40FbMocGfzwBQ8aSqA70BxCLtIiZjtiSY0tSkKgNHY; _cc_=UtASsssmfA%3D%3D; _l_g_=Ug%3D%3D; sg=403; _nk_=t_1635766629972_054; cookie1=VAFf%2BE%2B1dYinyy6Z9nX%2F6YcrsZkBJ09fmkBj%2FjHSq08%3D; sgcookie=E100x9EbAP0Jovji7au5ySYhtPaPQVBnGvYQYMSP6EbU5wC0j1xLM8Elm37txJX4PwUCc74iKPP0QHNp94PBHATZjmb8IeIoNV%2FazrjcKYy658Z4ouJkYGohyK8VZ7z7Il2h; havana_lgc_exp=1747723253140; wwUserTip=false; 3PcFlag=1747818321435; mtop_partitioned_detect=1; _m_h5_tk=cd4dbfebba0db89171c87c5c14338a0e_1748349394084; _m_h5_tk_enc=f12f6cd967775530c41f5fa7beeadb89; sca=a5e96ba9; xlly_s=1; _samesite_flag_=true; tfstk=gQUicF9yqlo_5oXKvyg14m06AWSL1Vgj_-LxHqHVYvkQHhP46kc4HJ1f1VFqn-2Kd1MxkVoqo7woQdL9WJm4H7rtbG6jiJ2TprE9kSRmgJeUnm1sHxk0HSrO9aQ815gjuKXRyaCfVpx3SxJxuVgU67sJYaQ815ujuTBRyrLErWDs3qoZ0DPEdbhZumlNtvlmNnJa3-5nYfhS_Kuq_X7EZvkquqy4T6cxLfuquXKpYAeLKyWK7J6huVQNofoiU5kHoW4n_cx__vY2uyqnjY7iKE84-fNKBJcWriHzVAn-CJ7pSqVEi8cuIp5KLkVU50FCb9VoSPUmWzWk280gn0zZxI886WqI-XzPM3ML-Ae4SD5Jrm3Uy0uavM6tDVcu3PwMiEyz97UsArXkLxZtN2lg3aWh4ute4-fvlYQ3GntjbXGneldXB0RhjBQct6xzNclITN5htntjbXGne6fH47iZOX7N.; isg=BEpKIwsVFrBRUJXEZzK-GhbcmzDsO86VJxZL-tSD9h0oh-pBvMsepZD1k_Vbd0Yt'
if str(cookie).find('_m_h5_tk') == -1:  # 不带_m_h5_tk
    h5 = goToken(cookie)
    cookie = h5 + cookie
comment_text = '挺好的'
date = '{"dinamicOpen": "True","pageType": "taobaoTotalPublishRate","orderId":"2573020380920950055","platformType": "wireless","asac": "2A23A16I9YC7QUVMJ8EH2I"}'
xapi = 'mtop.taobao.rate.component.render'
xv = '1.0'
t = str(int(time.time()))
token = cookie.split('_m_h5_tk=')[1].split('_')[0]
str1 = token + '&' + t + '&12574478&' + date
str2 = bytes(str1, encoding='utf-8')  # md5
sign = hashlib.md5(str2).hexdigest()
data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=10022339@ltao_android_10.39.17&data=' + str(quote(date, 'utf-8'))
url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
head = {
    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
    'cookie': cookie}
proxies = {"http": "http://127.0.0.1:7891",
           "https": "http://127.0.0.1:7891"}
result = requests.get(url2, timeout=20, headers=head, proxies=proxies,stream=False, verify=False).json()
submit_params = {
    "data": {},
    "hierarchy": result["data"]["hierarchy"],
    "linkage": result["data"]["linkage"]
}
json_data = result["data"]["data"]
tem_list = {}
order_length = 0
order = ""
# 遍历 json_data 的键值对
for k, v in json_data.items():
    if k.startswith("itemRateContainer"):
        submit_params["data"][k] = v
        order_length += 1
    elif k.startswith("rateRoot_"):
        submit_params["data"][k] = v
    elif k.startswith("mainOrderScore"):
        if not order:
            match = re.search(r'mainOrderScore_(\d+)_\d+_\d+', k)
            if match:
                order = match.group(1)
        tem_list[k] = v
    elif (k.startswith("text_") or k.startswith("tmallSatisficationScore_") or
          k.startswith("recordOnionShow_") or k.startswith("structRate_") or
          k.startswith("tbGnbRate_") or k.startswith("ugcPublicAndPublishSKS_")):
        tem_list[k] = v

# 遍历临时列表 tem_list 的键值对
for k, v in tem_list.items():
    if k.startswith("mainOrderScore_") or k.startswith("tmallSatisficationScore_"):
        v["fields"]["darkMode"] = '0'
        v["fields"]["darkModel"] = '0'
        v["fields"]["orderId"] = order
        v["fields"]["rateStatus"] = 0
        v["fields"]["starValue"] = 5
        if order_length == 1:
            v["fields"]["groupIndex"] = k[-1]
            v["fields"]["groupsEnable"] = '1'
        v["fields"]["rateType"] = '0' if order_length == 1 else '1'
        submit_params["data"][k] = v
    elif k.startswith("recordOnionShow_"):
        v["fields"]["trackInfo"]["exposure"]["args"]["trackId"] = order
        if order_length == 1:
            v["fields"]["groupIndex"] = '0'
            v["fields"]["groupsEnable"] = '1'

        # 使用完整的图片数据结构
        image_url = "https://img.alicdn.com/imgextra/i2/O1CN01h5kdjE2LZrfYgO9aZ_!!4611686018427380907-0-rate.jpg"
        goods_id = "929187653500"  # 可以从订单信息中获取
        v["fields"]['images'] = [create_image_data(image_url, goods_id)]
        submit_params["data"][k] = v
    elif k.startswith("structRate_"):
        v["fields"]["content"] = comment_text
        tags = v["fields"].get("tags")
        if tags:
            v["fields"]["trackInfo"]["exposure"]["args"]["struct_tag_title"] = tags[0]["title"]
            v["fields"]["trackInfo"]["exposure"]["args"]["struct_tag_id"] = tags[0]["id"]
        if order_length == 1:
            v["fields"]["groupIndex"] = '0'
            v["fields"]["groupsEnable"] = '1'
        v["fields"]["tags"] = []
        submit_params["data"][k] = v
    elif k.startswith("tbGnbRate_"):
        v["fields"]["darkMode"] = '0'
        v["fields"]["darkModel"] = '0'
        v["fields"]["trackInfo"]["exposure"]["args"]["trackId"] = order
        v["fields"]["orderId"] = order
        v["fields"]["rateStatus"] = 0
        v["fields"]["selectedValue"] = "1"
        v["fields"]["rateType"] = '0' if order_length == 1 else '1'
        if order_length == 1:
            v["fields"]["groupIndex"] = '1'
            v["fields"]["groupsEnable"] = '1'
        submit_params["data"][k] = v
    elif k.startswith("ugcPublicAndPublishSKS_"):
        v["fields"]["isAnonymous"] = True
        if order_length == 1:
            v["fields"]["groupIndex"] = '0'
            v["fields"]["groupsEnable"] = '1'
        v["fields"]["darkMode"] = '0'
        v["fields"]["darkModel"] = '0'
        v["fields"]["orderId"] = order
        v["fields"]["rateStatus"] = 0
        v["fields"]["rateType"] = '0' if order_length == 1 else '1'
        submit_params["data"][k] = v
    elif k.startswith("text_"):
        v["fields"]["data"]["text"] = comment_text
        submit_params["data"][k] = v
    else:
        submit_params["data"][k] = v
print(submit_params)
final_params = {"submit": submit_params, "asac": "2A23A16I9YC7QUVMJ8EH2I"}
get_rate = to_comment(final_params, cookie)
print(get_rate)
