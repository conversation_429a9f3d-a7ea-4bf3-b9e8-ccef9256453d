# 淘宝商品下单管理系统

一个专为淘宝卖家设计的商品下单管理系统，支持批量下单、确认收货和订单数据导出功能。

## 功能特点

### 1. 多Chrome用户并行下单
- 支持选择多个Chrome用户配置文件同时处理下单任务
- 智能分配任务，提高下单效率
- 实时显示每个Chrome用户的处理状态和进度
- 估算任务完成的剩余时间

### 2. 商品下单
- 批量导入商品链接
- 随机打乱下单顺序，避免系统检测
- 自动填写收货地址和支付信息
- 自动处理验证码和安全验证

### 3. 确认收货
- 批量确认多个订单收货
- 自动模式支持定时扫描待确认订单
- 多账号同时处理，提高效率

### 4. 订单导出
- 按时间范围、订单状态等条件筛选订单
- 导出为Excel格式，方便后续处理
- 支持导出详细订单信息

## 技术栈
- 前端：HTML, CSS, JavaScript, Bootstrap
- 后端：Python, Flask
- 自动化：DrissionPage, Selenium
- 数据处理：Pandas

## 系统要求
- Python 3.7+
- Chrome浏览器
- Windows/Mac/Linux系统

## 安装与运行

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行系统
```bash
python app.py
```

## 使用指南

### 多线程下单功能
1. 在下单页面中，查看并选择可用的Chrome用户
2. 输入要下单的商品链接（每行一个）
3. 可选择是否随机打乱下单顺序
4. 点击"开始下单"按钮启动任务
5. 实时监控各Chrome用户的下单状态和进度
6. 任务卡片显示每个Chrome用户的处理详情

### 确认收货
1. 在确认收货页面，选择需要确认的订单
2. 选择使用的Chrome用户
3. 点击"开始确认"按钮启动确认任务
4. 或选择自动模式，系统会定期检查待确认订单

### 订单导出
1. 在订单导出页面，设置导出条件
2. 选择导出字段和格式
3. 点击"导出订单"按钮生成导出文件
4. 下载生成的Excel文件

## 注意事项
- 首次使用时，需要登录并保存Chrome用户配置
- 使用前请确保Chrome浏览器已安装并更新到最新版本
- 多线程下单功能会同时打开多个浏览器窗口，请确保计算机有足够资源

## 更新日志

### v1.0.0
- 首次发布，实现基本功能

### v1.1.0
- 增加多线程下单支持
- 优化用户界面
- 添加任务状态实时监控
- 增加随机排序功能

### v1.2.0
- 修复多线程下单时Chrome用户数据目录冲突问题
- 优化ChromeDriver版本兼容性处理
- 修复启动按钮多次触发问题
- 增加详细的技术文档

## 多线程下单技术实现

### 1. 多线程启动Chrome浏览器

系统能够同时启动多个Chrome浏览器实例，每个实例使用不同的用户配置文件，实现多账号并行下单。

#### 核心实现方法

```python
def open_chrome_with_profile(profile, headless=False, driver_path=None):
    """打开Chrome浏览器并加载指定的用户配置文件"""
    try:
        # 获取原始用户配置路径
        original_user_data_dir = profile['path']
        original_profile_dir = profile['profile_dir']

        # 创建临时用户数据目录，避免多实例冲突
        import tempfile
        import uuid
        import shutil

        # 为每个浏览器实例生成唯一标识
        instance_id = f"instance_{profile['id']}_{int(time.time())}_{uuid.uuid4().hex[:8]}"

        # 创建唯一的临时用户数据目录
        temp_dir = tempfile.mkdtemp(prefix=f"chrome_temp_{instance_id}_")

        # 配置Chrome选项
        options = Options()

        # 使用临时目录作为用户数据目录
        options.add_argument(f"user-data-dir={temp_dir}")

        # 复制必要的配置文件（如Cookies, Login Data等）
        source_profile_path = os.path.join(original_user_data_dir, original_profile_dir)
        important_files = ['Cookies', 'Login Data', 'Web Data', 'Preferences', 'History', 'Bookmarks']

        # 创建Default目录并复制文件
        default_dir = os.path.join(temp_dir, "Default")
        os.makedirs(default_dir, exist_ok=True)

        for file in important_files:
            source_file = os.path.join(source_profile_path, file)
            if os.path.exists(source_file):
                shutil.copy2(source_file, os.path.join(default_dir, file))

        # 添加其他必要的选项
        options.add_argument(f"--remote-debugging-port={random.randint(9222, 9999)}")
        options.add_argument("--no-first-run")
        options.add_argument("--no-default-browser-check")

        # 创建WebDriver
        driver = webdriver.Chrome(executable_path=driver_path, options=options)
        return driver
    except Exception as e:
        print(f"创建Chrome驱动失败: {e}")
        return None
```

### 2. 多线程任务分配

系统能够将多个下单链接分配给多个Chrome用户，实现并行处理。

```python
def start_order_task(self, profile_ids, order_urls, shuffle=False, multi_thread=False):
    """启动下单任务"""
    try:
        print(f"OrderManager.start_order_task - 收到参数 profile_ids: {profile_ids}, order_urls数量: {len(order_urls)}, 多线程模式: {multi_thread}")

        # 如果没有选择用户或没有订单链接，则返回错误
        if not profile_ids or not order_urls:
            return {"success": False, "message": "请选择至少一个Chrome用户并输入至少一个商品链接"}

        # 随机打乱订单链接顺序
        if shuffle:
            random.shuffle(order_urls)

        # 设置任务状态
        self.is_running = True
        self.total_tasks = len(order_urls)
        self.completed_tasks = 0

        # 分配任务给用户
        user_tasks = self._assign_tasks_to_users(profile_ids, order_urls)

        # 启动任务
        if multi_thread:
            # 多线程模式：为每个用户创建一个线程
            with ThreadPoolExecutor(max_workers=len(user_tasks)) as executor:
                for profile_id, urls in user_tasks.items():
                    executor.submit(self._process_order_task, profile_id, urls, multi_thread)
        else:
            # 单线程模式：顺序处理每个用户的任务
            for profile_id, urls in user_tasks.items():
                self._process_order_task(profile_id, urls, multi_thread)

        return {"success": True, "message": "任务已启动"}
    except Exception as e:
        print(f"启动任务失败: {e}")
        traceback.print_exc()
        self.is_running = False
        return {"success": False, "message": str(e)}
```

### 3. ChromeDriver版本兼容性处理

系统能够处理不同版本的ChromeDriver和Chrome浏览器之间的兼容性问题。

```python
def get_driver_path():
    """获取Chrome驱动路径，优先使用项目根目录下的chromedriver"""
    # 优先使用当前目录下的chromedriver
    current_dir = os.getcwd()
    current_driver_path = os.path.join(current_dir, 'chromedriver.exe' if os.name == 'nt' else 'chromedriver')

    if os.path.exists(current_driver_path):
        return current_driver_path

    # 然后使用项目根目录下的chromedriver
    root_dir = os.path.dirname(os.path.abspath(__file__))
    driver_path = os.path.join(root_dir, 'chromedriver.exe' if os.name == 'nt' else 'chromedriver')

    if os.path.exists(driver_path):
        return driver_path

    # 检查是否在PyInstaller环境中
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包环境
        pyinstaller_path = os.path.join(sys._MEIPASS, 'chromedriver.exe' if os.name == 'nt' else 'chromedriver')
        if os.path.exists(pyinstaller_path):
            return pyinstaller_path

    # 如果都没有找到，则使用系统PATH中的chromedriver
    return None
```

### 4. 启动Chrome用户配置文件

系统使用DrissionPage启动Chrome用户配置文件，这种方式比Selenium更稳定可靠：

```python
from DrissionPage import ChromiumOptions, ChromiumPage

# 创建配置对象
co = ChromiumOptions()

# 设置浏览器路径
co.set_browser_path(r"C:\Program Files\Google\Chrome\Application\chrome.exe")

# 设置用户数据目录和配置文件
co.set_user_data_path(r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data")
co.set_user("Profile 5")  # 指定用户配置文件

# 创建页面对象
page = ChromiumPage(co)

# 访问网页
page.get("https://www.example.com")
```

DrissionPage的优势：
1. 不依赖WebDriver，避免了驱动版本兼容性问题
2. 直接使用Chrome用户配置文件，无需复制临时文件
3. 更稳定可靠，不会出现"user data directory is already in use"错误
4. API更简洁，代码更易读
5. 执行速度更快

当DrissionPage方式失败时，系统会尝试使用命令行方式直接启动Chrome：

```python
# 构建命令行参数
cmd = [
    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
    f"--user-data-dir={user_data_dir}",
    f"--profile-directory={profile_dir}",
    "--remote-debugging-port=9222"
]

# 启动Chrome
subprocess.Popen(cmd)

# 连接到已启动的Chrome
page = ChromiumPage("127.0.0.1:9222")
```

这种方式可以确保即使出现问题，系统仍然能够正常启动Chrome浏览器。

## 故障排除

1. 如果遇到Chrome启动问题，系统会自动尝试使用DrissionPage或命令行方式启动Chrome，这两种方式都不依赖WebDriver，可以避免大多数启动问题。

2. 如果启动按钮点击后出现多次弹窗，可能是因为事件绑定重复。已通过将事件绑定移至HTML中的onclick属性来解决这个问题。

3. 如果页面加载CSS和JS文件出现问题，请检查网络连接，系统已经将Remixicon字体文件引用替换为国内CDN源，提高加载速度。