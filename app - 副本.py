import os
import sys
import json
import threading
import hashlib
import traceback
from urllib.parse import quote
import time
import webview
from flask import Flask, render_template, request, jsonify, send_from_directory
import requests
import time
import re
import urllib.parse

# 添加当前目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from chrome_manager import ChromeManager
from order_manager import Order<PERSON>anager
from cookie_manager import CookieManager
from server import create_server
from config_manager import config_manager


# 全局变量
chrome_manager = ChromeManager()
order_manager = OrderManager(chrome_manager)
cookie_manager = CookieManager(chrome_manager)
global_config = {}

# 创建Flask应用
app = Flask(__name__)
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # 设置静态文件缓存过期时间为0

# 线程锁
thread_lock = threading.Lock()
server = None

# JavaScript API类
class Api:
    def __init__(self):
        self.window = None
        self.order_manager = order_manager  # 添加对order_manager的引用

    def set_window(self, window):
        self.window = window

    def start_order_task(self, profile_ids, order_urls, shuffle=False, multi_thread=False):
        """启动下单任务"""
        print(f"Python API: start_order_task 被调用 - {profile_ids}, {len(order_urls)} 条链接")

        try:
            # 确保profile_ids是列表
            if not isinstance(profile_ids, list):
                profile_ids = [profile_ids]

            # 确保列表中的ID是字符串类型
            profile_ids = [str(pid) for pid in profile_ids]

            # 校验参数
            if not profile_ids:
                return {"success": False, "message": "请选择至少一个Chrome用户"}
            if not order_urls:
                return {"success": False, "message": "请输入至少一个订单链接"}

            # 如果需要随机顺序，则打乱订单链接
            if shuffle:
                import random
                random.shuffle(order_urls)

            # 启动下单任务
            result = order_manager.start_order_task(profile_ids, order_urls, multi_thread)
            return result
        except Exception as e:
            import traceback
            print(f"启动下单任务失败: {str(e)}")
            traceback.print_exc()
            return {"success": False, "message": f"启动任务失败: {str(e)}"}

    def stop_order_task(self):
        """停止下单任务"""
        print("Python API: stop_order_task 被调用")
        try:
            result = order_manager.stop_all_tasks()
            return result
        except Exception as e:
            return {"success": False, "message": f"停止任务失败: {str(e)}"}

    def get_order_status(self):
        """获取任务状态"""
        try:
            task_status = order_manager.get_task_status()
            return {"success": True, "data": task_status}
        except Exception as e:
            return {"success": False, "message": str(e)}

    def get_profiles(self):
        """获取所有Chrome用户配置"""
        profiles = chrome_manager.get_profiles()
        # 尝试获取每个用户的订单数量
        for profile in profiles:
            try:
                # 如果有cookie，尝试获取订单数量
                print(profile.get('cookie'))
                if profile.get('cookie'):
                    order_counts = self.get_profile_order_counts(profile['id'])
                    if order_counts.get('success'):
                        profile['order_counts'] = order_counts.get('data', {})
            except Exception as e:
                print(f"获取用户 {profile.get('name')} 的订单数量失败: {e}")

        return {"success": True, "profiles": profiles}

    def refresh_profiles(self):
        """刷新Chrome用户配置列表"""
        print("Python API: refresh_profiles 被调用")
        chrome_manager.refresh_profiles()
        profiles = chrome_manager.get_profiles()
        return {"success": True, "profiles": profiles}

    def get_profile_order_counts(self, profile_id):
        """获取指定用户的订单数量信息"""
        print(f"获取用户 {profile_id} 的订单数量")
        try:
            # 从配置文件中获取用户的cookie
            profile = None
            for p in chrome_manager.get_profiles():
                if p['id'] == profile_id:
                    profile = p
                    break

            if not profile or not profile.get('cookie'):
                return {"success": False, "message": "未找到用户cookie"}

            cookie = profile.get('cookie')

            # 检查cookie是否包含_m_h5_tk
            if '_m_h5_tk=' not in cookie:
                # 尝试获取token
                h5_token = goToken(cookie)
                if h5_token:
                    cookie = h5_token + cookie
                else:
                    return {"success": False, "message": "无法获取token"}

            # 准备请求参数
            t = str(int(time.time() * 1000))
            data = '{}'
            token = cookie.split('_m_h5_tk=')[1].split('_')[0]

            # 构建请求头
            headers = {
                'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
                'cookie': cookie
            }

            # 计算签名
            sign_text = token + '&' + t + '&' + '12574478' + '&' + data
            sign = hashlib.md5(sign_text.encode('utf-8')).hexdigest()

            # 构建URL
            api = 'mtop.order.taobao.countv2'
            v = '1.0'
            url = f'https://h5api.m.taobao.com/h5/{api}/{v}/?jsv=2.4.9&appKey=12574478&t={t}&sign={sign}&api={api}&v={v}&data={urllib.parse.quote(data)}'

            # 发送请求
            response = requests.get(url, headers=headers)
            result = response.json()

            # 检查是否登录过期
            if 'ret' in result and any('SESSION_EXPIRED' in ret or 'Session过期' in ret for ret in result['ret']):
                # 更新用户状态为账号失效
                chrome_manager.update_profile_status(profile_id, 'inactive')

                return {
                    "success": False,
                    "message": "登录已过期，请重新获取Cookie",
                    "session_expired": True
                }

            # 检查响应是否成功
            if 'data' not in result or 'result' not in result['data']:
                return {"success": False, "message": "接口返回数据格式错误", "raw_response": result}

            # 提取订单数量信息
            order_counts = {}
            for item in result['data']['result']:
                order_counts[item['tabCode']] = item['count']

            # 更新用户状态为正常
            chrome_manager.update_profile_status(profile_id, 'active')

            return {
                "success": True,
                "data": {
                    "waitPay": order_counts.get('waitPay', '0'),     # 待付款
                    "waitSend": order_counts.get('waitSend', '0'),   # 待发货
                    "waitConfirm": order_counts.get('waitConfirm', '0'), # 待收货
                    "waitRate": order_counts.get('waitRate', '0')    # 待评价
                }
            }
        except Exception as e:
            print(f"获取订单数量失败: {e}")
            traceback.print_exc()
            return {"success": False, "message": f"获取订单数量失败: {str(e)}"}

    def _export_taobao_orders(self, profile_ids, order_type='all', start_date=None, end_date=None):
        """导出淘宝订单（内部方法）"""
        print(f"_export_taobao_orders函数被调用: 用户={profile_ids}, 订单类型={order_type}, 日期范围={start_date}-{end_date}")
        try:
            # 导入订单导出模块
            from order_export import export_orders
            print("成功导入order_export模块")

            # 获取用户cookie
            profile_cookies = {}
            for profile_id in profile_ids:
                profile = config_manager.get_profile(profile_id)
                if profile and profile.get('cookie'):
                    profile_cookies[profile_id] = profile.get('cookie')
                    print(f"获取到用户 {profile_id} 的cookie")
                else:
                    print(f"警告: 未获取到用户 {profile_id} 的cookie")

            print(f"获取到 {len(profile_cookies)} 个用户的cookie")

            # 如果没有提供日期范围，则使用配置中的日期
            if not start_date or not end_date:
                config = config_manager.get_config()
                start_date = start_date or config.get("start_date", "")
                end_date = end_date or config.get("end_date", "")
                print(f"使用配置中的日期范围: {start_date} - {end_date}")
            else:
                print(f"使用传入的日期范围: {start_date} - {end_date}")

            # 创建回调函数，用于更新UI状态
            def update_status(message):
                if self.window:
                    try:
                        self.window.evaluate_js(f"updateExportStatus('{message}')")
                    except Exception as e:
                        print(f"更新UI状态失败: {e}")
                print(message)

            # 打印导出参数
            print(f"调用export_orders函数: profile_ids={profile_ids}, profile_cookies={len(profile_cookies)}, start_date={start_date}, end_date={end_date}, order_type={order_type}")

            # 导出订单
            result = export_orders(profile_ids, profile_cookies, start_date, end_date, order_type, update_status)

            print(f"export_orders函数返回结果: {result}")

            if result.get('success'):
                # 获取文件路径
                file_path = result.get('file_path')

                # 获取文件名
                filename = os.path.basename(file_path)

                # 确保下载目录存在
                downloads_dir = os.path.join(current_dir, "downloads")
                if not os.path.exists(downloads_dir):
                    os.makedirs(downloads_dir)

                # 复制文件到下载目录
                import shutil
                download_path = os.path.join(downloads_dir, filename)
                shutil.copy2(file_path, download_path)

                # 构建下载URL
                download_url = f'/downloads/{filename}'

                return {
                    "success": True,
                    "message": result.get('message'),
                    "file_url": download_url
                }
            else:
                return {
                    "success": False,
                    "message": result.get('message')
                }
        except Exception as e:
            print(f"导出淘宝订单失败: {e}")
            traceback.print_exc()
            return {"success": False, "message": f"导出失败: {str(e)}"}

    def get_waiting_receipt_orders(self):
        """获取等待确认收货的订单"""
        print("Python API: get_waiting_receipt_orders 被调用")
        try:
            orders = order_manager.get_waiting_receipt_orders()
            return {"success": True, "orders": orders}
        except Exception as e:
            import traceback
            print(f"获取等待确认收货的订单失败: {str(e)}")
            traceback.print_exc()
            return {"success": False, "message": f"获取订单失败: {str(e)}"}

    def start_receipt_task(self, profile_ids, order_ids=None, shuffle=False, auto_mode=False):
        """启动确认收货任务"""
        print(f"Python API: start_receipt_task 被调用 - {profile_ids}, 自动模式: {auto_mode}")

        try:
            # 确保profile_ids是列表
            if not isinstance(profile_ids, list):
                profile_ids = [profile_ids]

            # 确保列表中的ID是字符串类型
            profile_ids = [str(pid) for pid in profile_ids]

            # 校验参数
            if not profile_ids:
                return {"success": False, "message": "请选择至少一个Chrome用户"}

            # 自动模式不需要订单ID
            if not auto_mode and (not order_ids or len(order_ids) == 0):
                return {"success": False, "message": "请选择至少一个订单"}

            # 如果需要随机顺序，则打乱订单ID
            if shuffle and order_ids and len(order_ids) > 0:
                import random
                random.shuffle(order_ids)

            # 启动确认收货任务
            if auto_mode:
                result = order_manager.start_auto_receipt_task(profile_ids)
            else:
                result = order_manager.start_manual_receipt_task(profile_ids, order_ids)

            return result
        except Exception as e:
            import traceback
            print(f"启动确认收货任务失败: {str(e)}")
            traceback.print_exc()
            return {"success": False, "message": f"启动任务失败: {str(e)}"}

    def stop_receipt_task(self):
        """停止确认收货任务"""
        print("Python API: stop_receipt_task 被调用")
        try:
            result = order_manager.stop_receipt_task()
            return result
        except Exception as e:
            return {"success": False, "message": f"停止任务失败: {str(e)}"}

    def get_receipt_status(self):
        """获取确认收货任务状态"""
        try:
            task_status = order_manager.get_receipt_status()
            return {"success": True, "data": task_status}
        except Exception as e:
            return {"success": False, "message": str(e)}

    def get_config(self):
        """获取配置信息"""
        try:
            # 使用集中配置管理器获取配置
            config = config_manager.get_config()
            print(f"成功读取配置文件")
            return {"success": True, "config": config}
        except Exception as e:
            import traceback
            print(f"获取配置失败: {str(e)}")
            print(traceback.format_exc())
            return {"success": False, "message": str(e)}

    def save_config(self, config):
        """保存配置信息"""
        try:
            # 如果新配置中有profiles字段，确保每个profile包含所有必要的字段
            if 'profiles' in config and isinstance(config['profiles'], dict):
                for profile_id, profile_data in config['profiles'].items():
                    # 获取现有的profile配置
                    existing_profile = config_manager.get_profile(profile_id)

                    # 更新profile数据
                    existing_profile.update(profile_data)

                    # 确保chrome_user_name和profile_dir字段存在
                    if 'chrome_user_name' not in existing_profile:
                        existing_profile['chrome_user_name'] = ""
                    if 'profile_dir' not in existing_profile:
                        existing_profile['profile_dir'] = ""

                    # 更新到配置管理器
                    config_manager.update_profile(profile_id, existing_profile)

            # 更新其他配置项
            for key, value in config.items():
                if key != 'profiles':  # profiles已单独处理
                    config_manager.set_value(key, value)

            # 保存配置
            success = config_manager.save_config()
            if success:
                # 验证配置文件是否可以正确读取
                try:
                    # 获取配置文件路径
                    config_path = config_manager.get_config_path()
                    with open(config_path, 'r', encoding='utf-8') as f:
                        # 尝试读取配置文件
                        test_config = json.load(f)
                    return {"success": True, "message": "配置保存成功"}
                except Exception as e:
                    return {"success": False, "message": f"配置保存成功，但无法正确读取: {str(e)}"}
            else:
                return {"success": False, "message": "配置保存失败"}
        except Exception as e:
            import traceback
            return {"success": False, "message": str(e)}

    def start_auto_confirm(self, profile_ids, profile_passwords=None, multi_thread=True):
        """启动自动确认收货任务"""
        print(f"Python API: start_auto_confirm 被调用 - {profile_ids}, 多线程模式: {multi_thread}")

        try:
            # 确保profile_ids是列表
            if not isinstance(profile_ids, list):
                profile_ids = [profile_ids]

            # 确保列表中的ID是字符串类型
            profile_ids = [str(pid) for pid in profile_ids]

            # 校验参数
            if not profile_ids:
                return {"success": False, "message": "请选择至少一个Chrome用户"}

            # 获取最大线程数设置
            max_threads = 2  # 默认值

            # 尝试从配置文件中读取最大线程数
            try:
                config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.json')
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        # 确保max_threads是整数
                        max_threads_config = config.get('max_threads', 2)
                        if isinstance(max_threads_config, str):
                            max_threads = int(max_threads_config)
                        else:
                            max_threads = max_threads_config
            except Exception as e:
                print(f"读取配置文件失败: {e}")

            # 如果不使用多线程模式，则一次只启动一个浏览器
            if not multi_thread:
                max_threads = 1

            print(f"使用最大线程数: {max_threads}")

            # 启动自动确认收货任务
            result = order_manager.start_auto_receipt_task(profile_ids, max_threads)
            return result
        except Exception as e:
            import traceback
            print(f"启动自动确认收货任务失败: {str(e)}")
            traceback.print_exc()
            return {"success": False, "message": f"启动任务失败: {str(e)}"}

    def stop_auto_confirm(self):
        """停止自动确认收货任务"""
        print("Python API: stop_auto_confirm 被调用")
        try:
            result = order_manager.stop_receipt_task()
            return result
        except Exception as e:
            return {"success": False, "message": f"停止任务失败: {str(e)}"}

    def start_confirm(self, profile_ids, profile_passwords=None, multi_thread=False):
        """启动确认收货任务（使用独立脚本执行）"""
        print(f"Python API: start_confirm 被调用 - {profile_ids}, 多线程模式: {multi_thread}")

        try:
            # 确保profile_ids是列表
            if not isinstance(profile_ids, list):
                profile_ids = [profile_ids]

            # 确保列表中的ID是字符串类型
            profile_ids = [str(pid) for pid in profile_ids]

            # 校验参数
            if not profile_ids:
                return {"success": False, "message": "请选择至少一个Chrome用户"}

            # 设置最大线程数
            max_threads = 1
            if multi_thread:
                # 从配置文件中读取最大线程数
                config = config_manager.get_config()
                config_max_threads = config.get('max_threads', 3)  # 默认为3
                max_threads = min(len(profile_ids), config_max_threads)
            print(f"使用线程数: {max_threads}")

            # 创建临时任务配置文件
            import tempfile
            import json
            import os

            # 获取用户的详细配置信息
            profiles_config = {}
            for profile_id in profile_ids:
                # 获取用户配置
                profile = chrome_manager.get_profile_by_id(profile_id)
                if profile:
                    # 将用户配置添加到profiles_config中，不包含cookie
                    profiles_config[profile_id] = {
                        "name": profile.get("name", ""),
                        "chrome_user_name": profile.get("chrome_user_name", ""),
                        "profile_dir": profile.get("profile_dir", ""),
                        "pay_password": profile.get("pay_password", "")
                    }

            # 获取日期范围配置
            config = config_manager.get_config()
            start_date = config.get("start_date", "")
            end_date = config.get("end_date", "")

            # 创建任务配置
            task_file = os.path.join(tempfile.gettempdir(), "confirm_receipt_task.json")
            task_config = {
                "profile_ids": profile_ids,
                "max_threads": max_threads,
                "profiles": profiles_config,
                "start_date": start_date,
                "end_date": end_date,
                "chrome_path": config.get("chrome_path", ""),
                "auto_review_enabled": config.get("auto_review_enabled", False),
                "rate": config.get("rate", ["宝贝收到了，很满意，谢谢卖家！"])
            }

            with open(task_file, 'w', encoding='utf-8') as f:
                json.dump(task_config, f, ensure_ascii=False, indent=4)

            print(f"任务配置已保存到: {task_file}")

            # 检查task目录是否存在，如果不存在则创建
            import sys
            import platform
            import subprocess

            # 确定应用根目录
            if getattr(sys, 'frozen', False):
                # 打包环境
                app_root = os.path.dirname(sys.executable)
            else:
                # 开发环境
                app_root = os.path.dirname(os.path.abspath(__file__))

            # 确保task目录存在
            task_dir = os.path.join(app_root, "task")
            if not os.path.exists(task_dir):
                print(f"创建task目录: {task_dir}")
                os.makedirs(task_dir)

            # 确定确认收货程序路径
            if getattr(sys, 'frozen', False):
                # 打包环境 - 使用exe文件
                confirm_exe = os.path.join(task_dir, "confirm_receipt.exe")
                print(f"使用打包的确认收货程序: {confirm_exe}")
                cmd_prefix = f'"{confirm_exe}"'
            else:
                # 开发环境 - 使用Python脚本
                confirm_path = os.path.join(app_root, "confirm_receipt.py")
                print(f"使用开发环境确认收货脚本: {confirm_path}")
                cmd_prefix = f'python "{confirm_path}"'

            # 获取config.json的绝对路径
            config_path = config_manager.get_config_path()

            # 构建命令行，添加config_path参数
            cmd = f'{cmd_prefix} --task_file "{task_file}" --config_path "{config_path}"'

            # 启动子进程执行确认收货程序
            print(f"启动确认收货程序，命令: {cmd}")

            if platform.system() == "Windows":
                # Windows环境
                full_cmd = f'start "淘宝自动确认收货" {cmd}'
                subprocess.Popen(full_cmd, shell=True)
            else:
                # Linux/Mac环境
                subprocess.Popen(cmd, shell=True)

            print(f"确认收货脚本已启动，命令: {cmd}")

            return {"success": True, "message": "确认收货任务已启动"}

        except Exception as e:
            import traceback
            print(f"启动自动确认收货任务失败: {str(e)}")
            traceback.print_exc()
            return {"success": False, "message": f"启动任务失败: {str(e)}"}

    def stop_confirm(self):
        """停止确认收货任务（新方法，与stop_auto_confirm功能相同，但名称更符合前端调用）"""
        print("Python API: stop_confirm 被调用")
        return self.stop_auto_confirm()

    def get_cookies(self, profile_ids):
        """获取指定用户的Cookie"""
        print(f"Python API: get_cookies 被调用 - {profile_ids}")

        try:
            # 确保profile_ids是列表
            if not isinstance(profile_ids, list):
                profile_ids = [profile_ids]

            # 确保列表中的ID是字符串类型
            profile_ids = [str(pid) for pid in profile_ids]

            # 校验参数
            if not profile_ids:
                return {"success": False, "message": "请选择至少一个Chrome用户"}

            # 设置CookieManager的窗口引用
            cookie_manager.set_window(self.window)

            # 先结束所有Chrome进程
            print("获取Cookie前，先结束所有Chrome进程...")
            # 尝试调用cookie_manager的_kill_chrome_processes方法
            if hasattr(cookie_manager, '_kill_chrome_processes'):
                cookie_manager._kill_chrome_processes()
                time.sleep(2)  # 等待进程完全结束
                print("所有Chrome进程已结束，准备获取Cookie")

            # 使用单线程模式获取Cookie
            result = cookie_manager.get_cookies(profile_ids, 1)  # 强制使用单线程
            return result
        except Exception as e:
            import traceback
            print(f"启动Cookie获取任务失败: {str(e)}")
            traceback.print_exc()
            return {"success": False, "message": f"启动任务失败: {str(e)}"}

    def stop_cookies(self):
        """停止Cookie获取任务"""
        print("Python API: stop_cookies 被调用")
        try:
            result = cookie_manager.stop_cookies()
            return result
        except Exception as e:
            return {"success": False, "message": f"停止任务失败: {str(e)}"}

    def kill_all_processes(self):
        """结束所有Chrome进程和confirm_receipt.exe进程"""
        print("Python API: kill_all_processes 被调用")
        try:
            import subprocess
            import platform

            # 结束所有Chrome进程
            if platform.system() == "Windows":
                # Windows环境
                subprocess.run("taskkill /f /im chrome.exe", shell=True)
                subprocess.run("taskkill /f /im confirm_receipt.exe", shell=True)
                print("已结束所有Chrome进程和confirm_receipt.exe进程")
            else:
                # Linux/Mac环境
                subprocess.run("pkill -9 chrome", shell=True)
                subprocess.run("pkill -9 confirm_receipt", shell=True)
                print("已结束所有Chrome进程和confirm_receipt进程")

            return {"success": True, "message": "已结束所有相关进程"}
        except Exception as e:
            import traceback
            print(f"结束进程失败: {str(e)}")
            traceback.print_exc()
            return {"success": False, "message": f"结束进程失败: {str(e)}"}

    def export_taobao_orders(self, profile_ids, order_type='all'):
        """导出淘宝订单

        Args:
            profile_ids: 选中的用户ID列表
            order_type: 订单类型，可选值：all, waitSend, waitConfirm, waitPay, waitRate

        Returns:
            dict: 包含成功状态和结果信息的字典
        """
        print(f"Python API: export_taobao_orders 被调用 - 用户: {profile_ids}, 订单类型: {order_type}")

        try:
            # 确保profile_ids是列表
            if not isinstance(profile_ids, list):
                profile_ids = [profile_ids]

            # 确保列表中的ID是字符串类型
            profile_ids = [str(pid) for pid in profile_ids]

            # 校验参数
            if not profile_ids:
                return {"success": False, "message": "请选择至少一个Chrome用户"}

            # 获取配置信息
            config = config_manager.get_config()
            start_date = config.get("start_date", "")
            end_date = config.get("end_date", "")

            # 获取用户的cookie
            profile_cookies = {}
            for profile_id in profile_ids:
                profile = config_manager.get_profile(profile_id)
                if profile and profile.get("cookie"):
                    profile_cookies[profile_id] = profile.get("cookie")

            if not profile_cookies:
                return {"success": False, "message": "所选用户均无有效Cookie，请先获取Cookie"}

            # 定义状态更新回调函数
            def update_status(status_message):
                if self.window:
                    try:
                        self.window.evaluate_js(f"updateExportStatus('{status_message}')")
                    except Exception as e:
                        print(f"更新UI状态失败: {e}")

            # 调用导出函数
            result = export_orders(profile_ids, profile_cookies, start_date, end_date, order_type, update_status)

            if result["success"]:
                # 创建下载链接
                file_path = result["file_path"]
                file_name = os.path.basename(file_path)

                # 确保下载目录存在
                downloads_dir = os.path.join(current_dir, "downloads")
                if not os.path.exists(downloads_dir):
                    os.makedirs(downloads_dir)

                # 复制文件到下载目录
                import shutil
                download_path = os.path.join(downloads_dir, file_name)
                shutil.copy2(file_path, download_path)

                # 返回下载链接
                download_url = f"/downloads/{file_name}"
                return {
                    "success": True,
                    "message": result["message"],
                    "file_url": download_url
                }
            else:
                return result

        except Exception as e:
            import traceback
            print(f"导出订单失败: {str(e)}")
            traceback.print_exc()
            return {"success": False, "message": f"导出订单失败: {str(e)}"}

    def start_order_task_with_config(self, task_config):
        """使用配置启动自动下单任务"""
        print(f"Python API: start_order_task_with_config 被调用 - 配置: {task_config}")

        try:
            # 导入必要的模块
            import sys
            import platform
            import subprocess

            # 获取用户ID列表
            profile_ids = task_config.get('profileIds', [])

            # 确保profile_ids是列表
            if not isinstance(profile_ids, list):
                profile_ids = [profile_ids]

            # 确保列表中的ID是字符串类型
            profile_ids = [str(pid) for pid in profile_ids]

            # 校验参数
            if not profile_ids:
                return {"success": False, "message": "请选择至少一个Chrome用户"}

            # 获取每个用户的下单链接
            profile_order_urls = task_config.get('profileOrderUrls', {})

            if not profile_order_urls:
                return {"success": False, "message": "请提供至少一个商品链接"}

            # 创建任务配置
            order_task_config = {
                "operation": "order",  # 指定操作类型为下单
                "profile_ids": profile_ids,
                "profile_order_urls": profile_order_urls,
                "shuffle": task_config.get('shuffle', False),
                "multi_thread": task_config.get('multiThread', False),
                "max_threads": task_config.get('maxThreads', 1),
                "auto_payment": task_config.get('autoPayment', False)  # 添加自动付款选项
            }

            # 保存任务配置到临时文件
            task_file = self._save_order_task_config(order_task_config)

            # 启动confirm_receipt.py执行下单任务
            return self._launch_order_task(task_file)

        except Exception as e:
            import traceback
            print(f"启动自动下单任务失败: {str(e)}")
            traceback.print_exc()
            return {"success": False, "message": f"启动任务失败: {str(e)}"}

    def _save_order_task_config(self, task_config):
        """保存下单任务配置到文件"""
        import os
        import json
        import time
        import tempfile

        # 使用系统临时目录
        temp_dir = tempfile.gettempdir()

        # 保存任务配置到临时文件
        task_file = os.path.join(temp_dir, f"order_task_{int(time.time())}.json")
        with open(task_file, 'w', encoding='utf-8') as f:
            json.dump(task_config, f, ensure_ascii=False, indent=4)

        print(f"下单任务配置已保存到临时目录: {task_file}")
        return task_file

    def _launch_order_task(self, task_file):
        """启动confirm_receipt.py执行下单任务"""
        import os
        import sys
        import platform
        import subprocess

        # 确定应用根目录
        if getattr(sys, 'frozen', False):
            # 打包环境
            app_root = os.path.dirname(sys.executable)
        else:
            # 开发环境
            app_root = os.path.dirname(os.path.abspath(__file__))

        # 确保task目录存在
        task_dir = os.path.join(app_root, "task")
        if not os.path.exists(task_dir):
            print(f"创建task目录: {task_dir}")
            os.makedirs(task_dir)

        # 确定脚本路径
        if getattr(sys, 'frozen', False):
            # 打包环境 - 使用exe文件
            script_exe = os.path.join(task_dir, "confirm_receipt.exe")
            print(f"使用打包的确认收货程序: {script_exe}")
            cmd_prefix = f'"{script_exe}"'
        else:
            # 开发环境 - 使用Python脚本
            script_path = os.path.join(app_root, "confirm_receipt.py")
            print(f"使用开发环境确认收货脚本: {script_path}")
            cmd_prefix = f'python "{script_path}"'

        # 获取config.json的绝对路径
        config_path = config_manager.get_config_path()

        # 构建命令行，添加config_path参数和操作类型参数
        cmd = f'{cmd_prefix} --task_file "{task_file}" --config_path "{config_path}" --operation order'

        # 启动子进程执行脚本
        print(f"启动下单程序，命令: {cmd}")

        if platform.system() == "Windows":
            # Windows环境
            full_cmd = f'start "淘宝自动下单" {cmd}'
            subprocess.Popen(full_cmd, shell=True)
        else:
            # Linux/Mac环境
            subprocess.Popen(cmd, shell=True)

        print(f"下单脚本已启动，命令: {cmd}")

        return {"success": True, "message": "下单任务已启动"}

    def start_order_task(self, profile_ids, order_urls, shuffle=False, multi_thread=False, max_threads=1):
        """启动自动下单任务（兼容旧版本）"""
        print(f"Python API: start_order_task 被调用 - 用户: {profile_ids}, 链接数: {len(order_urls)}")

        try:
            # 导入必要的模块
            import sys
            import platform
            import subprocess

            # 确保profile_ids是列表
            if not isinstance(profile_ids, list):
                profile_ids = [profile_ids]

            # 确保列表中的ID是字符串类型
            profile_ids = [str(pid) for pid in profile_ids]

            # 校验参数
            if not profile_ids:
                return {"success": False, "message": "请选择至少一个Chrome用户"}

            if not order_urls:
                return {"success": False, "message": "请提供至少一个商品链接"}

            # 创建任务配置文件
            task_config = {
                "operation": "order",  # 指定操作类型为下单
                "profile_ids": profile_ids,
                "order_urls": order_urls,
                "shuffle": shuffle,
                "multi_thread": multi_thread,
                "max_threads": max_threads
            }

            # 使用系统临时目录
            import tempfile
            temp_dir = tempfile.gettempdir()

            # 保存任务配置到临时文件
            task_file = os.path.join(temp_dir, f"order_task_{int(time.time())}.json")
            with open(task_file, 'w', encoding='utf-8') as f:
                json.dump(task_config, f, ensure_ascii=False, indent=4)

            print(f"下单任务配置已保存到临时目录: {task_file}")

            # 确定应用根目录
            if getattr(sys, 'frozen', False):
                # 打包环境
                app_root = os.path.dirname(sys.executable)
            else:
                # 开发环境
                app_root = os.path.dirname(os.path.abspath(__file__))

            # 确保task目录存在
            task_dir = os.path.join(app_root, "task")
            if not os.path.exists(task_dir):
                print(f"创建task目录: {task_dir}")
                os.makedirs(task_dir)

            # 确定脚本路径
            if getattr(sys, 'frozen', False):
                # 打包环境 - 使用exe文件
                script_exe = os.path.join(task_dir, "confirm_receipt.exe")
                print(f"使用打包的确认收货程序: {script_exe}")
                cmd_prefix = f'"{script_exe}"'
            else:
                # 开发环境 - 使用Python脚本
                script_path = os.path.join(app_root, "confirm_receipt.py")
                print(f"使用开发环境确认收货脚本: {script_path}")
                cmd_prefix = f'python "{script_path}"'

            # 获取config.json的绝对路径
            config_path = config_manager.get_config_path()

            # 构建命令行，添加config_path参数和操作类型参数
            cmd = f'{cmd_prefix} --task_file "{task_file}" --config_path "{config_path}" --operation order'

            # 启动子进程执行脚本
            print(f"启动下单程序，命令: {cmd}")

            if platform.system() == "Windows":
                # Windows环境
                full_cmd = f'start "淘宝自动下单" {cmd}'
                subprocess.Popen(full_cmd, shell=True)
            else:
                # Linux/Mac环境
                subprocess.Popen(cmd, shell=True)

            print(f"下单脚本已启动，命令: {cmd}")

            return {"success": True, "message": "下单任务已启动"}

        except Exception as e:
            import traceback
            print(f"启动自动下单任务失败: {str(e)}")
            traceback.print_exc()
            return {"success": False, "message": f"启动任务失败: {str(e)}"}

# 加载配置
def load_config():
    global global_config
    try:
        # 使用集中配置管理器加载配置
        global_config = config_manager.get_config()
        print(f"已加载配置文件: {config_manager.get_config_path()}")
    except Exception as e:
        print(f"加载配置失败: {str(e)}")
        print("将使用默认配置继续运行")
        global_config = {
            "chrome_driver_path": "",
            "start_date": "",
            "end_date": "",
            "interval": "",
            "max_threads": 1,
            "auto_review_enabled": False,
            "profiles": {}
        }

# 保存配置
def save_config():
    try:
        # 使用集中配置管理器保存配置
        config_manager.update_config(global_config)
        success = config_manager.save_config()
        if success:
            print(f"配置保存成功: {config_manager.get_config_path()}")

            # 验证配置文件是否可以正确读取
            try:
                # 获取配置文件路径
                config_path = config_manager.get_config_path()
                with open(config_path, 'r', encoding='utf-8') as f:
                    # 尝试读取配置文件
                    _ = json.load(f)
                print(f"配置文件验证成功，可以正确读取")
            except Exception as e:
                print(f"警告：配置文件保存后无法正确读取: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"配置保存失败")
        return success
    except Exception as e:
        print(f"保存配置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
def goToken(cookie):
    """获取淘宝API所需的token"""

    t = str(int(time.time()))
    date = '{}'
    xapi = 'mtop.tmall.kangaroo.core.service.route.aldlampservicefixedresv2'
    xv = '1.0'
    token = ''
    str1 = token + '&' + t + '&12574478&' + date
    str2 = bytes(str1, encoding='utf-8')  # md5
    sign = hashlib.md5(str2).hexdigest()
    data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=2019@weex_h5_0.12.14&data=' + str(
        quote(date, 'utf-8'))
    url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
    head = {
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
        'cookie': cookie,
        'url': url2
    }

    r = requests.get(url2, timeout=20, headers=head, stream=False, verify=False)
    if r.text.find('为空') != -1 or r.text.find('过期') != -1:
        set_cookie = str(r.headers.get('Set-Cookie'))
        mk = '_m_h5_tk=' + set_cookie.split('_m_h5_tk=')[1].split(';')[0] + ';'
        enc = '_m_h5_tk_enc=' + set_cookie.split('_m_h5_tk_enc=')[1].split(';')[0] + ';'
        return mk + enc


# 添加获取订单数量的路由
@app.route('/api/order_counts', methods=['POST'])
def get_order_counts():
    """获取用户的订单数量信息"""
    try:
        # 获取请求参数
        data = request.json
        profile_id = data.get('profile_id')

        if not profile_id:
            return jsonify({"success": False, "message": "缺少profile_id参数"})

        # 从配置文件中获取用户的cookie
        profile = config_manager.get_profile(profile_id)
        if not profile or not profile.get('cookie'):
            return jsonify({"success": False, "message": "未找到用户cookie"})

        clean_cookie = profile.get('cookie', '')
        # 处理cookie，删除令牌部分
        cookie = clean_cookie
        # 删除_m_h5_tk部分
        if "_m_h5_tk=" in clean_cookie:
            cookie = re.sub(r'_m_h5_tk=[^;]+;', '', clean_cookie)
        # 删除_m_h5_tk_enc部分
        if "_m_h5_tk_enc=" in clean_cookie:
            cookie = re.sub(r'_m_h5_tk_enc=[^;]+;', '', clean_cookie)
        h5 = goToken(cookie)
        cookie = h5 + cookie
        # 构建请求头
        headers = {
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
            'cookie': cookie
        }

        # 请求mtop.order.taobao.countv2接口
        # 获取当前时间戳
        t = str(int(time.time() * 1000))

        # 构建请求参数
        data = '{}'
        token = cookie.split('_m_h5_tk=')[1].split(';')[0].split('_')[0] if '_m_h5_tk=' in cookie else ''

        if not token:
            return jsonify({"success": False, "message": "cookie中未找到token"})

        # 计算签名
        sign_text = token + '&' + t + '&' + '12574478' + '&' + data
        sign = hashlib.md5(sign_text.encode('utf-8')).hexdigest()

        # 构建URL
        api = 'mtop.order.taobao.countv2'
        v = '1.0'
        url = f'https://h5api.m.taobao.com/h5/{api}/{v}/?jsv=2.4.9&appKey=12574478&t={t}&sign={sign}&api={api}&v={v}&data={urllib.parse.quote(data)}'

        # 发送请求
        response = requests.get(url, headers=headers)
        print(response.text)
        # 解析响应
        result = response.json()

        # 检查是否登录过期
        if 'ret' in result and any('SESSION_EXPIRED' in ret or 'Session过期' in ret for ret in result['ret']):
            # 更新用户状态为账号失效
            profile['status'] = '账号失效'
            config_manager.update_profile(profile_id, profile)
            config_manager.save_config()

            return jsonify({
                "success": False,
                "message": "登录已过期，请重新获取Cookie",
                "session_expired": True
            })

        # 检查响应是否成功
        if 'data' not in result or 'result' not in result['data']:
            return jsonify({"success": False, "message": "接口返回数据格式错误", "raw_response": result})

        # 提取订单数量信息
        order_counts = {}
        for item in result['data']['result']:
            order_counts[item['tabCode']] = item['count']

        # 更新用户状态为正常
        if profile.get('status') != '正常':
            profile['status'] = '正常'
            config_manager.update_profile(profile_id, profile)
            config_manager.save_config()

        return jsonify({
            "success": True,
            "data": {
                "waitPay": order_counts.get('waitPay', '0'),     # 待付款
                "waitSend": order_counts.get('waitSend', '0'),   # 待发货
                "waitConfirm": order_counts.get('waitConfirm', '0'), # 待收货
                "waitRate": order_counts.get('waitRate', '0')    # 待评价
            }
        })
    except Exception as e:
        import traceback
        print(f"获取订单数量失败: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "message": f"获取订单数量失败: {str(e)}"})



@app.route('/api/cancel_orders', methods=['POST'])
def cancel_orders():
    """取消用户的待付款订单"""
    print('取消用户的待付款订单')
    try:
        # 获取请求参数
        data = request.json
        profile_ids = data.get('profile_ids', [])

        if not profile_ids:
            return jsonify({"success": False, "message": "请选择至少一个用户"})

        # 确保profile_ids是列表
        if not isinstance(profile_ids, list):
            profile_ids = [profile_ids]

        results = []
        success_count = 0
        fail_count = 0

        # 处理每个用户
        for profile_id in profile_ids:
            # 从配置文件中获取用户的cookie
            profile = config_manager.get_profile(profile_id)
            if not profile or not profile.get('cookie'):
                results.append({
                    "profile_id": profile_id,
                    "success": False,
                    "message": "未找到用户cookie"
                })
                fail_count += 1
                continue

            # 获取用户名称
            profile_name = profile.get('name', profile_id)

            # 获取待付款订单列表
            wait_pay_orders = get_wait_pay_orders(profile)
            if not wait_pay_orders:
                results.append({
                    "profile_id": profile_id,
                    "profile_name": profile_name,
                    "success": True,
                    "message": "没有待付款订单"
                })
                success_count += 1
                continue

            # 取消每个待付款订单
            order_results = []
            for order in wait_pay_orders:
                order_id = order.get('orderId')
                if not order_id:
                    continue

                # 调用取消订单API
                cancel_result = cancel_order(profile, order_id)
                order_results.append({
                    "order_id": order_id,
                    "success": cancel_result.get('success', False),
                    "message": cancel_result.get('message', '')
                })

                if cancel_result.get('success', False):
                    success_count += 1
                else:
                    fail_count += 1

            results.append({
                "profile_id": profile_id,
                "profile_name": profile_name,
                "success": True,
                "orders": order_results
            })

        return jsonify({
            "success": True,
            "message": f"处理完成: {success_count}个成功, {fail_count}个失败",
            "results": results
        })
    except Exception as e:
        import traceback
        print(f"取消待付款订单失败: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "message": f"取消待付款订单失败: {str(e)}"})

def get_wait_pay_orders(profile):
    """获取用户的待付款订单列表"""
    try:
        clean_cookie = profile.get('cookie', '')
        # 处理cookie，删除令牌部分
        cookie = clean_cookie
        # 删除_m_h5_tk部分
        if "_m_h5_tk=" in clean_cookie:
            cookie = re.sub(r'_m_h5_tk=[^;]+;', '', clean_cookie)
        # 删除_m_h5_tk_enc部分
        if "_m_h5_tk_enc=" in clean_cookie:
            cookie = re.sub(r'_m_h5_tk_enc=[^;]+;', '', clean_cookie)
        h5 = goToken(cookie)
        cookie = h5 + cookie

        # 构建请求头
        headers = {
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
            'cookie': cookie
        }

        # 获取待付款订单列表
        orders = []
        for i in range(1):  # 只获取第一页
            t = str(int(time.time()))
            date = '{"OrderType":"OrderList","appName":"tborder","appVersion":"3.0","condition":"{\\"categoryText\\":[null],\\"filterSelectInfo\\":{\\"isFilterResult\\":true,\\"selectedCategoryIndex\\":\\"-1\\",\\"selectedEndTime\\":\\"\\",\\"selectedEndTimeIndex\\":\\"1\\",\\"selectedGiftIndex\\":\\"-1\\",\\"selectedSourceIndex\\":\\"-1\\",\\"selectedStartTime\\":\\"\\",\\"selectedStartTimeIndex\\":\\"1\\",\\"selectedTimeIndex\\":\\"-1\\"},\\"onlyGiftFilter\\":\\"false\\",\\"orderFilterExtParam\\":{\\"beginTime\\":\\"\\",\\"endTime\\":\\"\\",\\"filterSource\\":\\"\\",\\"giftType\\":\\"\\"},\\"version\\":\\"1.0.0\\",\\"wordType\\":\\"3\\"}","page":"' + str(i) + '","tabCode":"waitPay","templateConfigVersion":"0"}'
            xapi = 'mtop.taobao.order.queryboughtlistv2'
            xv = '1.0'
            token = cookie.split('_m_h5_tk=')[1].split(';')[0].split('_')[0]
            str1 = token + '&' + t + '&12574478&' + date
            str2 = bytes(str1, encoding='utf-8')  # md5
            sign = hashlib.md5(str2).hexdigest()
            data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&type=json&ttid=700170@taobao_android_10.27.10&dataType=json&data=' + str(
                quote(date, 'utf-8'))
            url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2

            # 发送请求
            r = requests.get(url2, timeout=20, headers=headers, stream=False, verify=False)
            result = r.json()
            # 提取订单信息
            if 'data' in result and 'data' in result['data']:
                for key, value in result['data']['data'].items():
                    if key.startswith("item_"):
                        order_id = value.get('fields', {}).get('basicInfo', {}).get('orderId')
                        if order_id:
                            orders.append({
                                'orderId': order_id,
                                'title': value.get('fields', {}).get('item', {}).get('title', ''),
                                'status': value.get('fields', {}).get('queryParams', {}).get('status', '')
                            })

        return orders
    except Exception as e:
        print(f"获取待付款订单失败: {e}")
        traceback.print_exc()
        return []

def cancel_order(profile, order_id):
    """取消指定的订单"""
    try:
        clean_cookie = profile.get('cookie', '')
        # 处理cookie，删除令牌部分
        cookie = clean_cookie
        # 删除_m_h5_tk部分
        if "_m_h5_tk=" in clean_cookie:
            cookie = re.sub(r'_m_h5_tk=[^;]+;', '', clean_cookie)
        # 删除_m_h5_tk_enc部分
        if "_m_h5_tk_enc=" in clean_cookie:
            cookie = re.sub(r'_m_h5_tk_enc=[^;]+;', '', clean_cookie)
        h5 = goToken(cookie)
        cookie = h5 + cookie

        # 构建请求头
        headers = {
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
            'cookie': cookie
        }

        # 构建取消订单请求
        t = str(int(time.time()))
        date = '{"orderId":"' + str(order_id) + '","code":"cancelOrder","map":"{\\"reasonId\\":\\"1\\"}","ttid":"201200@taobao_h5_9.18.0","requestIdentity":"1@chrome_windows_131.0.0.0#pc","condition":"{\\"version\\":\\"1.0.0\\"}","extParams":"{\\"useNewDetail\\":true}"}'
        xapi = 'mtop.order.doOp'
        xv = '3.0'
        token = cookie.split('_m_h5_tk=')[1].split(';')[0].split('_')[0]
        str1 = token + '&' + t + '&12574478&' + date
        str2 = bytes(str1, encoding='utf-8')  # md5
        sign = hashlib.md5(str2).hexdigest()
        data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&type=json&ttid=201200@taobao_h5_9.18.0&dataType=json&data=' + str(
            quote(date, 'utf-8'))
        url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2

        # 发送请求
        r = requests.get(url2, timeout=20, headers=headers, stream=False, verify=False)
        result = r.json()
        print(result)
        # 检查响应
        if 'ret' in result and 'SUCCESS::调用成功' in result['ret'][0]:
            return {
                "success": True,
                "message": "订单取消成功"
            }
        else:
            error_msg = result.get('ret', ['未知错误'])[0]
            return {
                "success": False,
                "message": f"订单取消失败: {error_msg}"
            }
    except Exception as e:
        print(f"取消订单失败: {e}")
        traceback.print_exc()
        return {
            "success": False,
            "message": f"取消订单失败: {str(e)}"
        }

# 启动Flask服务器
def start_flask_server():
    global server
    server = create_server(chrome_manager, order_manager, app)
    # 设置cookie_manager
    if hasattr(server, 'cookie_manager'):
        server.cookie_manager = cookie_manager
    server.start(host='127.0.0.1', port=8060, debug=False)

# 主函数
def main():
    try:
        # 加载配置
        load_config()

        # 检测Chrome用户
        chrome_manager.refresh_profiles()

        # 创建下载目录（如果不存在）
        downloads_dir = os.path.join(current_dir, "downloads")
        if not os.path.exists(downloads_dir):
            os.makedirs(downloads_dir)

        # 创建API实例
        api = Api()

        # 启动Flask服务器
        threading.Thread(target=start_flask_server, daemon=True).start()

        # 等待Flask服务器启动
        time.sleep(1)

        # 创建PyWebView窗口
        window = webview.create_window(
            title='淘宝商品下单管理系统',
            url='http://127.0.0.1:8060/',  # 默认打开首页，使用8060端口
            width=1200,
            height=800,
            resizable=True,
            text_select=True,
            confirm_close=True,
            maximized=True,  # 设置窗口默认最大化
            js_api=api  # 使用js_api参数暴露API对象
        )

        # 设置API的窗口引用
        api.set_window(window)

        # 启动PyWebView
        webview.start(debug=False)  # 关闭调试模式，不显示开发者工具

    except Exception as e:
        print(f"系统启动失败: {str(e)}")
        input("按任意键退出...")

if __name__ == "__main__":
    main()