#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
异步任务处理模块
处理取消待付款订单和刷新用户列表等需要异步执行的任务
"""

import threading
import time
import json
import traceback
from concurrent.futures import ThreadPoolExecutor
import hashlib
import re
import requests
from urllib.parse import quote

# 全局任务管理器实例
task_manager = None

class AsyncTaskManager:
    """异步任务管理器，处理后台任务执行"""
    
    def __init__(self, max_workers=5):
        """初始化任务管理器
        
        Args:
            max_workers: 最大工作线程数
        """
        self.tasks = {}  # 存储任务状态
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.lock = threading.Lock()  # 用于线程安全操作
        
    def start_task(self, task_type, task_func, callback=None, *args, **kwargs):
        """启动异步任务
        
        Args:
            task_type: 任务类型标识
            task_func: 要执行的函数
            callback: 任务完成后的回调函数
            *args, **kwargs: 传递给task_func的参数
            
        Returns:
            str: 任务ID
        """
        task_id = f"{task_type}_{int(time.time())}"
        
        with self.lock:
            self.tasks[task_id] = {
                "id": task_id,
                "type": task_type,
                "status": "pending",
                "progress": 0,
                "result": None,
                "error": None,
                "start_time": time.time(),
                "update_time": time.time()
            }
        
        # 提交任务到线程池
        self.executor.submit(
            self._run_task, 
            task_id, 
            task_func, 
            callback,
            *args, 
            **kwargs
        )
        
        return task_id
    
    def _run_task(self, task_id, task_func, callback, *args, **kwargs):
        """在线程中执行任务
        
        Args:
            task_id: 任务ID
            task_func: 要执行的函数
            callback: 完成后的回调函数
            *args, **kwargs: 传递给task_func的参数
        """
        try:
            # 更新任务状态为运行中
            self.update_task_status(task_id, "running")
            
            # 执行任务函数
            result = task_func(
                progress_callback=lambda p, m=None: self.update_task_progress(task_id, p, m),
                *args, 
                **kwargs
            )
            
            # 更新任务结果和状态
            self.update_task_status(task_id, "completed", result=result)
            
            # 执行回调函数
            if callback:
                callback(task_id, result)
                
        except Exception as e:
            # 记录错误信息
            error_msg = str(e)
            error_trace = traceback.format_exc()
            
            print(f"任务 {task_id} 执行出错: {error_msg}")
            print(error_trace)
            
            # 更新任务状态为失败
            self.update_task_status(
                task_id, 
                "failed", 
                error=error_msg,
                error_trace=error_trace
            )
    
    def update_task_status(self, task_id, status, **kwargs):
        """更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            **kwargs: 其他要更新的字段
        """
        with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id]["status"] = status
                self.tasks[task_id]["update_time"] = time.time()
                
                # 更新其他字段
                for key, value in kwargs.items():
                    self.tasks[task_id][key] = value
    
    def update_task_progress(self, task_id, progress, message=None):
        """更新任务进度
        
        Args:
            task_id: 任务ID
            progress: 进度百分比 (0-100)
            message: 可选的进度消息
        """
        with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id]["progress"] = progress
                self.tasks[task_id]["update_time"] = time.time()
                
                if message:
                    self.tasks[task_id]["message"] = message
    
    def get_task_status(self, task_id):
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            dict: 任务状态信息，如果任务不存在则返回None
        """
        with self.lock:
            return self.tasks.get(task_id, None)
    
    def get_recent_tasks(self, task_type=None, limit=10):
        """获取最近的任务
        
        Args:
            task_type: 可选的任务类型过滤
            limit: 返回的最大任务数
            
        Returns:
            list: 任务状态列表
        """
        with self.lock:
            # 按更新时间排序
            sorted_tasks = sorted(
                self.tasks.values(),
                key=lambda t: t.get("update_time", 0),
                reverse=True
            )
            
            # 过滤任务类型
            if task_type:
                sorted_tasks = [t for t in sorted_tasks if t.get("type") == task_type]
            
            # 限制返回数量
            return sorted_tasks[:limit]
    
    def clean_old_tasks(self, max_age_hours=24):
        """清理旧任务
        
        Args:
            max_age_hours: 任务最大保留时间(小时)
        """
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        with self.lock:
            task_ids = list(self.tasks.keys())
            for task_id in task_ids:
                task = self.tasks[task_id]
                if current_time - task.get("update_time", 0) > max_age_seconds:
                    del self.tasks[task_id]

# 创建全局任务管理器实例
def get_task_manager():
    """获取全局任务管理器实例"""
    global task_manager
    if task_manager is None:
        task_manager = AsyncTaskManager()
    return task_manager
