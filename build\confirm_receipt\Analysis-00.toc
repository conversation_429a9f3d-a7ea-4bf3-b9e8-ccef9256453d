(['D:\\py-ide\\taobao_rate\\taobao_order_system\\confirm_receipt.py'],
 ['D:\\py-ide\\taobao_rate\\taobao_order_system'],
 ['DrissionPage',
  'argparse',
  'json',
  'datetime',
  'random',
  'threading',
  'subprocess',
  'auto_review',
  'codecs'],
 ['c:\\users\\<USER>\\venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'c:\\users\\<USER>\\venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 [],
 [],
 False,
 False,
 False,
 {},
 [],
 [('auto_review.py', 'D:\\py-ide\\taobao_rate\\auto_review.py', 'DATA')],
 '3.7.4 (tags/v3.7.4:e09359112e, Jul  8 2019, 20:34:20) [MSC v.1916 64 bit '
 '(AMD64)]',
 [('pyi_rth_pkgutil',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('confirm_receipt',
   'D:\\py-ide\\taobao_rate\\taobao_order_system\\confirm_receipt.py',
   'PYSOURCE')],
 [('_distutils_hack',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Program Files\\Python37\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Program Files\\Python37\\lib\\contextlib.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Program Files\\Python37\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Program Files\\Python37\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Program Files\\Python37\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Program Files\\Python37\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Program Files\\Python37\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('py_compile', 'D:\\Program Files\\Python37\\lib\\py_compile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Program Files\\Python37\\lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'D:\\Program Files\\Python37\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'D:\\Program Files\\Python37\\lib\\zipfile.py', 'PYMODULE'),
  ('struct', 'D:\\Program Files\\Python37\\lib\\struct.py', 'PYMODULE'),
  ('tarfile', 'D:\\Program Files\\Python37\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Program Files\\Python37\\lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'D:\\Program Files\\Python37\\lib\\_compression.py',
   'PYMODULE'),
  ('copy', 'D:\\Program Files\\Python37\\lib\\copy.py', 'PYMODULE'),
  ('lzma', 'D:\\Program Files\\Python37\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\Program Files\\Python37\\lib\\bz2.py', 'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\Program Files\\Python37\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\Program Files\\Python37\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('_osx_support',
   'D:\\Program Files\\Python37\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\Program Files\\Python37\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'D:\\Program Files\\Python37\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\Program Files\\Python37\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\Program Files\\Python37\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\Program Files\\Python37\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\Program Files\\Python37\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\Program Files\\Python37\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\Program Files\\Python37\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('sysconfig', 'D:\\Program Files\\Python37\\lib\\sysconfig.py', 'PYMODULE'),
  ('pprint', 'D:\\Program Files\\Python37\\lib\\pprint.py', 'PYMODULE'),
  ('string', 'D:\\Program Files\\Python37\\lib\\string.py', 'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('shlex', 'D:\\Program Files\\Python37\\lib\\shlex.py', 'PYMODULE'),
  ('setuptools._distutils.text_file',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'D:\\Program Files\\Python37\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\Program Files\\Python37\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\Program Files\\Python37\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('getopt', 'D:\\Program Files\\Python37\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\Program Files\\Python37\\lib\\gettext.py', 'PYMODULE'),
  ('win32con',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('logging',
   'D:\\Program Files\\Python37\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\Program Files\\Python37\\lib\\pickle.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\Program Files\\Python37\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\Program Files\\Python37\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'D:\\Program Files\\Python37\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\Program Files\\Python37\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   'D:\\Program Files\\Python37\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   'D:\\Program Files\\Python37\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Program Files\\Python37\\lib\\configparser.py',
   'PYMODULE'),
  ('email', 'D:\\Program Files\\Python37\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'D:\\Program Files\\Python37\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Program Files\\Python37\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Program Files\\Python37\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Program Files\\Python37\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\Program Files\\Python37\\lib\\calendar.py', 'PYMODULE'),
  ('socket', 'D:\\Program Files\\Python37\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\Program Files\\Python37\\lib\\selectors.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\Program Files\\Python37\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Program Files\\Python37\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Program Files\\Python37\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Program Files\\Python37\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Program Files\\Python37\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Program Files\\Python37\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Program Files\\Python37\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Program Files\\Python37\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Program Files\\Python37\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Program Files\\Python37\\lib\\base64.py', 'PYMODULE'),
  ('quopri', 'D:\\Program Files\\Python37\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'D:\\Program Files\\Python37\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\Program Files\\Python37\\lib\\optparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\Program Files\\Python37\\lib\\textwrap.py', 'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Program Files\\Python37\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Program Files\\Python37\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Program Files\\Python37\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Program Files\\Python37\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Program Files\\Python37\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Program Files\\Python37\\lib\\email\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('cgi', 'D:\\Program Files\\Python37\\lib\\cgi.py', 'PYMODULE'),
  ('html', 'D:\\Program Files\\Python37\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Program Files\\Python37\\lib\\html\\entities.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('glob', 'D:\\Program Files\\Python37\\lib\\glob.py', 'PYMODULE'),
  ('setuptools._distutils.command.register',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('getpass', 'D:\\Program Files\\Python37\\lib\\getpass.py', 'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('site', 'D:\\Program Files\\Python37\\lib\\site.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\Program Files\\Python37\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Program Files\\Python37\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Program Files\\Python37\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\Program Files\\Python37\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server',
   'D:\\Program Files\\Python37\\lib\\http\\server.py',
   'PYMODULE'),
  ('http', 'D:\\Program Files\\Python37\\lib\\http\\__init__.py', 'PYMODULE'),
  ('socketserver',
   'D:\\Program Files\\Python37\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\Program Files\\Python37\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.client',
   'D:\\Program Files\\Python37\\lib\\http\\client.py',
   'PYMODULE'),
  ('ssl', 'D:\\Program Files\\Python37\\lib\\ssl.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Program Files\\Python37\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\Program Files\\Python37\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\Program Files\\Python37\\lib\\tty.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Program Files\\Python37\\lib\\pkgutil.py', 'PYMODULE'),
  ('inspect', 'D:\\Program Files\\Python37\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'D:\\Program Files\\Python37\\lib\\ast.py', 'PYMODULE'),
  ('dis', 'D:\\Program Files\\Python37\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Program Files\\Python37\\lib\\opcode.py', 'PYMODULE'),
  ('setuptools._distutils.command.config',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Program Files\\Python37\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Program Files\\Python37\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue', 'D:\\Program Files\\Python37\\lib\\queue.py', 'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Program Files\\Python37\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('bisect', 'D:\\Program Files\\Python37\\lib\\bisect.py', 'PYMODULE'),
  ('multiprocessing.semaphore_tracker',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\semaphore_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'D:\\Program Files\\Python37\\lib\\runpy.py', 'PYMODULE'),
  ('signal', 'D:\\Program Files\\Python37\\lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Program Files\\Python37\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Program Files\\Python37\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Program Files\\Python37\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Program Files\\Python37\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\Program Files\\Python37\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('decimal', 'D:\\Program Files\\Python37\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Program Files\\Python37\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars',
   'D:\\Program Files\\Python37\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers', 'D:\\Program Files\\Python37\\lib\\numbers.py', 'PYMODULE'),
  ('hmac', 'D:\\Program Files\\Python37\\lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Program Files\\Python37\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Program Files\\Python37\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\Program Files\\Python37\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Program Files\\Python37\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Program Files\\Python37\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Program Files\\Python37\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Program Files\\Python37\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Program Files\\Python37\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Program Files\\Python37\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Program Files\\Python37\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('difflib', 'D:\\Program Files\\Python37\\lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'D:\\Program Files\\Python37\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Program Files\\Python37\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('typing', 'D:\\Program Files\\Python37\\lib\\typing.py', 'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('__future__', 'D:\\Program Files\\Python37\\lib\\__future__.py', 'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.metadata',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('typing_extensions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Program Files\\Python37\\lib\\dataclasses.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Program Files\\Python37\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'D:\\Program Files\\Python37\\lib\\csv.py', 'PYMODULE'),
  ('setuptools._vendor',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\Program Files\\Python37\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\Program Files\\Python37\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.extension',
   'D:\\Program Files\\Python37\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.logging',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\Program Files\\Python37\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.errors',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('pkg_resources',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('imp', 'D:\\Program Files\\Python37\\lib\\imp.py', 'PYMODULE'),
  ('plistlib', 'D:\\Program Files\\Python37\\lib\\plistlib.py', 'PYMODULE'),
  ('setuptools.wheel',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\Program Files\\Python37\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\Program Files\\Python37\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('importlib_metadata',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('zipp',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.py310compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.command',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\Program Files\\Python37\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\Program Files\\Python37\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\Program Files\\Python37\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.core',
   'D:\\Program Files\\Python37\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'D:\\Program Files\\Python37\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('nturl2path', 'D:\\Program Files\\Python37\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\Program Files\\Python37\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Program Files\\Python37\\lib\\netrc.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\Program Files\\Python37\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('stringprep', 'D:\\Program Files\\Python37\\lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Program Files\\Python37\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Program Files\\Python37\\lib\\_py_abc.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Program Files\\Python37\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Program Files\\Python37\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('datetime', 'D:\\Program Files\\Python37\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'D:\\Program Files\\Python37\\lib\\_strptime.py', 'PYMODULE'),
  ('platform', 'D:\\Program Files\\Python37\\lib\\platform.py', 'PYMODULE'),
  ('urllib3',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('idna',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('ipaddress', 'D:\\Program Files\\Python37\\lib\\ipaddress.py', 'PYMODULE'),
  ('urllib3.util.proxy',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._collections',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.packages.backports.weakref_finalize',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\packages\\backports\\weakref_finalize.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3._version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('DrissionPage',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\__init__.py',
   'PYMODULE'),
  ('DrissionPage.version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\version.py',
   'PYMODULE'),
  ('DrissionPage._pages.web_page',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_pages\\web_page.py',
   'PYMODULE'),
  ('DrissionPage._pages', '-', 'PYMODULE'),
  ('DrissionPage._units.setter',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\setter.py',
   'PYMODULE'),
  ('DrissionPage._units', '-', 'PYMODULE'),
  ('DrissionPage.errors',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\errors.py',
   'PYMODULE'),
  ('DrissionPage._functions.web',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_functions\\web.py',
   'PYMODULE'),
  ('DrissionPage._functions', '-', 'PYMODULE'),
  ('DataRecorder.tools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DataRecorder\\tools.py',
   'PYMODULE'),
  ('DataRecorder',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DataRecorder\\__init__.py',
   'PYMODULE'),
  ('DataRecorder.recorder',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DataRecorder\\recorder.py',
   'PYMODULE'),
  ('DataRecorder.style.cell_style',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DataRecorder\\style\\cell_style.py',
   'PYMODULE'),
  ('DataRecorder.style',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DataRecorder\\style\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Program Files\\Python37\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Program Files\\Python37\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Program Files\\Python37\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Program Files\\Python37\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Program Files\\Python37\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('doctest', 'D:\\Program Files\\Python37\\lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\Program Files\\Python37\\lib\\pdb.py', 'PYMODULE'),
  ('code', 'D:\\Program Files\\Python37\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Program Files\\Python37\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\Program Files\\Python37\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\Program Files\\Python37\\lib\\cmd.py', 'PYMODULE'),
  ('lxml.cssselect',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('cssselect',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cssselect\\__init__.py',
   'PYMODULE'),
  ('cssselect.xpath',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cssselect\\xpath.py',
   'PYMODULE'),
  ('cssselect.parser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cssselect\\parser.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('openpyxl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.distutils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('dummy_threading',
   'D:\\Program Files\\Python37\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('_dummy_thread',
   'D:\\Program Files\\Python37\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.machar',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('DataRecorder.setter',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DataRecorder\\setter.py',
   'PYMODULE'),
  ('DataRecorder.base',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DataRecorder\\base.py',
   'PYMODULE'),
  ('DataRecorder.filler',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DataRecorder\\filler.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('PIL.Image',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('fractions', 'D:\\Program Files\\Python37\\lib\\fractions.py', 'PYMODULE'),
  ('PIL.ImageShow',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'D:\\Program Files\\Python37\\lib\\colorsys.py', 'PYMODULE'),
  ('PIL.ImageFilter',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('cffi',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Program Files\\Python37\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Program Files\\Python37\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Program Files\\Python37\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Program Files\\Python37\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Program Files\\Python37\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Program Files\\Python37\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.verifier',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('PIL._util',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('DataRecorder.db_recorder',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DataRecorder\\db_recorder.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Program Files\\Python37\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Program Files\\Python37\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Program Files\\Python37\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('DataRecorder.byte_recorder',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DataRecorder\\byte_recorder.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('tests',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\tests\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('DrissionPage._functions.tools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_functions\\tools.py',
   'PYMODULE'),
  ('DrissionPage._configs.options_manage',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_configs\\options_manage.py',
   'PYMODULE'),
  ('DrissionPage._configs', '-', 'PYMODULE'),
  ('DrissionPage._units.cookies_setter',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\cookies_setter.py',
   'PYMODULE'),
  ('requests.structures',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\Program Files\\Python37\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('charset_normalizer',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('DrissionPage._functions.settings',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_functions\\settings.py',
   'PYMODULE'),
  ('DrissionPage._functions.texts',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_functions\\texts.py',
   'PYMODULE'),
  ('DrissionPage._functions.cookies',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_functions\\cookies.py',
   'PYMODULE'),
  ('tldextract',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\tldextract\\__init__.py',
   'PYMODULE'),
  ('tldextract.tldextract',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\tldextract\\tldextract.py',
   'PYMODULE'),
  ('tldextract.suffix_list',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\tldextract\\suffix_list.py',
   'PYMODULE'),
  ('requests_file',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests_file.py',
   'PYMODULE'),
  ('requests.adapters',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('requests.utils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.__version__',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.certs',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.models',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.status_codes',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.hooks',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.exceptions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.cookies',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('tldextract.remote',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\tldextract\\remote.py',
   'PYMODULE'),
  ('tldextract.cache',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\tldextract\\cache.py',
   'PYMODULE'),
  ('filelock',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\filelock\\__init__.py',
   'PYMODULE'),
  ('filelock.version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\filelock\\version.py',
   'PYMODULE'),
  ('filelock._windows',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\filelock\\_windows.py',
   'PYMODULE'),
  ('filelock._util',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\filelock\\_util.py',
   'PYMODULE'),
  ('filelock._unix',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\filelock\\_unix.py',
   'PYMODULE'),
  ('filelock._soft',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\filelock\\_soft.py',
   'PYMODULE'),
  ('filelock._error',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\filelock\\_error.py',
   'PYMODULE'),
  ('filelock._api',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\filelock\\_api.py',
   'PYMODULE'),
  ('tldextract._version',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\tldextract\\_version.py',
   'PYMODULE'),
  ('DrissionPage._base.base',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_base\\base.py',
   'PYMODULE'),
  ('DrissionPage._base', '-', 'PYMODULE'),
  ('DrissionPage._functions.locator',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_functions\\locator.py',
   'PYMODULE'),
  ('DrissionPage._functions.by',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_functions\\by.py',
   'PYMODULE'),
  ('DrissionPage._functions.elements',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_functions\\elements.py',
   'PYMODULE'),
  ('DrissionPage._elements.none_element',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_elements\\none_element.py',
   'PYMODULE'),
  ('DrissionPage._elements', '-', 'PYMODULE'),
  ('DownloadKit',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DownloadKit\\__init__.py',
   'PYMODULE'),
  ('DownloadKit.downloadKit',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DownloadKit\\downloadKit.py',
   'PYMODULE'),
  ('DownloadKit.setter',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DownloadKit\\setter.py',
   'PYMODULE'),
  ('DownloadKit.mission',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DownloadKit\\mission.py',
   'PYMODULE'),
  ('DownloadKit._funcs',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DownloadKit\\_funcs.py',
   'PYMODULE'),
  ('DrissionPage._pages.session_page',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_pages\\session_page.py',
   'PYMODULE'),
  ('DrissionPage._elements.session_element',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_elements\\session_element.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_page',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_pages\\chromium_page.py',
   'PYMODULE'),
  ('DrissionPage._units.waiter',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\waiter.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_base',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_pages\\chromium_base.py',
   'PYMODULE'),
  ('DrissionPage._units.states',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\states.py',
   'PYMODULE'),
  ('DrissionPage._units.scroller',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\scroller.py',
   'PYMODULE'),
  ('DrissionPage._units.screencast',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\screencast.py',
   'PYMODULE'),
  ('DrissionPage._units.rect',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\rect.py',
   'PYMODULE'),
  ('DrissionPage._units.listener',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\listener.py',
   'PYMODULE'),
  ('DrissionPage._base.driver',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_base\\driver.py',
   'PYMODULE'),
  ('websocket',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._socket',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._utils',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._core',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._http',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._url',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._handshake',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._app',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._logging',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._abnf',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('DrissionPage._units.console',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\console.py',
   'PYMODULE'),
  ('DrissionPage._units.actions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\actions.py',
   'PYMODULE'),
  ('DrissionPage._functions.keys',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_functions\\keys.py',
   'PYMODULE'),
  ('DrissionPage._elements.chromium_element',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_elements\\chromium_element.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_frame',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_pages\\chromium_frame.py',
   'PYMODULE'),
  ('DrissionPage._units.selector',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\selector.py',
   'PYMODULE'),
  ('DrissionPage._units.clicker',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\clicker.py',
   'PYMODULE'),
  ('DrissionPage._units.downloader',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_units\\downloader.py',
   'PYMODULE'),
  ('DrissionPage._configs.session_options',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_configs\\session_options.py',
   'PYMODULE'),
  ('DrissionPage._configs.chromium_options',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_configs\\chromium_options.py',
   'PYMODULE'),
  ('DrissionPage._base.chromium',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_base\\chromium.py',
   'PYMODULE'),
  ('DrissionPage._pages.mix_tab',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_pages\\mix_tab.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_tab',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_pages\\chromium_tab.py',
   'PYMODULE'),
  ('DrissionPage._functions.browser',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\DrissionPage\\_functions\\browser.py',
   'PYMODULE'),
  ('requests',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.api',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.packages',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('hashlib', 'D:\\Program Files\\Python37\\lib\\hashlib.py', 'PYMODULE'),
  ('subprocess', 'D:\\Program Files\\Python37\\lib\\subprocess.py', 'PYMODULE'),
  ('threading', 'D:\\Program Files\\Python37\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Program Files\\Python37\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\Program Files\\Python37\\lib\\argparse.py', 'PYMODULE'),
  ('random', 'D:\\Program Files\\Python37\\lib\\random.py', 'PYMODULE'),
  ('json', 'D:\\Program Files\\Python37\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\Program Files\\Python37\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Program Files\\Python37\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Program Files\\Python37\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('uuid', 'D:\\Program Files\\Python37\\lib\\uuid.py', 'PYMODULE'),
  ('netbios',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE')],
 [('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('python37.dll', 'D:\\Program Files\\Python37\\python37.dll', 'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Program Files\\Python37\\VCRUNTIME140.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\ProgramData\\Miniconda3\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\.libs\\libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'BINARY'),
  ('_lzma.pyd', 'D:\\Program Files\\Python37\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Program Files\\Python37\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('win32\\win32api.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\Program Files\\Python37\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Program Files\\Python37\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\Program Files\\Python37\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Program Files\\Python37\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Program Files\\Python37\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\etree.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\_elementpath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\sax.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\objectify.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\diff.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\builder.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\psutil\\_psutil_windows.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_imagingcms.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\_cffi_backend.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('win32\\win32wnet.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('pywin32_system32\\pywintypes37.dll',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pywin32_system32\\pywintypes37.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\Program Files\\Python39\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'D:\\Program Files\\Python37\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\Program Files\\Python37\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('sqlite3.dll', 'D:\\Program Files\\Python37\\DLLs\\sqlite3.dll', 'BINARY')],
 [],
 [],
 [('auto_review.py', 'D:\\py-ide\\taobao_rate\\auto_review.py', 'DATA'),
  ('base_library.zip',
   'D:\\py-ide\\taobao_rate\\taobao_order_system\\build\\confirm_receipt\\base_library.zip',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\LICENSE',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\RECORD',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\RECORD',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\METADATA',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\METADATA',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\WHEEL',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('certifi\\py.typed',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA')],
 [])
