('D:\\py-ide\\taobao_rate\\taobao_order_system\\build\\confirm_receipt\\confirm_receipt.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True},
 [('PYZ-00.pyz',
   'D:\\py-ide\\taobao_rate\\taobao_order_system\\build\\confirm_receipt\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\py-ide\\taobao_rate\\taobao_order_system\\build\\confirm_receipt\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\py-ide\\taobao_rate\\taobao_order_system\\build\\confirm_receipt\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\py-ide\\taobao_rate\\taobao_order_system\\build\\confirm_receipt\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\py-ide\\taobao_rate\\taobao_order_system\\build\\confirm_receipt\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\py-ide\\taobao_rate\\taobao_order_system\\build\\confirm_receipt\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('confirm_receipt',
   'D:\\py-ide\\taobao_rate\\taobao_order_system\\confirm_receipt.py',
   'PYSOURCE'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('python37.dll', 'D:\\Program Files\\Python37\\python37.dll', 'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Program Files\\Python37\\VCRUNTIME140.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\ProgramData\\Miniconda3\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\.libs\\libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'BINARY'),
  ('_lzma.pyd', 'D:\\Program Files\\Python37\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Program Files\\Python37\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('win32\\win32api.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\Program Files\\Python37\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Program Files\\Python37\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\Program Files\\Python37\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Program Files\\Python37\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Program Files\\Python37\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\etree.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\_elementpath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\sax.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\objectify.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\html\\diff.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\builder.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\psutil\\_psutil_windows.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_imagingcms.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\_cffi_backend.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('win32\\win32wnet.pyd',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('pywin32_system32\\pywintypes37.dll',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\pywin32_system32\\pywintypes37.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\Program Files\\Python39\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'D:\\Program Files\\Python37\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\Program Files\\Python37\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('sqlite3.dll', 'D:\\Program Files\\Python37\\DLLs\\sqlite3.dll', 'BINARY'),
  ('auto_review.py', 'D:\\py-ide\\taobao_rate\\auto_review.py', 'DATA'),
  ('base_library.zip',
   'D:\\py-ide\\taobao_rate\\taobao_order_system\\build\\confirm_receipt\\base_library.zip',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\LICENSE',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\RECORD',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\RECORD',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\METADATA',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\METADATA',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\WHEEL',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('certifi\\py.typed',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'c:\\users\\<USER>\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA')],
 False,
 False,
 False,
 [],
 None,
 None,
 None)
