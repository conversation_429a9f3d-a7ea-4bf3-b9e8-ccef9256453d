import os
import sys
import json
import time
import traceback
import random
import subprocess
import platform
import uuid
from DrissionPage import ChromiumOptions, ChromiumPage

def get_chrome_profiles():
    """获取所有Chrome用户配置，使用旧格式ID (profile_数字)"""
    profiles = []
    try:
        # 获取Chrome配置目录
        if os.name == 'nt':  # Windows
            chrome_user_data_dir = os.path.join(os.environ['LOCALAPPDATA'], 'Google', 'Chrome', 'User Data')
            print(f"Windows Chrome用户数据目录: {chrome_user_data_dir}")
        else:
            print(f"不支持的操作系统: {os.name}")
            return profiles

        # 检查用户数据目录是否存在
        if not os.path.exists(chrome_user_data_dir):
            print(f"Chrome用户数据目录不存在: {chrome_user_data_dir}")
            return profiles

        # 检查Local State文件
        local_state_path = os.path.join(chrome_user_data_dir, 'Local State')
        if not os.path.exists(local_state_path):
            print(f"未找到Chrome Local State文件: {local_state_path}")

            # 尝试直接查找Profile目录
            print("尝试直接查找Profile目录...")
            profile_dirs = [d for d in os.listdir(chrome_user_data_dir)
                           if os.path.isdir(os.path.join(chrome_user_data_dir, d))
                           and (d.startswith('Profile') or d == 'Default')]

            if profile_dirs:
                for i, profile_dir in enumerate(profile_dirs):
                    # 使用旧的id格式
                    old_id = f'profile_{i}'
                    # 保存chrome_id以便兼容
                    chrome_id = f"chrome_{profile_dir}"

                    profiles.append({
                        'id': old_id,  # 使用旧ID作为主要标识符
                        'chrome_id': chrome_id,  # 保存chrome_id以便兼容
                        'name': f'用户{i+1}',
                        'path': chrome_user_data_dir,
                        'profile_dir': profile_dir,
                        'is_active': profile_dir == 'Default',
                        'status': '未知',
                        'orig_index': i  # 添加原始索引
                    })
                return profiles
            else:
                print("未找到任何Profile目录")
                return profiles

        # 读取Local State文件
        with open(local_state_path, 'r', encoding='utf-8') as f:
            local_state = json.load(f)

        # 获取用户配置信息
        profile_info = local_state.get('profile', {}).get('info_cache', {})
        active_profile = local_state.get('profile', {}).get('last_active_profiles', [])[0] if local_state.get('profile', {}).get('last_active_profiles', []) else None

        for i, (profile_dir, info) in enumerate(profile_info.items()):
            # 使用旧的id格式
            old_id = f'profile_{i}'
            # 保存chrome_id以便兼容
            chrome_id = f"chrome_{profile_dir}"

            profiles.append({
                'id': old_id,  # 使用旧ID作为主要标识符
                'chrome_id': chrome_id,  # 保存chrome_id以便兼容
                'name': info.get('name', profile_dir),
                'path': chrome_user_data_dir,
                'profile_dir': profile_dir,
                'is_active': profile_dir == active_profile,
                'status': '未知',
                'orig_index': i  # 添加原始索引
            })

        # 如果没有找到任何配置，添加默认配置
        if not profiles:
            print("未找到任何用户配置，添加默认配置")
            profiles.append({
                'id': 'profile_0',  # 使用旧ID作为主要标识符
                'chrome_id': 'chrome_Default',  # 保存chrome_id以便兼容
                'name': 'Default',
                'path': chrome_user_data_dir,
                'profile_dir': 'Default',
                'is_active': True,
                'status': '未知',
                'orig_index': 0  # 添加原始索引
            })
    except Exception as e:
        print(f"获取Chrome配置信息失败: {e}")
        traceback.print_exc()

    print(f"共找到 {len(profiles)} 个Chrome用户配置")
    for i, p in enumerate(profiles):
        print(f"配置 {i}: {p['name']} - {p['profile_dir']} - ID: {p['id']}")

    return profiles

def open_chrome_with_profile(profile, headless=False):
    """打开Chrome浏览器并加载指定的用户配置文件"""
    try:
        # 获取原始用户配置路径
        original_user_data_dir = profile['path']
        original_profile_dir = profile['profile_dir']
        print(f"Chrome用户配置: {original_user_data_dir}/{original_profile_dir}")

        # 为每个浏览器实例生成一个唯一标识，但确保同一个profile_id在短时间内不会重复创建
        # 使用profile_id和当前分钟数，避免在一分钟内重复创建
        current_minute = int(time.time()) // 60
        instance_id = f"instance_{profile['id']}_{current_minute}"
        print(f"创建Chrome实例: {instance_id}")

        # 使用系统浏览器用户目录
        user_id = profile['id']
        print(f"为用户 {user_id} 使用系统浏览器用户目录")

        # 为每个用户分配固定端口，避免为同一用户创建多个实例
        # 提取用户ID中的数字部分（如果有）
        user_id_num = ''.join(filter(str.isdigit, user_id)) or '0'
        # 将数字转换为整数，并确保在有效端口范围内
        # 添加当前时间的分钟数作为偏移量，确保每次运行使用不同的端口
        time_offset = int(time.time() / 60) % 100  # 0-99的循环值
        debug_port = 9000 + (int(user_id_num) + time_offset) % 1000
        print(f"为用户 {user_id} 分配固定端口: {debug_port}，时间偏移量: {time_offset}")

        # 使用原始的用户数据目录和配置文件目录
        user_data_path = original_user_data_dir
        profile_dir = original_profile_dir
        print(f"使用Chrome用户数据目录: {user_data_path}")
        print(f"使用配置文件目录: {profile_dir}")

        # 获取Chrome可执行文件路径
        chrome_exe = None
        if platform.system() == "Windows":
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe")
            ]
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_exe = path
                    break

        if chrome_exe:
            print(f"找到Chrome可执行文件: {chrome_exe}")
        else:
            print("未找到Chrome可执行文件，将使用默认路径")
            chrome_exe = "chrome"

        # 创建DrissionPage的ChromiumOptions对象
        co = ChromiumOptions()

        # 设置浏览器路径
        co.set_browser_path(chrome_exe)

        # 设置用户数据目录和配置文件
        co.set_user_data_path(original_user_data_dir)
        co.set_user(original_profile_dir)

        # 设置端口
        co.set_local_port(debug_port)

        # 添加其他必要的选项
        co.set_argument("--no-first-run")  # 跳过首次运行向导
        co.set_argument("--no-default-browser-check")  # 跳过默认浏览器检查
        co.set_argument(f"--window-name=Chrome_{profile['name']}_{instance_id}")  # 设置窗口标题
        co.set_argument("--disable-features=TranslateUI")  # 禁用翻译功能
        co.set_argument("--disable-extensions")  # 禁用扩展，减少干扰
        co.set_argument("--disable-background-networking")  # 禁用后台网络活动
        co.set_argument("--disable-background-timer-throttling")  # 禁用后台计时器节流
        co.set_argument("--disable-backgrounding-occluded-windows")  # 禁用背景遮挡窗口
        co.set_argument("--disable-breakpad")  # 禁用崩溃报告
        co.set_argument("--disable-component-extensions-with-background-pages")  # 禁用带有后台页面的组件扩展
        co.set_argument("--disable-dev-shm-usage")  # 禁用/dev/shm使用
        co.set_argument("--disable-domain-reliability")  # 禁用域可靠性
        co.set_argument("--disable-sync")  # 禁用同步功能

        # 检查是否已经有同一用户的Chrome实例在运行
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if f'--remote-debugging-port={debug_port}' in cmdline:
                        print(f"发现用户 {user_id} 已有Chrome实例在运行，端口: {debug_port}")
                        try:
                            # 尝试直接连接到已有的实例
                            page = ChromiumPage(co)
                            return page
                        except:
                            # 如果连接失败，尝试关闭已有实例后重新启动
                            proc.kill()
                            time.sleep(2)
                            break
        except ImportError:
            print("psutil模块未安装，无法检查已有实例")
        except Exception as e:
            print(f"检查已有实例出错: {e}")

        # 防止Chrome崩溃报告
        if headless:
            co.headless(True)

        print(f"使用Chrome用户: {profile['name']} (ID: {profile['id']})")

        # 创建ChromiumPage对象
        try:
            print("创建ChromiumPage对象...")
            page = ChromiumPage(co)

            # 验证页面对象是否有效
            if page:
                # 尝试访问一个简单的属性，确认对象可用
                try:
                    _ = page.url  # 尝试获取url属性
                    print(f"ChromiumPage对象创建成功，初始URL: {page.url}")
                except Exception as e:
                    print(f"ChromiumPage对象创建后验证失败: {e}")
                    traceback.print_exc()
                    return None

                return page
            else:
                print("ChromiumPage对象创建失败，返回了None")
                return None
        except Exception as e:
            print(f"创建ChromiumPage对象失败: {e}")
            traceback.print_exc()
            return None

    except Exception as e:
        print(f"打开Chrome浏览器失败: {e}")
        traceback.print_exc()
        return None

class ChromeManager:
    """Chrome用户管理类"""

    def __init__(self):
        self.profiles = []
        self._initialized = False  # 添加初始化状态标记
        self._auto_extract_completed = False  # 添加自动提取完成标记
        self.refresh_profiles()

        # 在初始化时执行一次自动Cookie提取
        if not self._auto_extract_completed:
            print("应用启动时自动提取淘宝Cookie...")
            self._auto_extract_cookies()
            self._auto_extract_completed = True
            print("自动Cookie提取完成")

    def _get_chrome_path_from_config(self):
        """从配置文件获取Chrome路径"""
        try:
            # 使用集中配置管理器获取配置
            from config_manager import config_manager
            config = config_manager.get_config()
            chrome_path = config.get('chrome_path', '')

            if chrome_path and os.path.exists(chrome_path):
                print(f"从配置文件获取Chrome路径: {chrome_path}")
                return chrome_path
            return None
        except Exception as e:
            print(f"从配置获取Chrome路径失败: {e}")
            return None

    def refresh_profiles(self):
        """刷新Chrome用户配置列表"""
        self.profiles = get_chrome_profiles()
        return self.profiles

    def get_profiles(self):
        """获取所有Chrome用户配置，并加载配置信息，支持新旧两种格式"""
        try:
            # 使用集中配置管理器获取配置
            from config_manager import config_manager
            config = config_manager.get_config()

            # 注意：自动Cookie提取已移至__init__方法中，只在启动时执行一次

            # 如果配置中有profiles字段，更新每个用户的信息
            if 'profiles' in config and isinstance(config['profiles'], dict):
                # 第一步：先为每个从Local State读取的用户配置添加基本信息
                for profile in self.profiles:
                    # 尝试使用新格式ID (chrome_profile_dir)
                    chrome_id = profile.get('chrome_id')
                    # 兼容旧格式ID (profile_数字)
                    old_id = profile.get('id')

                    # 首先尝试使用新格式ID查找配置
                    if chrome_id in config['profiles']:
                        profile_config = config['profiles'][chrome_id]
                        # 添加支付密码
                        profile['pay_password'] = profile_config.get('pay_password', '')
                        # 添加cookie
                        profile['cookie'] = profile_config.get('cookie', '')
                        # 添加状态
                        if 'status' in profile_config:
                            profile['status'] = profile_config.get('status')
                        # 添加Chrome用户名
                        if 'chrome_user_name' in profile_config:
                            profile['chrome_user_name'] = profile_config.get('chrome_user_name')
                        else:
                            # 如果配置中没有chrome_user_name，使用profile中的name
                            profile_config['chrome_user_name'] = profile.get('name', '')
                    # 如果没有找到，尝试使用旧格式ID
                    elif old_id in config['profiles']:
                        profile_config = config['profiles'][old_id]
                        # 添加支付密码
                        profile['pay_password'] = profile_config.get('pay_password', '')
                        # 添加cookie
                        profile['cookie'] = profile_config.get('cookie', '')
                        # 添加状态
                        if 'status' in profile_config:
                            profile['status'] = profile_config.get('status')
                        # 添加Chrome用户名
                        if 'chrome_user_name' in profile_config:
                            profile['chrome_user_name'] = profile_config.get('chrome_user_name')
                        else:
                            # 如果配置中没有chrome_user_name，使用profile中的name
                            profile_config['chrome_user_name'] = profile.get('name', '')

                # 第二步：根据Chrome用户名匹配配置文件中的用户，并更新profile_dir
                config_updated = False

                # 遍历从Local State读取的每个用户配置
                for profile in self.profiles:
                    chrome_name = profile.get('name', '')  # Local State中的用户名
                    local_state_profile_dir = profile.get('profile_dir', '')  # Local State中的配置文件目录
                    chrome_id = profile.get('chrome_id', '')  # 新格式ID

                    if not chrome_name or not local_state_profile_dir:
                        continue

                    # 在配置文件中查找匹配的用户
                    matched = False
                    for profile_id, profile_config in config['profiles'].items():
                        # 检查chrome_user_name是否与Local State中的name匹配
                        if profile_config.get('chrome_user_name', '') == chrome_name:
                            # 找到匹配的用户，检查profile_dir是否一致
                            if profile_config.get('profile_dir', '') != local_state_profile_dir:
                                # 更新配置文件中的profile_dir
                                print(f"根据用户名 '{chrome_name}' 匹配，更新用户 {profile_id} 的配置文件目录: {profile_config.get('profile_dir', '无')} -> {local_state_profile_dir}")
                                profile_config['profile_dir'] = local_state_profile_dir
                                config_updated = True

                            # 如果是旧格式ID，检查并添加到映射关系（避免重复添加）
                            if profile_id.startswith('profile_') and profile_id != chrome_id:
                                if "profile_id_mapping" not in config:
                                    config["profile_id_mapping"] = {}

                                # 只在映射关系不存在或不同时才添加
                                existing_mapping = config["profile_id_mapping"].get(profile_id)
                                if existing_mapping != chrome_id:
                                    config["profile_id_mapping"][profile_id] = chrome_id
                                    config_updated = True
                                    print(f"添加ID映射关系: {profile_id} -> {chrome_id}")
                                # else:
                                #     print(f"ID映射关系已存在: {profile_id} -> {chrome_id}")

                            matched = True

                    # 如果没有找到匹配的用户，记录日志
                    if not matched:
                        print(f"警告: Local State中的用户 '{chrome_name}' 在配置文件中没有匹配的chrome_user_name")

                # 注意：系统设计为使用旧格式ID (profile_数字)，不进行迁移
                # 通过profile_id_mapping维护新旧ID的映射关系以确保兼容性

                # 如果有更新，保存配置
                if config_updated:
                    config_manager.save_config()
                    print("已更新配置文件")

        except Exception as e:
            print(f"加载配置文件中的用户信息失败: {e}")
            traceback.print_exc()

        return self.profiles

    def get_profile_by_id(self, profile_id):
        """根据ID获取用户配置，优先使用旧格式ID"""
        print(f"正在查找ID为 {profile_id} 的Chrome用户...")

        # 确保profile_id是字符串，便于比较
        profile_id = str(profile_id)

        # 1. 先尝试使用旧格式ID精确匹配
        for profile in self.profiles:
            if str(profile.get('id', '')) == profile_id:
                print(f"找到匹配的Chrome用户(旧ID): {profile.get('name', '')}")
                return profile

        # 2. 检查是否有数字匹配，例如前端传来"2"，而配置中是"profile_2"
        if profile_id.isdigit():
            profile_with_prefix = f"profile_{profile_id}"
            for profile in self.profiles:
                if str(profile.get('id', '')) == profile_with_prefix:
                    print(f"通过数字ID {profile_id} 匹配到Chrome用户: {profile.get('name', '')}")
                    return profile

        # 3. 尝试使用profile_dir直接匹配
        for profile in self.profiles:
            if str(profile.get('profile_dir', '')) == profile_id:
                print(f"通过profile_dir匹配到Chrome用户: {profile.get('name', '')}")
                return profile

        # 4. 如果没有找到，尝试宽松匹配（忽略类型和空格）
        for profile in self.profiles:
            if str(profile.get('id', '')).strip() == profile_id.strip():
                print(f"通过宽松匹配找到Chrome用户: {profile.get('name', '')}")
                return profile

        # 5. 尝试使用新格式ID (chrome_XXX) 匹配（兼容性考虑）
        for profile in self.profiles:
            if str(profile.get('chrome_id', '')) == profile_id:
                print(f"找到匹配的Chrome用户(新ID): {profile.get('name', '')}")
                return profile

        # 6. 如果是不带前缀的profile_dir，尝试添加前缀后匹配
        chrome_id_with_prefix = f"chrome_{profile_id}"
        for profile in self.profiles:
            if str(profile.get('chrome_id', '')) == chrome_id_with_prefix:
                print(f"通过添加前缀匹配到Chrome用户: {profile.get('name', '')}")
                return profile

        print(f"未找到ID为 {profile_id} 的Chrome用户")
        return None

    def update_profile_status(self, profile_id, status, status_data=None):
        """
        更新用户状态，支持新旧两种格式的ID

        Args:
            profile_id: 用户ID（可以是旧格式ID、新格式ID或profile_dir）
            status: 状态字符串
            status_data: 状态相关的额外数据（可选）
        """
        print(f"更新用户 {profile_id} 状态: {status}")

        # 获取用户配置
        profile = self.get_profile_by_id(profile_id)
        if profile:
            profile['status'] = status
            profile['last_updated'] = time.strftime("%Y-%m-%d %H:%M:%S")

            # 如果提供了状态数据，则更新
            if status_data is not None:
                profile['status_data'] = status_data
                print(f"更新用户 {profile_id} 状态数据: {status_data}")

            # 同时更新配置文件中的状态
            try:
                from config_manager import config_manager

                # 获取新格式ID
                chrome_id = profile.get('chrome_id')
                if chrome_id:
                    # 获取用户配置
                    profile_data = config_manager.get_profile(chrome_id)
                    # 更新状态
                    profile_data['status'] = status
                    # 保存配置
                    config_manager.update_profile(chrome_id, profile_data)
                    config_manager.save_config()
                    print(f"已更新配置文件中用户 {chrome_id} 的状态")
            except Exception as e:
                print(f"更新配置文件中的用户状态失败: {e}")

            return True

        print(f"未找到用户 {profile_id}，无法更新状态")
        return False

    def open_browser_with_profile(self, profile, headless=False):
        """直接使用profile对象打开浏览器，避免重复查找用户配置"""
        if profile:
            # 尝试使用命令行方式启动Chrome
            try:
                return self.open_chrome_with_command_line(profile, headless)
            except Exception as e:
                print(f"命令行方式启动Chrome失败: {e}")
                traceback.print_exc()
                # 回退到DrissionPage方式
                return open_chrome_with_profile(profile, headless)
        return None

    def open_chrome_with_command_line(self, profile, headless=False):
        """使用命令行方式启动Chrome浏览器"""
        process = None
        try:
            # 获取原始用户配置路径
            user_data_dir = profile['path']
            profile_dir = profile['profile_dir']
            print(f"命令行方式启动Chrome，用户配置: {user_data_dir}/{profile_dir}")

            # 为每个浏览器实例生成一个唯一标识
            instance_id = f"instance_{profile['id']}_{int(time.time())}"

            # 为每个用户分配固定端口，确保不同用户使用不同端口
            user_id = profile['id']
            user_id_num = ''.join(filter(str.isdigit, user_id)) or '0'
            # 使用用户ID和随机数生成端口，确保每次都不同
            random_offset = random.randint(1, 999)  # 完全随机的偏移量
            debug_port = 9000 + (int(user_id_num) * 10 + random_offset) % 1000
            print(f"为用户 {user_id} 分配端口: {debug_port}，随机偏移量: {random_offset}")

            # 检查端口是否已被占用
            try:
                import socket
                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                result = s.connect_ex(('127.0.0.1', debug_port))
                s.close()
                if result == 0:
                    print(f"端口 {debug_port} 已被占用，尝试使用其他端口")
                    # 尝试其他端口
                    for _ in range(10):  # 最多尝试10次
                        debug_port = random.randint(9000, 9999)
                        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        result = s.connect_ex(('127.0.0.1', debug_port))
                        s.close()
                        if result != 0:
                            print(f"找到可用端口: {debug_port}")
                            break
                    else:
                        print("无法找到可用端口")
                        return None
            except:
                print("无法检查端口占用情况")

            # 获取Chrome可执行文件路径
            chrome_exe = None

            # 首先从配置文件获取Chrome路径
            config_chrome_path = self._get_chrome_path_from_config()
            if config_chrome_path and os.path.exists(config_chrome_path):
                chrome_exe = config_chrome_path
                print(f"使用配置文件中的Chrome路径: {chrome_exe}")
            else:
                # 如果配置文件中没有有效路径，尝试常见路径
                if platform.system() == "Windows":
                    chrome_paths = [
                        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                        os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"),
                        # 添加更多可能的路径
                        r"C:\Users\<USER>\.PyCharm2018.3\config\scratches\chrome\App\chrome.exe"
                    ]

                    for path in chrome_paths:
                        if path and os.path.exists(path):
                            chrome_exe = path
                            print(f"找到Chrome可执行文件: {chrome_exe}")
                            break

                if not chrome_exe:
                    print("未找到Chrome可执行文件，尝试查找系统中的Chrome...")
                    # 尝试使用where命令查找Chrome
                    try:
                        if platform.system() == "Windows":
                            result = subprocess.run(['where', 'chrome'], capture_output=True, text=True)
                            if result.returncode == 0 and result.stdout.strip():
                                chrome_exe = result.stdout.strip().split('\n')[0]
                                print(f"通过where命令找到Chrome: {chrome_exe}")
                        else:
                            result = subprocess.run(['which', 'chrome'], capture_output=True, text=True)
                            if result.returncode == 0 and result.stdout.strip():
                                chrome_exe = result.stdout.strip()
                                print(f"通过which命令找到Chrome: {chrome_exe}")
                    except Exception as e:
                        print(f"查找Chrome命令失败: {e}")

            # 如果仍然没有找到Chrome，使用默认路径
            if not chrome_exe:
                print("未找到Chrome可执行文件，将使用默认路径")
                chrome_exe = "chrome"

            # 验证Chrome可执行文件是否存在
            if chrome_exe != "chrome" and not os.path.exists(chrome_exe):
                print(f"警告: Chrome可执行文件路径 {chrome_exe} 不存在")

            print(f"最终使用的Chrome可执行文件路径: {chrome_exe}")

            # 构建命令行参数
            cmd = [
                chrome_exe,
                f"--user-data-dir={user_data_dir}",
                f"--profile-directory={profile_dir}",
                f"--remote-debugging-port={debug_port}",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-extensions",
                "--disable-background-networking",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-breakpad",
                "--disable-component-extensions-with-background-pages",
                "--disable-dev-shm-usage",
                "--disable-domain-reliability",
                "--disable-sync",
                "--disable-features=TranslateUI",
                f"--window-name=Chrome_{profile['name']}_{instance_id}"
            ]

            if headless:
                cmd.append("--headless")

            # 启动Chrome
            print(f"执行命令: {' '.join(cmd)}")
            process = subprocess.Popen(cmd)
            print(f"Chrome进程已启动，PID: {process.pid}")

            # 等待Chrome启动，减少等待时间
            wait_time = 3  # 从5秒减少到3秒
            print(f"等待Chrome启动，等待{wait_time}秒...")
            time.sleep(wait_time)

            # 创建DrissionPage的ChromiumOptions对象
            co = ChromiumOptions()
            co.set_local_port(debug_port)

            # 连接到已启动的Chrome，增加重试次数
            max_retries = 3
            page = None
            for retry in range(max_retries):
                try:
                    print(f"尝试连接到已启动的Chrome，端口: {debug_port}，尝试 {retry+1}/{max_retries}")
                    page = ChromiumPage(co)
                    break
                except Exception as e:
                    print(f"连接失败，错误: {e}")
                    if retry < max_retries - 1:
                        print(f"等待1秒后重试...")
                        time.sleep(1)  # 从2秒减少到1秒
                    else:
                        print("连接失败，放弃尝试")
                        # 关闭进程
                        if process:
                            try:
                                process.terminate()
                                print(f"已终止Chrome进程 PID: {process.pid}")
                            except:
                                pass
                        return None

            # 验证页面对象是否有效
            if page:
                try:
                    _ = page.url  # 尝试获取url属性
                    print(f"成功连接到Chrome，初始URL: {page.url}")
                    # 保存进程对象到页面对象，以便后续关闭
                    setattr(page, '_chrome_process', process)
                    return page
                except Exception as e:
                    print(f"连接到Chrome后验证失败: {e}")
                    traceback.print_exc()
            else:
                print("连接到Chrome失败，返回了None")

            # 如果到这里，说明连接失败，关闭进程
            if process:
                try:
                    process.terminate()
                    print(f"已终止Chrome进程 PID: {process.pid}")
                except:
                    pass
            return None

        except Exception as e:
            print(f"命令行方式启动Chrome失败: {e}")
            traceback.print_exc()
            # 确保进程被关闭
            if process:
                try:
                    process.terminate()
                    print(f"已终止Chrome进程 PID: {process.pid}")
                except:
                    pass
            return None

    def open_browser(self, profile_id, headless=False):
        """打开指定ID的用户浏览器，确保使用正确的profile_dir"""
        # 获取用户配置
        profile = self.get_profile_by_id(profile_id)
        if not profile:
            print(f"未找到ID为 {profile_id} 的Chrome用户")
            return None

        # 验证profile_dir是否正确
        chrome_name = profile.get('name', '')
        if chrome_name:
            # 获取Chrome用户数据目录
            chrome_user_data_dir = os.path.join(os.environ['LOCALAPPDATA'], 'Google', 'Chrome', 'User Data')
            local_state_path = os.path.join(chrome_user_data_dir, 'Local State')

            if os.path.exists(local_state_path):
                try:
                    # 读取Local State文件
                    with open(local_state_path, 'r', encoding='utf-8') as f:
                        local_state = json.load(f)

                    # 获取用户配置信息
                    profile_info = local_state.get('profile', {}).get('info_cache', {})

                    # 查找匹配的用户名
                    for profile_dir, info in profile_info.items():
                        if info.get('name', '') == chrome_name:
                            # 找到匹配的用户，检查profile_dir是否一致
                            if profile.get('profile_dir', '') != profile_dir:
                                print(f"警告: 用户 {chrome_name} 的profile_dir不一致")
                                print(f"配置中: {profile.get('profile_dir', '')}")
                                print(f"实际值: {profile_dir}")
                                print(f"将使用实际值 {profile_dir} 启动浏览器")

                                # 更新profile对象中的profile_dir
                                profile['profile_dir'] = profile_dir

                                # 尝试更新配置文件
                                try:
                                    from config_manager import config_manager
                                    config = config_manager.get_config()
                                    if 'profiles' in config and profile_id in config['profiles']:
                                        config['profiles'][profile_id]['profile_dir'] = profile_dir
                                        config_manager.save_config()
                                        print(f"已更新配置文件中的profile_dir")
                                except Exception as e:
                                    print(f"更新配置文件失败: {e}")
                            break
                except Exception as e:
                    print(f"验证profile_dir时出错: {e}")

        # 使用(可能已更新的)profile启动浏览器
        return self.open_browser_with_profile(profile, headless)

    def _auto_extract_cookies(self):
        """自动提取所有用户的淘宝Cookie"""
        try:
            # 检查是否已经完成自动提取，避免重复执行
            if self._auto_extract_completed:
                print("自动Cookie提取已完成，跳过重复执行")
                return True

            print("开始自动提取淘宝Cookie...")

            # 导入cookie_extractor模块
            try:
                from cookie_extractor import get_chrome_cookies
            except ImportError as e:
                print(f"无法导入cookie_extractor模块: {e}")
                return False

            # 检查Chrome是否正在运行，如果是则先尝试关闭
            if self._is_chrome_running():
                print("检测到Chrome正在运行，尝试关闭Chrome进程...")
                try:
                    self._kill_chrome_processes()
                    import time
                    time.sleep(2)  # 等待进程完全关闭
                    print("Chrome进程已关闭")
                except Exception as e:
                    print(f"关闭Chrome进程失败: {e}")
                    print("将跳过自动Cookie提取，建议手动关闭Chrome后重试")
                    return False

            # 获取淘宝相关的Cookie
            cookies = get_chrome_cookies("taobao.com")
            print(f"从Chrome中提取到 {len(cookies)} 个淘宝Cookie")

            if not cookies:
                print("未找到任何淘宝Cookie")
                return False

            # 将Cookie匹配到对应的用户
            matched_cookies = self._match_cookies_to_users(cookies)
            print(f"成功匹配 {len(matched_cookies)} 个用户的Cookie")

            # 验证并更新Cookie到配置文件
            updated_count = self._update_cookies_to_config(matched_cookies)
            print(f"成功更新 {updated_count} 个用户的Cookie到配置文件")

            # 标记自动提取已完成
            self._auto_extract_completed = True
            return True

        except Exception as e:
            print(f"自动提取Cookie失败: {e}")
            traceback.print_exc()
            return False

    def _match_cookies_to_users(self, cookies):
        """将Cookie匹配到对应的Chrome用户"""
        matched_cookies = {}

        try:
            # 如果只有一个用户，直接分配所有Cookie
            if len(self.profiles) == 1:
                profile = self.profiles[0]
                cookie_string = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
                matched_cookies[profile['id']] = cookie_string
                print(f"单用户模式：将所有Cookie分配给用户 {profile['name']}")
                return matched_cookies

            # 多用户情况：尝试智能匹配
            # 策略1：按Cookie的域名和用户配置目录匹配
            for profile in self.profiles:
                profile_dir = profile.get('profile_dir', '')
                profile_name = profile.get('name', '')

                # 为每个用户收集相关的Cookie
                user_cookies = []

                # 简单策略：将所有淘宝Cookie分配给每个用户
                # 在实际使用中，用户可以通过手动获取来覆盖不正确的Cookie
                for cookie in cookies:
                    user_cookies.append(cookie)

                if user_cookies:
                    cookie_string = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in user_cookies])
                    matched_cookies[profile['id']] = cookie_string
                    print(f"为用户 {profile_name} 分配了 {len(user_cookies)} 个Cookie")

            return matched_cookies

        except Exception as e:
            print(f"匹配Cookie到用户失败: {e}")
            traceback.print_exc()
            return {}

    def _update_cookies_to_config(self, matched_cookies):
        """将匹配的Cookie更新到配置文件"""
        updated_count = 0

        try:
            from config_manager import config_manager

            for profile_id, cookie_string in matched_cookies.items():
                try:
                    # 验证Cookie是否有效
                    if self._validate_cookie(cookie_string):
                        # 获取用户配置
                        profile_data = config_manager.get_profile(profile_id)

                        # 更新Cookie和状态
                        profile_data['cookie'] = cookie_string
                        profile_data['status'] = '正常'
                        profile_data['last_check'] = time.strftime("%Y-%m-%d %H:%M:%S")
                        profile_data['auto_extracted'] = True  # 标记为自动提取

                        # 保存配置
                        config_manager.update_profile(profile_id, profile_data)
                        updated_count += 1

                        print(f"已更新用户 {profile_id} 的Cookie")
                    else:
                        print(f"用户 {profile_id} 的Cookie验证失败，跳过更新")

                except Exception as e:
                    print(f"更新用户 {profile_id} 的Cookie失败: {e}")

            # 保存配置文件
            if updated_count > 0:
                config_manager.save_config()
                print(f"配置文件已保存，共更新 {updated_count} 个用户")

            return updated_count

        except Exception as e:
            print(f"更新Cookie到配置文件失败: {e}")
            traceback.print_exc()
            return 0

    def _validate_cookie(self, cookie_string):
        """验证Cookie是否有效（简单验证）"""
        try:
            # 简单验证：检查Cookie是否包含必要的字段
            required_fields = ['_tb_token_', 'cookie2']

            for field in required_fields:
                if field not in cookie_string:
                    print(f"Cookie缺少必要字段: {field}")
                    return False

            # 检查Cookie长度（有效的淘宝Cookie通常比较长）
            if len(cookie_string) < 100:
                print("Cookie长度过短，可能无效")
                return False

            print("Cookie基本验证通过")
            return True

        except Exception as e:
            print(f"验证Cookie时出错: {e}")
            return False

    def _is_chrome_running(self):
        """检查Chrome是否正在运行"""
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    return True
            return False
        except ImportError:
            # 如果没有psutil，使用系统命令检查
            try:
                import subprocess
                if platform.system() == "Windows":
                    result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq chrome.exe'],
                                          capture_output=True, text=True)
                    return 'chrome.exe' in result.stdout
                else:
                    result = subprocess.run(['pgrep', 'chrome'], capture_output=True)
                    return result.returncode == 0
            except Exception:
                return False
        except Exception:
            return False

    def _kill_chrome_processes(self):
        """关闭所有Chrome进程"""
        try:
            import subprocess
            if platform.system() == "Windows":
                # Windows环境
                subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'],
                             capture_output=True, check=False)
                print("已尝试关闭所有Chrome进程")
            else:
                # Linux/Mac环境
                subprocess.run(['pkill', '-9', 'chrome'],
                             capture_output=True, check=False)
                print("已尝试关闭所有Chrome进程")
        except Exception as e:
            print(f"关闭Chrome进程时出错: {e}")
            raise