import os
import sys
import json
import traceback

class ConfigManager:
    """集中的配置管理器，处理配置文件的读取和保存"""

    _instance = None
    _config = None
    _config_path = None

    def __new__(cls):
        """单例模式，确保只有一个ConfigManager实例"""
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """初始化配置管理器，确定配置文件路径"""
        # 确定配置文件路径（只执行一次）
        if getattr(sys, 'frozen', False):
            # 打包后，使用程序所在目录
            exe_dir = os.path.dirname(sys.executable)
            self._config_path = os.path.join(exe_dir, 'config.json')
            print(f"配置管理器初始化 - 打包环境，配置文件路径: {self._config_path}")
        else:
            # 开发环境，使用当前目录
            self._config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.json')
            print(f"配置管理器初始化 - 开发环境，配置文件路径: {self._config_path}")

        # 加载配置文件
        self._load_config()

    def _load_config(self):
        """从文件加载配置"""
        try:
            if os.path.exists(self._config_path):
                try:
                    # 尝试使用utf-8编码读取
                    with open(self._config_path, 'r', encoding='utf-8') as f:
                        self._config = json.load(f)
                    print(f"配置已加载(UTF-8): {self._config_path}")
                except UnicodeDecodeError:
                    # 如果utf-8失败，尝试使用gbk编码
                    with open(self._config_path, 'r', encoding='gbk') as f:
                        self._config = json.load(f)
                    print(f"配置已加载(GBK): {self._config_path}")

                    # 将GBK编码的文件转换为UTF-8编码
                    print("检测到GBK编码，转换为UTF-8编码...")
                    self.save_config()
            else:
                # 创建默认配置
                self._config = self._create_default_config()
                self.save_config()
                print(f"创建了默认配置: {self._config_path}")
        except Exception as e:
            print(f"加载配置失败: {e}")
            traceback.print_exc()
            # 使用默认配置
            self._config = self._create_default_config()

    def _create_default_config(self):
        """创建默认配置"""
        # 尝试查找Chrome浏览器路径
        chrome_path = self._find_chrome_path()

        return {
            "chrome_driver_path": "",
            "chrome_path": chrome_path,  # 添加Chrome浏览器路径
            "start_date": "",
            "end_date": "",
            "interval": "",
            "max_threads": 1,
            "max_concurrent_browsers": 3,
            "retry_count": 3,
            "timeout": 30,
            "order_interval": 60,
            "confirm_interval": 60,
            "auto_review_enabled": False,
            "profiles": {}
        }

    def _find_chrome_path(self):
        """查找Chrome浏览器路径"""
        try:
            import platform
            import subprocess

            # 默认Chrome路径列表
            chrome_paths = []

            if platform.system() == "Windows":
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"),
                    # 添加特定路径
                    r"C:\Users\<USER>\.PyCharm2018.3\config\scratches\chrome\App\chrome.exe"
                ]
            elif platform.system() == "Darwin":  # macOS
                chrome_paths = [
                    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                    os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome")
                ]
            elif platform.system() == "Linux":
                chrome_paths = [
                    "/usr/bin/google-chrome",
                    "/usr/bin/chromium-browser",
                    "/usr/bin/chromium"
                ]

            # 检查路径是否存在
            for path in chrome_paths:
                if os.path.exists(path):
                    print(f"找到Chrome浏览器: {path}")
                    return path

            # 尝试使用where/which命令查找Chrome
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['where', 'chrome'], capture_output=True, text=True)
                    if result.returncode == 0 and result.stdout.strip():
                        path = result.stdout.strip().split('\n')[0]
                        print(f"通过where命令找到Chrome: {path}")
                        return path
                else:
                    result = subprocess.run(['which', 'google-chrome'], capture_output=True, text=True)
                    if result.returncode == 0 and result.stdout.strip():
                        path = result.stdout.strip()
                        print(f"通过which命令找到Chrome: {path}")
                        return path
            except Exception as e:
                print(f"查找Chrome命令失败: {e}")

            print("未找到Chrome浏览器路径")
            return ""
        except Exception as e:
            print(f"查找Chrome路径时出错: {e}")
            import traceback
            traceback.print_exc()
            return ""

    def get_config(self):
        """获取完整配置"""
        return self._config

    def get_value(self, key, default=None):
        """获取指定键的配置值"""
        return self._config.get(key, default)

    def set_value(self, key, value):
        """设置指定键的配置值"""
        self._config[key] = value

    def update_config(self, config_dict):
        """更新多个配置项"""
        self._config.update(config_dict)

    def save_config(self):
        """保存配置到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self._config_path), exist_ok=True)

            # 保存配置，确保中文正确保存
            with open(self._config_path, 'w', encoding='utf-8') as f:
                # 使用ensure_ascii=False确保中文字符不会被转义为ASCII编码
                json.dump(self._config, f, ensure_ascii=False, indent=4)

                # 确保文件末尾有换行符
                f.write('\n')


            # 验证保存的文件是否可以正确读取
            try:
                with open(self._config_path, 'r', encoding='utf-8') as f:
                    test_config = json.load(f)
            except Exception as e:
                print(f"警告：配置文件保存后无法正确读取: {e}")
                traceback.print_exc()

            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            traceback.print_exc()
            return False

    def generate_profile_id(self, profile_dir, index=0):
        """生成基于索引的配置文件标识符（旧格式）"""
        # 使用旧格式ID (profile_数字)
        return f"profile_{index}"

    def get_profile(self, profile_id_or_dir):
        """获取指定ID或目录的用户配置，优先使用旧格式ID"""
        if "profiles" not in self._config:
            self._config["profiles"] = {}

        # 直接查找完整ID
        if profile_id_or_dir in self._config["profiles"]:
            return self._config["profiles"][profile_id_or_dir]

        # 如果是旧格式(profile_数字)，直接返回
        if profile_id_or_dir.startswith("profile_"):
            if profile_id_or_dir in self._config["profiles"]:
                return self._config["profiles"][profile_id_or_dir]
            else:
                # 未找到匹配的配置，返回空对象
                return {}

        # 如果是新格式ID(chrome_开头)，尝试查找对应的旧格式ID
        if profile_id_or_dir.startswith("chrome_"):
            # 查看是否有映射关系
            if "profile_id_mapping" in self._config:
                for old_id, new_id in self._config["profile_id_mapping"].items():
                    if new_id == profile_id_or_dir and old_id in self._config["profiles"]:
                        return self._config["profiles"][old_id]

        # 如果是不带前缀的profile_dir，尝试查找对应的旧格式ID
        try:
            # 尝试从缓存中查找对应的旧ID
            from chrome_manager import get_chrome_profiles
            profiles = get_chrome_profiles()
            for profile in profiles:
                if profile.get('profile_dir', '') == profile_id_or_dir:
                    old_id = profile.get('id', '')
                    if old_id and old_id in self._config["profiles"]:
                        return self._config["profiles"][old_id]
        except Exception as e:
            print(f"通过profile_dir查找旧格式配置失败: {e}")

        # 未找到匹配的配置
        return {}

    def update_profile(self, profile_id_or_dir, profile_data):
        """更新指定ID或目录的用户配置，优先使用旧格式ID"""
        if "profiles" not in self._config:
            self._config["profiles"] = {}

        # 如果是旧格式ID，直接使用
        if profile_id_or_dir.startswith("profile_"):
            # 如果用户配置不存在，创建一个空配置
            if profile_id_or_dir not in self._config["profiles"]:
                self._config["profiles"][profile_id_or_dir] = {}

            # 更新用户配置
            self._config["profiles"][profile_id_or_dir].update(profile_data)
            return

        # 如果是新格式ID(chrome_开头)，尝试查找对应的旧格式ID
        if profile_id_or_dir.startswith("chrome_"):
            # 查看是否有映射关系
            if "profile_id_mapping" in self._config:
                for old_id, new_id in self._config["profile_id_mapping"].items():
                    if new_id == profile_id_or_dir:
                        # 找到对应的旧ID，使用旧ID更新
                        if old_id not in self._config["profiles"]:
                            self._config["profiles"][old_id] = {}
                        self._config["profiles"][old_id].update(profile_data)
                        return

            # 如果没有找到映射关系，尝试从Chrome配置中查找
            try:
                from chrome_manager import get_chrome_profiles
                profiles = get_chrome_profiles()
                for i, profile in enumerate(profiles):
                    chrome_id = profile.get('chrome_id', '')
                    if chrome_id == profile_id_or_dir:
                        # 找到匹配的Chrome配置，使用旧ID
                        old_id = profile.get('id', f'profile_{i}')
                        if old_id not in self._config["profiles"]:
                            self._config["profiles"][old_id] = {}
                        self._config["profiles"][old_id].update(profile_data)
                        return
            except Exception as e:
                print(f"通过新格式ID查找旧格式配置失败: {e}")

        # 如果是不带前缀的profile_dir，尝试查找对应的旧格式ID
        try:
            from chrome_manager import get_chrome_profiles
            profiles = get_chrome_profiles()
            for profile in profiles:
                if profile.get('profile_dir', '') == profile_id_or_dir:
                    old_id = profile.get('id', '')
                    if old_id:
                        # 找到匹配的Chrome配置，使用旧ID
                        if old_id not in self._config["profiles"]:
                            self._config["profiles"][old_id] = {}
                        self._config["profiles"][old_id].update(profile_data)
                        return
        except Exception as e:
            print(f"通过profile_dir查找旧格式配置失败: {e}")

        # 如果所有尝试都失败，使用原始ID
        if profile_id_or_dir not in self._config["profiles"]:
            self._config["profiles"][profile_id_or_dir] = {}
        self._config["profiles"][profile_id_or_dir].update(profile_data)

    def migrate_profiles(self):
        """禁用迁移功能，保持使用旧格式ID"""
        return False

    def get_config_path(self):
        """获取配置文件路径"""
        return self._config_path

# 创建全局配置管理器实例
config_manager = ConfigManager()
