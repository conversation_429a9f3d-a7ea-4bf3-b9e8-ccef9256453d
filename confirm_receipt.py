#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
淘宝自动确认收货脚本
独立于Flask应用，可以通过命令行直接启动
"""

import os,uuid,sys,json,time,random,re
import argparse
import threading
import subprocess
import traceback
import hashlib
import requests
from urllib.parse import quote
from DrissionPage import ChromiumPage, ChromiumOptions

# 禁用SSL警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 通知app.py更新用户密码错误状态
def notify_password_error(profile_id):
    """
    通知app.py更新用户密码错误状态

    Args:
        profile_id: 用户ID
    """
    try:
        # 构建请求URL
        # 如果是在打包环境中运行，使用localhost:8060
        # 如果是在开发环境中运行，使用localhost:5000
        port = 8060  # 默认使用8060端口

        # 尝试发送请求
        url = f"http://localhost:{port}/api/update_password_error"
        headers = {"Content-Type": "application/json"}
        data = {"profile_id": profile_id}

        # 发送请求
        response = requests.post(url, json=data, headers=headers, timeout=5)

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"成功通知app.py更新用户 {profile_id} 的密码错误状态")
            else:
                print(f"通知app.py失败: {result.get('message')}")
        else:
            print(f"通知app.py失败，状态码: {response.status_code}")

    except Exception as e:
        print(f"通知app.py更新用户密码错误状态失败: {e}")
        # 这里不抛出异常，避免影响主流程

# 通知app.py更新用户状态为正常
def notify_password_normal(profile_id):
    """
    通知app.py更新用户状态为正常

    Args:
        profile_id: 用户ID
    """
    try:
        # 构建请求URL
        # 如果是在打包环境中运行，使用localhost:8060
        # 如果是在开发环境中运行，使用localhost:5000
        port = 8060  # 默认使用8060端口

        # 尝试发送请求
        url = f"http://localhost:{port}/api/update_profile_status"
        headers = {"Content-Type": "application/json"}
        data = {"profile_id": profile_id, "status": "正常"}

        # 发送请求
        response = requests.post(url, json=data, headers=headers, timeout=5)

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"成功通知app.py更新用户 {profile_id} 的状态为正常")
            else:
                print(f"通知app.py失败: {result.get('message')}")
        else:
            print(f"通知app.py失败，状态码: {response.status_code}")

    except Exception as e:
        print(f"通知app.py更新用户状态失败: {e}")
        # 这里不抛出异常，避免影响主流程

# 通知app.py更新用户风控状态
def notify_risk_control(profile_id):
    """
    通知app.py更新用户风控状态

    Args:
        profile_id: 用户ID
    """
    try:
        # 构建请求URL
        # 如果是在打包环境中运行，使用localhost:8060
        # 如果是在开发环境中运行，使用localhost:5000
        port = 8060  # 默认使用8060端口

        # 尝试发送请求
        url = f"http://localhost:{port}/api/update_profile_status"
        headers = {"Content-Type": "application/json"}
        data = {"profile_id": profile_id, "status": "风控"}

        # 发送请求
        response = requests.post(url, json=data, headers=headers, timeout=5)

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"成功通知app.py更新用户 {profile_id} 的状态为风控")
            else:
                print(f"通知app.py失败: {result.get('message')}")
        else:
            print(f"通知app.py失败，状态码: {response.status_code}")

    except Exception as e:
        print(f"通知app.py更新用户风控状态失败: {e}")
        # 这里不抛出异常，避免影响主流程

# 通知app.py更新任务计数
def notify_task_counts(profile_id, total_count, completed_count, skipped_count):
    """
    通知app.py更新任务计数

    Args:
        profile_id: 用户ID
        total_count: 总订单数
        completed_count: 已完成订单数
        skipped_count: 已跳过订单数
    """
    try:
        # 构建请求URL
        port = 8060  # 默认使用8060端口

        # 尝试发送请求
        url = f"http://localhost:{port}/api/update_task_counts"
        headers = {"Content-Type": "application/json"}
        data = {
            "profile_id": profile_id,
            "total_count": total_count,
            "completed_count": completed_count,
            "skipped_count": skipped_count
        }

        # 发送请求
        response = requests.post(url, json=data, headers=headers, timeout=5)

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"成功通知app.py更新任务计数: 总数={total_count}, 已完成={completed_count}, 已跳过={skipped_count}")
            else:
                print(f"通知app.py更新任务计数失败: {result.get('message')}")
        else:
            print(f"通知app.py更新任务计数失败，状态码: {response.status_code}")

    except Exception as e:
        print(f"通知app.py更新任务计数失败: {e}")
        # 这里不抛出异常，避免影响主流程

# 创建全局锁，用于K宝/K令设备操作的排队机制
k_device_lock = threading.Lock()

# 设置自动评价功能可用
AUTO_REVIEW_AVAILABLE = True
print("自动评价功能已启用")

# 老版本自动评价相关函数
def goToken(cookie):
    """获取淘宝API所需的token"""
    t = str(int(time.time()))
    date = '{}'
    xapi = 'mtop.tmall.kangaroo.core.service.route.aldlampservicefixedresv2'
    xv = '1.0'
    token = ''
    str1 = token + '&' + t + '&12574478&' + date
    str2 = bytes(str1, encoding='utf-8')  # md5
    sign = hashlib.md5(str2).hexdigest()
    data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=2019@weex_h5_0.12.14&data=' + str(
        quote(date, 'utf-8'))
    url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
    head = {
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
        'cookie': cookie,
        'url': url2
    }
    try:
        r = requests.get(url2, timeout=20, headers=head, stream=False, verify=False)
        if r.text.find('为空') != -1 or r.text.find('过期') != -1:
            set_cookie = str(r.headers.get('Set-Cookie'))
            mk = '_m_h5_tk=' + set_cookie.split('_m_h5_tk=')[1].split(';')[0] + ';'
            enc = '_m_h5_tk_enc=' + set_cookie.split('_m_h5_tk_enc=')[1].split(';')[0] + ';'
            return mk + enc
        else:
            return ''
    except Exception as e:
        print(f"获取token失败: {e}")
        return ''

def to_comment(final_params, cookie):
    """提交评价到淘宝服务器"""
    try:
        # 使用json模块正确序列化数据，避免编码问题
        date = json.dumps(final_params)
        xapi = 'mtop.taobao.rate.component.publish'
        xv = '1.0'
        t = str(int(time.time()))
        token = cookie.split('_m_h5_tk=')[1].split('_')[0]
        str1 = token + '&' + t + '&12574478&' + date
        str2 = bytes(str1, encoding='utf-8')  # md5
        sign = hashlib.md5(str2).hexdigest()
        data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=700170@taobao_android_10.27.10'
        url2 = 'https://guide-acs.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
        head = {
            'user-agent': 'MTOPSDK/3.1.1.7+(Android;11;Google;Pixel+4a)',
            'cookie': cookie,
            'Content-type': 'application/x-www-form-urlencoded; charset=utf-8'
        }
        # 使用json参数而不是data参数，让requests自动处理JSON序列化和编码
        result = requests.post(url2, timeout=20, data={'data': date}, headers=head, verify=False)
        return result.json()
    except Exception as e:
        print(f"提交评价失败: {e}")
        return {"ret": ["FAILED::提交评价失败"]}
def extract_upload_params_from_cookie(cookie):
    """
    从cookie中提取上传所需的参数

    Args:
        cookie: 完整的cookie字符串

    Returns:
        dict: 包含_tb_token_等参数的字典
    """
    try:
        params = {}

        # 提取_tb_token_
        if '_tb_token_=' in cookie:
            token_part = cookie.split('_tb_token_=')[1]
            if ';' in token_part:
                params['_tb_token_'] = token_part.split(';')[0]
            else:
                params['_tb_token_'] = token_part

        return params
    except Exception as e:
        print(f"提取上传参数失败: {e}")
        return {}

def extract_seller_and_order_info(json_data, order_id):
    """
    从评价API响应中提取卖家ID和订单信息

    Args:
        json_data: 淘宝API返回的JSON数据
        order_id: 订单ID

    Returns:
        dict: 包含sellerNumId和bizOrderId的字典
    """
    try:
        info = {
            'bizOrderId': order_id,
            'sellerNumId': ''
        }

        # 尝试从多个可能的位置提取卖家ID
        for key, value in json_data.items():
            if key.startswith("itemRateContainer"):
                fields = value.get("fields", {})

                # 尝试从basicInfo中获取卖家ID
                basic_info = fields.get("basicInfo", {})
                if "sellerId" in basic_info:
                    info['sellerNumId'] = str(basic_info["sellerId"])
                    break
                elif "sellerNumId" in basic_info:
                    info['sellerNumId'] = str(basic_info["sellerNumId"])
                    break

                # 尝试从item信息中获取
                item_info = fields.get("item", {})
                if "sellerId" in item_info:
                    info['sellerNumId'] = str(item_info["sellerId"])
                    break
                elif "sellerNumId" in item_info:
                    info['sellerNumId'] = str(item_info["sellerNumId"])
                    break

        return info
    except Exception as e:
        print(f"提取卖家和订单信息失败: {e}")
        return {'bizOrderId': order_id, 'sellerNumId': ''}

def upload_image_to_platform(image_path, cookie, seller_order_info=None):
    """
    上传图片到淘宝平台并返回图片URL

    Args:
        image_path: 本地图片文件路径
        cookie: 完整的cookie字符串
        seller_order_info: 包含sellerNumId和bizOrderId的字典

    Returns:
        str: 上传后的图片URL，如果上传失败返回空字符串
    """
    try:
        import requests
        import os

        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"图片文件不存在: {image_path}")
            return ""

        # 提取上传参数
        upload_params = extract_upload_params_from_cookie(cookie)
        if not upload_params.get('_tb_token_'):
            print("无法从cookie中提取_tb_token_，跳过图片上传")
            return ""

        # 如果没有提供卖家订单信息，使用默认值
        if not seller_order_info:
            seller_order_info = {'sellerNumId': '', 'bizOrderId': ''}

        # 构建上传URL
        upload_url = "https://rate.taobao.com/upload_pic.htm?_input_charset=utf-8&at_iframe=1"

        # 准备文件数据
        filename = os.path.basename(image_path)

        # 构建multipart/form-data
        files = {
            'picture': (filename, open(image_path, 'rb'), 'image/png')
        }

        data = {
            'sellerNumId': seller_order_info.get('sellerNumId', ''),
            'bizOrderId': seller_order_info.get('bizOrderId', ''),
            '_tb_token_': upload_params['_tb_token_']
        }

        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': '*/*',
            'Origin': 'https://rate.taobao.com',
            'Referer': 'https://rate.taobao.com/',
            'Cookie': cookie
        }

        print(f"开始上传图片: {image_path}")
        print(f"上传参数: sellerNumId={data['sellerNumId']}, bizOrderId={data['bizOrderId']}")

        # 发送上传请求
        response = requests.post(
            upload_url,
            files=files,
            data=data,
            headers=headers,
            timeout=30,
            verify=False
        )

        # 关闭文件
        files['picture'][1].close()

        # 检查响应
        if response.status_code == 200:
            # 尝试解析响应获取图片URL
            response_text = response.text
            print(f"上传响应: {response_text[:200]}...")

            # 淘宝图片上传成功后通常返回JSON格式，包含图片URL
            try:
                import json
                import re

                if response_text.strip():
                    # 尝试解析JSON响应
                    if response_text.startswith('{'):
                        result = json.loads(response_text)

                        # 优先处理淘宝标准响应格式
                        if 'thumbnail' in result and result['thumbnail']:
                            # 从thumbnail字段获取图片URL
                            thumbnail_url = result['thumbnail']

                            # 确保URL有协议前缀
                            if thumbnail_url.startswith('//'):
                                thumbnail_url = 'https:' + thumbnail_url
                            elif not thumbnail_url.startswith('http'):
                                thumbnail_url = 'https://' + thumbnail_url

                            # 移除尺寸后缀（如_40x40.jpg, _80x80.png等）
                            # 匹配模式：_数字x数字.扩展名
                            clean_url = re.sub(r'_\d+x\d+\.(jpg|jpeg|png|gif)$', '', thumbnail_url)

                            # 如果移除后缀后URL不完整，尝试重建
                            if not clean_url.endswith(('.jpg', '.jpeg', '.png', '.gif')):
                                # 从原URL中提取文件扩展名
                                original_ext_match = re.search(r'\.([a-zA-Z]+)_\d+x\d+\.(jpg|jpeg|png|gif)$', thumbnail_url)
                                if original_ext_match:
                                    original_ext = original_ext_match.group(1)
                                    clean_url = clean_url + '.' + original_ext
                                else:
                                    # 如果无法确定原始扩展名，使用png
                                    clean_url = clean_url + '.png'

                            print(f"图片上传成功: {clean_url}")
                            return clean_url

                        # 备用方案：其他可能的字段
                        elif 'url' in result:
                            image_url = result['url']
                            print(f"图片上传成功: {image_url}")
                            return image_url
                        elif 'data' in result and 'url' in result['data']:
                            image_url = result['data']['url']
                            print(f"图片上传成功: {image_url}")
                            return image_url
                        elif 'picUrl' in result:
                            image_url = result['picUrl']
                            print(f"图片上传成功: {image_url}")
                            return image_url

                    # 如果不是JSON格式，尝试从HTML/文本中提取URL
                    # 匹配淘宝/阿里云的图片URL格式
                    url_patterns = [
                        r'https?://[^"\s<>]*\.alicdn\.com/[^"\s<>]*\.(?:jpg|jpeg|png|gif)',
                        r'https?://[^"\s<>]*\.taobao\.com/[^"\s<>]*\.(?:jpg|jpeg|png|gif)',
                        r'https?://[^"\s<>]*gsnapshot\.alicdn\.com/[^"\s<>]*',
                        r'https?://[^"\s<>]*img\.alicdn\.com/[^"\s<>]*',
                        r'"url"\s*:\s*"([^"]*)"',
                        r'"picUrl"\s*:\s*"([^"]*)"'
                    ]

                    for pattern in url_patterns:
                        matches = re.findall(pattern, response_text)
                        if matches:
                            # 取第一个匹配的URL
                            image_url = matches[0]
                            # 如果是从JSON字段中提取的，可能需要处理转义字符
                            image_url = image_url.replace('\\/', '/')
                            print(f"从响应中提取到图片URL: {image_url}")
                            return image_url

                print("上传响应中未找到图片URL，使用默认URL")
                # 如果无法提取URL，返回一个默认的示例URL
                return "https://gsnapshot.alicdn.com/imgextra/i2/2217598556784/O1CN01pfPijY1zz7xvti4dL_!!2217598556784.png"

            except Exception as parse_error:
                print(f"解析上传响应失败: {parse_error}")
                # 发生错误时也返回默认URL，确保评价流程能继续
                return "https://gsnapshot.alicdn.com/imgextra/i2/2217598556784/O1CN01pfPijY1zz7xvti4dL_!!2217598556784.png"
        else:
            print(f"图片上传失败，HTTP状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            # 上传失败时返回默认URL，确保评价流程能继续
            return "https://gsnapshot.alicdn.com/imgextra/i2/2217598556784/O1CN01pfPijY1zz7xvti4dL_!!2217598556784.png"

    except Exception as e:
        print(f"上传图片时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return ""

def test_url_processing():
    """测试URL处理逻辑"""
    import re

    # 测试用例
    test_cases = [
        "//img.alicdn.com/imgextra/i4/4611686018427387244/O1CN01aeICIt1qV3QLF1Fi0_!!4611686018427387244-2-rate.png_40x40.jpg",
        "//img.alicdn.com/imgextra/i4/test.jpg_80x80.png",
        "//img.alicdn.com/imgextra/i4/test.png_120x120.jpg"
    ]

    for test_url in test_cases:
        # 确保URL有协议前缀
        if test_url.startswith('//'):
            full_url = 'https:' + test_url

        # 移除尺寸后缀
        clean_url = re.sub(r'_\d+x\d+\.(jpg|jpeg|png|gif)$', '', full_url)

        # 如果移除后缀后URL不完整，尝试重建
        if not clean_url.endswith(('.jpg', '.jpeg', '.png', '.gif')):
            # 从原URL中提取文件扩展名
            original_ext_match = re.search(r'\.([a-zA-Z]+)_\d+x\d+\.(jpg|jpeg|png|gif)$', full_url)
            if original_ext_match:
                original_ext = original_ext_match.group(1)
                clean_url = clean_url + '.' + original_ext
            else:
                # 如果无法确定原始扩展名，使用png
                clean_url = clean_url + '.png'

        print(f"原始URL: {test_url}")
        print(f"处理后URL: {clean_url}")
        print("---")

# 如果直接运行此脚本，执行测试
if __name__ == "__main__":
    test_url_processing()

def scan_image_directory(image_directory):
    """
    扫描图片目录，按商品ID组织图片文件

    Args:
        image_directory: 图片目录路径

    Returns:
        dict: 商品ID到图片文件列表的映射
              格式: {商品ID: [图片文件路径1, 图片文件路径2, ...]}
    """
    if not image_directory or not os.path.exists(image_directory):
        return {}

    image_map = {}

    try:
        # 支持的图片格式
        supported_formats = ('.png', '.jpg', '.jpeg')

        # 遍历目录中的所有文件
        for filename in os.listdir(image_directory):
            if not filename.lower().endswith(supported_formats):
                continue

            # 提取文件名（不含扩展名）
            name_without_ext = os.path.splitext(filename)[0]

            # 解析商品ID
            if '-' in name_without_ext:
                # 多张图片格式：商品ID-序号
                goods_id = name_without_ext.split('-')[0]
            else:
                # 单张图片格式：商品ID
                goods_id = name_without_ext

            # 构建完整文件路径
            file_path = os.path.join(image_directory, filename)

            # 添加到映射中
            if goods_id not in image_map:
                image_map[goods_id] = []
            image_map[goods_id].append(file_path)

        # 对每个商品的图片列表按文件名排序
        for goods_id in image_map:
            image_map[goods_id].sort()

        print(f"扫描图片目录 {image_directory}，找到 {len(image_map)} 个商品的图片")
        for goods_id, files in image_map.items():
            print(f"  商品ID {goods_id}: {len(files)} 张图片")

    except Exception as e:
        print(f"扫描图片目录失败: {e}")
        return {}

    return image_map

def extract_goods_id_from_api_response(json_data):
    """
    从淘宝API响应中提取商品ID

    Args:
        json_data: 淘宝API返回的JSON数据

    Returns:
        str: 商品ID，如果提取失败返回空字符串
    """
    try:
        # 尝试从多个可能的位置提取商品ID

        # 方法1: 从itemRateContainer中提取
        for key, value in json_data.items():
            if key.startswith("itemRateContainer"):
                # 尝试从fields中提取商品信息
                fields = value.get("fields", {})

                # 尝试从item信息中获取
                item_info = fields.get("item", {})
                if "itemId" in item_info:
                    return str(item_info["itemId"])

                # 尝试从basicInfo中获取
                basic_info = fields.get("basicInfo", {})
                if "itemId" in basic_info:
                    return str(basic_info["itemId"])

                # 尝试从其他可能的字段获取
                for field_name in ["goodsId", "productId", "skuId"]:
                    if field_name in fields:
                        return str(fields[field_name])

        # 方法2: 从其他字段中查找
        # 这里可以根据实际API响应结构添加更多提取逻辑

        print("未能从API响应中提取到商品ID")
        return ""

    except Exception as e:
        print(f"提取商品ID失败: {e}")
        return ""

def create_image_data(image_url, goods_id, pub_session=None):
    """创建完整的图片数据结构，与淘宝评价系统完全兼容 - 精确匹配实际POST数据格式"""
    if not pub_session:
        pub_session = str(uuid.uuid4())

    # 确保URL格式正确，移除多余空格
    image_url = image_url.replace(" ", "")

    # 按照实际POST数据的精确格式构建statInfo
    # 注意：这里的转义层级必须与实际数据完全匹配

    # 第一层：构建最内层的配置JSON字符串
    music_frame_config_str = json.dumps({
        "type": "music",
        "frameResolutionSize": 256,
        "frameCount": 2,
        "frameInterval": 0.5,
        "frameQuality": 50,
        "frameDecodeTimeout": 1000,
        "frameUploadTimeout": 1500
    }, separators=(',', ':'))

    topic_frame_config_str = json.dumps({
        "type": "topic",
        "frameResolutionSize": 256,
        "frameCount": 5,
        "frameInterval": 0.2,
        "frameQuality": 70,
        "frameDecodeTimeout": 5000,
        "frameUploadTimeout": 4000
    }, separators=(',', ':'))

    # 第二层：构建ab_test_info JSON字符串
    ab_test_info_str = json.dumps({
        "musicFrameConfig": music_frame_config_str,
        "topicFrameConfig": topic_frame_config_str
    }, separators=(',', ':'))

    # 第三层：构建完整的statInfo结构（按实际数据字段顺序）
    stat_info = {
        "ab_test_info": ab_test_info_str,
        "camera_rotation": 0,
        "filter": [{}],
        "fun_id": {},
        "goods_id": str(goods_id),
        "is_hq_record": False,
        "itemsticker_items": [],
        "pub_session": pub_session,
        "source": "user_record",
        "additionalInfo": {
            "imageSource": "1",
            "containExif": False,
            "isScreenshot": False,
            "OS": "Android"
        }
    }

    # 返回完整的图片数据结构
    return {
        "fileSourceTag": "taobao_camera",
        "height": 1440,
        "statInfo": json.dumps(stat_info, separators=(',', ':')),
        "url": image_url,
        "width": 1080
    }
def fetch_rate_detail(order_id, cookie, comment_text, image_directory=""):
    date = '{"dinamicOpen": "True","pageType": "taobaoTotalPublishRate","orderId":"' + order_id + '","platformType": "wireless","asac": "2A23A16I9YC7QUVMJ8EH2I"}'
    xapi = 'mtop.taobao.rate.component.render'
    xv = '1.0'
    t = str(int(time.time()))
    token = cookie.split('_m_h5_tk=')[1].split('_')[0]
    str1 = token + '&' + t + '&12574478&' + date
    str2 = bytes(str1, encoding='utf-8')  # md5
    sign = hashlib.md5(str2).hexdigest()
    data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=10022339@ltao_android_10.39.17&data=' + str(
        quote(date, 'utf-8'))
    url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
    head = {
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
        'cookie': cookie}
    result = requests.get(url2, timeout=20, headers=head, stream=False, verify=False).json()
    submit_params = {
        "data": {},
        "hierarchy": result["data"]["hierarchy"],
        "linkage": result["data"]["linkage"]
    }
    json_data = result["data"]["data"]
    tem_list = {}
    order_length = 0
    order = ""
    # 遍历 json_data 的键值对
    for k, v in json_data.items():
        if k.startswith("itemRateContainer"):
            submit_params["data"][k] = v
            order_length += 1
        elif k.startswith("rateRoot_"):
            submit_params["data"][k] = v
        elif k.startswith("mainOrderScore"):
            if not order:
                match = re.search(r'mainOrderScore_(\d+)_\d+_\d+', k)
                if match:
                    order = match.group(1)
            tem_list[k] = v
        elif (k.startswith("text_") or k.startswith("tmallSatisficationScore_") or
              k.startswith("recordOnionShow_") or k.startswith("structRate_") or
              k.startswith("tbGnbRate_") or k.startswith("ugcPublicAndPublishSKS_")):
            tem_list[k] = v

    # 遍历临时列表 tem_list 的键值对
    for k, v in tem_list.items():
        if k.startswith("mainOrderScore_") or k.startswith("tmallSatisficationScore_"):
            v["fields"]["darkMode"] = '0'
            v["fields"]["darkModel"] = '0'
            v["fields"]["orderId"] = order
            v["fields"]["rateStatus"] = 0
            v["fields"]["starValue"] = 5
            if order_length == 1:
                v["fields"]["groupIndex"] = k[-1]
                v["fields"]["groupsEnable"] = '1'
            v["fields"]["rateType"] = '0' if order_length == 1 else '1'
            submit_params["data"][k] = v
        elif k.startswith("recordOnionShow_"):
            v["fields"]["trackInfo"]["exposure"]["args"]["trackId"] = order
            if order_length == 1:
                v["fields"]["groupIndex"] = '0'
                v["fields"]["groupsEnable"] = '1'

            # 图片处理逻辑
            if image_directory:
                # 如果指定了图片目录，尝试匹配商品图片
                print(f"检查图片目录: {image_directory}")

                # 扫描图片目录
                image_map = scan_image_directory(image_directory)

                # 从API响应中提取商品ID
                goods_id = extract_goods_id_from_api_response(json_data)

                if goods_id and goods_id in image_map:
                    # 找到匹配的图片
                    image_files = image_map[goods_id]
                    print(f"为商品ID {goods_id} 找到 {len(image_files)} 张图片")

                    # 提取卖家和订单信息用于图片上传
                    seller_order_info = extract_seller_and_order_info(json_data, order_id)

                    # 创建图片数据列表
                    images_data = []
                    for image_file in image_files:
                        # 上传图片并获取URL（使用真实的上传函数）
                        image_url = upload_image_to_platform(image_file, cookie, seller_order_info)
                        if image_url:
                            images_data.append(create_image_data(image_url, goods_id))
                        else:
                            print(f"图片 {image_file} 上传失败，跳过此图片")
                    print(images_data)
                    input(111111111)
                    if images_data:
                        v["fields"]['images'] = images_data
                        print(f"成功为商品ID {goods_id} 添加了 {len(images_data)} 张图片")
                    else:
                        print(f"商品ID {goods_id} 的所有图片上传失败，跳过图片评价")
                        # 不添加images字段，进行纯文字评价
                else:
                    if goods_id:
                        print(f"未找到商品ID {goods_id} 对应的图片文件")
                    else:
                        print("未能从API响应中提取到商品ID")
                    # 不添加images字段，进行纯文字评价
            else:
                # 未指定图片目录，跳过图片评价
                print("未指定图片目录，跳过图片评价")
                # 不添加images字段，进行纯文字评价

            submit_params["data"][k] = v
        elif k.startswith("structRate_"):
            v["fields"]["content"] = comment_text
            tags = v["fields"].get("tags")
            if tags:
                v["fields"]["trackInfo"]["exposure"]["args"]["struct_tag_title"] = tags[0]["title"]
                v["fields"]["trackInfo"]["exposure"]["args"]["struct_tag_id"] = tags[0]["id"]
            if order_length == 1:
                v["fields"]["groupIndex"] = '0'
                v["fields"]["groupsEnable"] = '1'
            v["fields"]["tags"] = []
            submit_params["data"][k] = v
        elif k.startswith("tbGnbRate_"):
            v["fields"]["darkMode"] = '0'
            v["fields"]["darkModel"] = '0'
            v["fields"]["trackInfo"]["exposure"]["args"]["trackId"] = order
            v["fields"]["orderId"] = order
            v["fields"]["rateStatus"] = 0
            v["fields"]["selectedValue"] = "1"
            v["fields"]["rateType"] = '0' if order_length == 1 else '1'
            if order_length == 1:
                v["fields"]["groupIndex"] = '1'
                v["fields"]["groupsEnable"] = '1'
            submit_params["data"][k] = v
        elif k.startswith("ugcPublicAndPublishSKS_"):
            v["fields"]["isAnonymous"] = True
            if order_length == 1:
                v["fields"]["groupIndex"] = '0'
                v["fields"]["groupsEnable"] = '1'
            v["fields"]["darkMode"] = '0'
            v["fields"]["darkModel"] = '0'
            v["fields"]["orderId"] = order
            v["fields"]["rateStatus"] = 0
            v["fields"]["rateType"] = '0' if order_length == 1 else '1'
            submit_params["data"][k] = v
        elif k.startswith("text_"):
            v["fields"]["data"]["text"] = comment_text
            submit_params["data"][k] = v
        else:
            submit_params["data"][k] = v

    final_params = {"submit": submit_params, "asac": "2A23A16I9YC7QUVMJ8EH2I"}
    get_rate = to_comment(final_params, cookie)
    return get_rate
def extract_specific_fields2(data): # 提交批量付款前获取所有待付款订单
    """
    提取data.data下第一层以'item_'开头的节点中的特定字段。
    过滤重复的orderId。
    """
    result = []
    # 用于存储已经处理过的orderId
    processed_order_ids = set()

    # 直接处理data.data下的第一层节点
    for key, value in data.get("data", {}).get("data", {}).items():
        if key.startswith("item_"):
            # 获取orderId
            order_id = value.get("fields", {}).get("basicInfo", {}).get("orderId")

            # 如果orderId已存在或为None，跳过此项
            if not order_id or order_id in processed_order_ids:
                continue

            # 提取特定字段
            item_data = {
                "orderId": order_id,
                "promotion": value.get("fields", {}).get("item", {}).get("priceInfo", {}).get("promotion"),
                "title": value.get("fields", {}).get("item", {}).get("title"),
                "status": value.get("fields", {}).get("queryParams", {}).get("status"),
                "shopName": value.get("fields", {}).get("queryParams", {}).get("shopName")
            }

            # 添加orderId到已处理集合中
            processed_order_ids.add(order_id)
            # 添加数据到结果列表
            result.append(item_data)


    return result
def extract_specific_fields(data):
    """
    提取data.data下第一层以'item_'开头的节点中的特定字段。
    过滤重复的orderId。
    """
    result = []
    # 用于存储已经处理过的orderId
    processed_order_ids = set()

    # 直接处理data.data下的第一层节点
    for key, value in data.get("data", {}).get("data", {}).items():
        if key.startswith("item_"):
            # 获取orderId
            order_id = value.get("fields", {}).get("basicInfo", {}).get("orderId")

            # 如果orderId已存在或为None，跳过此项
            if not order_id or order_id in processed_order_ids:
                continue

            # 提取特定字段
            item_data = {
                "orderId": order_id,
                "promotion": value.get("fields", {}).get("item", {}).get("priceInfo", {}).get("promotion"),
                "title": value.get("fields", {}).get("item", {}).get("title"),
                "status": value.get("fields", {}).get("queryParams", {}).get("status"),
                "shopName": value.get("fields", {}).get("queryParams", {}).get("shopName")
            }

            # 添加orderId到已处理集合中
            processed_order_ids.add(order_id)
            # 添加数据到结果列表
            result.append(item_data)

    return result
def task_rate(ck_str, beginTime, endTime, commnt_text, window=None, image_directory=""):
    """获取待评价订单并进行评价"""
    try:
        cookie = ck_str
        if str(cookie).find('_m_h5_tk') == -1:  # 不带_m_h5_tk
            h5 = goToken(cookie)
            cookie = h5 + cookie

        # 获取待评价订单
        result = []
        for i in range(10):  # 最多获取10页
            t = str(int(time.time()))
            date = '{"OrderType":"OrderFilter","appName":"tborder","appVersion":"3.0","condition":"{\\"categoryText\\":[null],\\"filterSelectInfo\\":{\\"isFilterResult\\":true,\\"selectedCategoryIndex\\":\\"-1\\",\\"selectedEndTime\\":\\"\\",\\"selectedEndTimeIndex\\":\\"1\\",\\"selectedGiftIndex\\":\\"-1\\",\\"selectedSourceIndex\\":\\"-1\\",\\"selectedStartTime\\":\\"\\",\\"selectedStartTimeIndex\\":\\"1\\",\\"selectedTimeIndex\\":\\"-1\\"},\\"onlyGiftFilter\\":\\"false\\",\\"orderFilterExtParam\\":{\\"beginTime\\":\\"\\",\\"endTime\\":\\"\\",\\"filterSource\\":\\"\\",\\"giftType\\":\\"\\"},\\"version\\":\\"1.0.0\\",\\"wordType\\":\\"3\\"}","page":"' + str(
            i + 1) + '","tabCode":"waitRate","templateConfigVersion":"0"}'
            xapi = 'mtop.taobao.order.queryboughtlistv2'
            xv = '1.0'
            token = cookie.split('_m_h5_tk=')[1].split('_')[0]
            str1 = token + '&' + t + '&12574478&' + date
            str2 = bytes(str1, encoding='utf-8')  # md5
            sign = hashlib.md5(str2).hexdigest()
            data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&type=json&ttid=700170@taobao_android_10.27.10&dataType=json&data=' + str(
                quote(date, 'utf-8'))
            url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
            head = {
                'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
                'cookie': cookie
            }

            # 发送请求获取待评价订单
            r = requests.get(url2, timeout=20, headers=head, stream=False, verify=False)
            result.extend(extract_specific_fields(r.json()))

        # 处理评价内容
        # 确保评价内容是列表格式
        if isinstance(commnt_text, str):
            # 如果是字符串，检查是否包含换行符
            if '\n' in commnt_text:
                # 按换行符分割成列表
                commnt_text_list = commnt_text.split('\n')
                # 过滤掉空行
                commnt_text_list = [text.strip() for text in commnt_text_list if text.strip()]
                print(f"评价内容字符串按换行符分割，得到 {len(commnt_text_list)} 条评价")
            else:
                # 单行字符串，直接放入列表
                commnt_text_list = [commnt_text]
        elif isinstance(commnt_text, list):
            # 已经是列表，直接使用
            commnt_text_list = commnt_text
        else:
            # 其他类型，使用默认评价
            commnt_text_list = ["宝贝收到了，很满意，谢谢卖家！"]
            print("评价内容格式不正确，使用默认评价")

        # 打印评价内容列表（调试用）
        print(f"评价内容列表包含 {len(commnt_text_list)} 条评价")

        # 处理每个订单
        for i in result:
            orderId = i['orderId']
            # 随机选择一条评价内容
            commnt_text2 = random.choice(commnt_text_list)
            print(f"为订单 {orderId} 随机选择评价内容: {commnt_text2[:20]}...")
            res = fetch_rate_detail(orderId, cookie, commnt_text2, image_directory)
            status = '成功' if res.get('ret', [''])[0] == 'SUCCESS::调用成功' else '失败'
            print(f"订单 {orderId} 评价{status}")
            time.sleep(1)
    except Exception as e:
        print(f"自动评价过程出错: {e}")
        traceback.print_exc()
        return False

# 兼容旧版本的auto_review_after_confirm函数
def auto_review_after_confirm(cookie, order_ids, comment_texts, window=None, image_directory=""):
    """确认收货后自动评价的主函数 - 兼容旧版本接口"""
    # 处理单个订单号的情况
    if isinstance(order_ids, str):
        order_ids = [order_ids]

    # 处理评价内容
    # 如果是字符串，检查是否包含换行符
    if isinstance(comment_texts, str):
        if '\n' in comment_texts:
            # 按换行符分割成列表
            comment_texts_list = comment_texts.split('\n')
            # 过滤掉空行
            comment_texts_list = [text.strip() for text in comment_texts_list if text.strip()]
            print(f"评价内容字符串按换行符分割，得到 {len(comment_texts_list)} 条评价")
        else:
            # 单行字符串，直接放入列表
            comment_texts_list = [comment_texts]
    elif isinstance(comment_texts, list):
        # 已经是列表，直接使用
        comment_texts_list = comment_texts
    else:
        # 其他类型，使用默认评价
        comment_texts_list = ["宝贝收到了，很满意，谢谢卖家！"]
        print("评价内容格式不正确，使用默认评价")

    # 随机选择一条评价内容
    comment_text = random.choice(comment_texts_list)
    print(f"随机选择评价内容: {comment_text[:20]}...")

    # 确保cookie包含必要的token
    if str(cookie).find('_m_h5_tk') == -1:  # 不带_m_h5_tk
        h5 = goToken(cookie)
        cookie = h5 + cookie
        if not h5:
            return {"success": False, "message": "获取token失败，请检查cookie是否有效"}

    # 处理每个订单
    success_count = 0
    fail_count = 0
    results = []

    for order_id in order_ids:
        try:
            # 获取评价参数
            res = fetch_rate_detail(order_id, cookie, comment_text, image_directory)
            if not res:
                result = {"success": False, "message": f"获取订单 {order_id} 评价详情失败"}
                results.append(result)
                fail_count += 1
                continue

            # 检查评价结果
            if res.get('ret', [''])[0] == 'SUCCESS::调用成功':
                result = {"success": True, "message": f"订单 {order_id} 评价成功"}
                success_count += 1
            else:
                result = {"success": False, "message": f"订单 {order_id} 评价失败: {res.get('ret', ['未知错误'])[0]}"}
                fail_count += 1

            results.append(result)

            # 随机延迟，避免操作过快
            time.sleep(1.5)
        except Exception as e:
            error_msg = f"评价订单 {order_id} 时发生错误: {str(e)}"
            print(error_msg)
            results.append({"success": False, "message": error_msg})
            fail_count += 1

    # 返回批量评价结果
    summary = {
        "success": success_count > 0,
        "message": f"评价完成: {success_count}个成功, {fail_count}个失败",
        "success_count": success_count,
        "fail_count": fail_count,
        "details": results
    }

    return summary

# 确保当前目录在sys.path中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 全局变量，用于存储配置文件路径
CONFIG_PATH = None

def get_config_path():
    """获取配置文件路径"""
    global CONFIG_PATH

    # 如果已经设置了配置文件路径，直接返回
    if CONFIG_PATH and os.path.exists(CONFIG_PATH):
        return CONFIG_PATH

    # 否则使用默认路径
    if getattr(sys, 'frozen', False):
        # 打包后，使用程序所在目录
        exe_dir = os.path.dirname(sys.executable)
        config_path = os.path.join(exe_dir, 'config.json')
    else:
        # 开发环境，使用当前目录
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.json')
    return config_path

def load_config():
    """加载配置文件"""
    config_path = get_config_path()
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        else:
            return {}
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {}

def get_cookie_from_config(profile_id):
    """
    从配置文件中直接获取指定profile_id的cookie

    Args:
        profile_id: 用户ID

    Returns:
        tuple: (淘宝cookie字符串, 支付宝cookie字符串)，如果未找到则返回空字符串
    """
    try:
        # 加载配置文件
        config = load_config()
        profiles = config.get('profiles', {})

        # 直接查找profile_id
        if profile_id in profiles:
            profile_data = profiles[profile_id]
            cookie = profile_data.get('cookie', '')
            alipay_cookie = profile_data.get('alipay', '')

            if cookie:
                return cookie, alipay_cookie
            else:
                return '', alipay_cookie

        print(f"在配置文件中未找到用户ID: {profile_id}")
        return '', ''
    except Exception as e:
        return '', ''

def kill_chrome_processes():
    """结束所有Chrome进程"""
    import platform

    if platform.system() == "Windows":
        # Windows
        try:
            subprocess.run(["taskkill", "/F", "/IM", "chrome.exe"],
                          stdout=subprocess.PIPE,
                          stderr=subprocess.PIPE,
                          shell=True)

        except Exception as e:
            print(f"关闭Chrome进程失败: {e}")
    else:
        # Linux/Mac
        try:
            subprocess.run(["pkill", "-f", "chrome"],
                          stdout=subprocess.PIPE,
                          stderr=subprocess.PIPE)

        except Exception as e:
            print(f"关闭Chrome进程失败: {e}")

    # 等待进程完全结束
    time.sleep(3)


def open_browser_with_profile(profile_id, window_position=None):
    """
    使用指定的用户ID打开全新浏览器

    Args:
        profile_id: 用户ID
        window_position: 窗口位置，格式为 (x, y, width, height)，如果为None则使用默认大小和位置
    """
    try:
        # 获取Chrome路径
        config = load_config()
        chrome_path = config.get('chrome_path', '')

        # 如果配置中没有Chrome路径，尝试使用默认路径
        if not chrome_path:
            import platform
            if platform.system() == "Windows":
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"),
                    r"C:\Users\<USER>\.PyCharm2018.3\config\scratches\chrome\App\chrome.exe"
                ]
                for path in chrome_paths:
                    if os.path.exists(path):
                        chrome_path = path
                        break

        # 创建ChromiumOptions对象 - 使用全新浏览器
        co = ChromiumOptions()

        # 设置Chrome路径
        if chrome_path:
            co.set_browser_path(chrome_path)

        # 设置调试端口
        debug_port = random.randint(9000, 9999)
        co.set_local_port(debug_port)

        # 添加其他必要的选项
        co.set_argument("--no-first-run")  # 跳过首次运行向导
        co.set_argument("--no-default-browser-check")  # 跳过默认浏览器检查
        co.set_argument("--disable-features=TranslateUI")  # 禁用翻译功能
        co.set_argument("--disable-extensions")  # 禁用扩展，减少干扰


        browser = ChromiumPage(co)

        # 设置窗口大小和位置
        if window_position:
            x, y, width, height = window_position
            # 先设置窗口大小
            browser.set.window.size(width, height)
            # 再设置窗口位置
            browser.set.window.location(x, y)

        # 直接从config.json中获取指定profile_id的cookie
        cookie_str, alipay_cookie_str = get_cookie_from_config(profile_id)
        if cookie_str:
            set_cookies(browser, cookie_str, alipay_cookie_str)
        else:
            print(f"未从配置文件中找到用户 {profile_id} 的cookie")

        return browser
    except Exception as e:
        print(f"打开浏览器失败: {e}")
        return None

def set_cookies(browser, cookie_str, alipay_cookie_str=''):
    """设置cookie"""
    try:
        # 设置淘宝cookie
        if cookie_str and ';' in cookie_str and '=' in cookie_str:
            # 解析cookie字符串
            cookie_parts = cookie_str.split(';')
            for part in cookie_parts:
                if '=' in part:
                    try:
                        name, value = part.strip().split('=', 1)
                        cookie = {
                            'name': name,
                            'value': value,
                            'domain': '.taobao.com',
                            'path': '/'
                        }
                        browser.set.cookies(cookie)
                    except Exception as e:
                        pass


        # 设置支付宝cookie
        if alipay_cookie_str and ';' in alipay_cookie_str and '=' in alipay_cookie_str:
            # 解析cookie字符串
            cookie_parts = alipay_cookie_str.split(';')
            for part in cookie_parts:
                if '=' in part:
                    try:
                        name, value = part.strip().split('=', 1)
                        cookie = {
                            'name': name,
                            'value': value,
                            'domain': '.alipay.com',
                            'path': '/'
                        }
                        browser.set.cookies(cookie)
                    except Exception as e:
                        pass


        return True
    except Exception as e:
        print(f"设置cookie失败: {e}")
        return False

def get_orders(browser):
    """获取订单列表和对应的日期信息"""
    orders = []
    order_dates = []

    try:
        # 等待订单根元素加载完成
        root_element = browser.ele('css:#tp-bought-root', timeout=10)
        if not root_element:
            return [], []

        # 获取所有div子元素
        all_divs = root_element.eles('xpath:./div')

        # 从索引3开始处理（对应第4个元素）
        for i in range(3, len(all_divs)):
            order_div = all_divs[i]

            # 尝试获取日期元素
            try:
                date_element = order_div.ele('xpath:./div/table/tbody[1]/tr/td[1]/label/span[2]', timeout=1)
                if date_element:
                    date_text = date_element.text.strip()

                    # 添加到订单和日期列表
                    orders.append(order_div)
                    order_dates.append(date_text)
            except:
                # 如果找不到日期元素，可能不是订单元素，跳过
                continue


        return orders, order_dates
    except Exception as e:

        traceback.print_exc()
        return [], []

def filter_orders_by_date(orders, order_dates, start_date, end_date):
    """按日期筛选订单，最多返回15个订单"""
    if not start_date and not end_date:
        # 如果没有日期限制，也要限制订单数量
        return orders[:15]

    filtered_orders = []

    for i, date_str in enumerate(order_dates):
        # 如果已经找到15个订单，就停止筛选
        if len(filtered_orders) >= 15:
            break

        try:
            # 提取日期部分
            if ' ' in date_str:
                order_date_str = date_str.split(' ')[0]  # 假设格式为"2023-01-01 12:34:56"
            else:
                order_date_str = date_str

            # 解析日期
            from datetime import datetime
            order_date = datetime.strptime(order_date_str, "%Y-%m-%d")

            # 检查是否在日期范围内
            in_range = True

            if start_date:
                start = datetime.strptime(start_date, "%Y-%m-%d")
                if order_date < start:
                    in_range = False

            if end_date:
                end = datetime.strptime(end_date, "%Y-%m-%d")
                if order_date > end:
                    in_range = False

            if in_range:
                filtered_orders.append(orders[i])
        except Exception as e:
            print(f"解析订单日期出错: {e}")
            traceback.print_exc()
            continue

    print(f"日期筛选后剩余 {len(filtered_orders)} 个订单")
    return filtered_orders

def check_login(browser):
    """检查是否已登录淘宝"""
    try:
        # 获取当前URL
        current_url = browser.url

        # 如果URL包含login.taobao.com，表示未登录
        if "login.taobao.com" in current_url:
            print("用户未登录")
            return False

        # 尝试访问我的淘宝页面
        browser.get("https://i.taobao.com/my_taobao.htm")
        time.sleep(2)

        # 再次检查URL
        current_url = browser.url
        if "login.taobao.com" in current_url:
            print("用户未登录")
            return False

        print("用户已登录")
        return True
    except Exception as e:
        print(f"检查登录状态出错: {e}")
        return False



def process_receipt_task(profile_id, task_config=None, window_position=None):
    """处理单个用户的确认收货任务"""
    browser = None

    # 获取用户名称，用于日志显示
    config = load_config()
    profile_name = ""

    # 从配置文件中获取用户名称
    if profile_id in config.get('profiles', {}):
        profile_name = config.get('profiles', {}).get(profile_id, {}).get('name', '')

    # 如果任务配置中有用户名称，优先使用任务配置中的
    if task_config and 'profiles' in task_config and profile_id in task_config['profiles']:
        profile_name = task_config['profiles'][profile_id].get('name', profile_name)

    print(f"开始处理用户 {profile_name} (ID: {profile_id}) 的确认收货任务")

    # 获取日期范围配置
    start_date_str = config.get('start_date', '')
    end_date_str = config.get('end_date', '')

    # 如果任务配置中有日期范围，优先使用任务配置中的日期范围
    if task_config:
        if 'start_date' in task_config and task_config['start_date']:
            start_date_str = task_config['start_date']
        if 'end_date' in task_config and task_config['end_date']:
            end_date_str = task_config['end_date']

    # 获取支付密码
    pay_password = config.get('profiles', {}).get(profile_id, {}).get('pay_password', '')

    try:
        # 打开浏览器，传递profile_id和窗口位置
        browser = open_browser_with_profile(profile_id, window_position)
        if not browser:
            print(f"为用户 {profile_name} (ID: {profile_id}) 打开浏览器失败")
            return False

        # 检查是否登录
        if not check_login(browser):
            print(f"用户 {profile_name} (ID: {profile_id}) 未登录")
            return False
        while True:
            # 打开待收货页面
            wait_receipt_url = "https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitConfirm&pageSize=100&route_to=tm1"
            browser.get(wait_receipt_url)
            print("已打开待收货页面")
            time.sleep(1)
            # 检查是否有待收货订单
            orders, order_dates = get_orders(browser)
            if not orders:
                print("没有待确认订单")
                return True

            # 过滤日期
            filtered_orders = filter_orders_by_date(orders, order_dates, start_date_str, end_date_str)
            if not filtered_orders:
                print("所有订单不在筛选日期范围内")
                return True

            # 计数器
            completed_count = 0
            skipped_count = 0

            # 记录无法勾选的订单ID
            disabled_order_ids = []

            # 更新任务计数并通知前端
            total_count = len(filtered_orders)
            notify_task_counts(profile_id, total_count, completed_count, skipped_count)

            # 尝试勾选所有符合条件的订单
            print(f"开始勾选 {len(filtered_orders)} 个订单")

            for i, order in enumerate(filtered_orders):
                try:
                    # 从data-reactid属性中提取订单ID
                    data_reactid = order.attr('data-reactid')
                    if data_reactid and '$order-' in data_reactid:
                        # 从data-reactid中提取订单ID
                        order_id = data_reactid.split('$order-')[1]
                        if not order_id:
                            print(f"无法从data-reactid提取订单 {i+1} 的ID，跳过")
                            continue
                    else:
                        print(f"无法获取订单 {i+1} 的ID，跳过")
                        continue

                    # 查找复选框
                    checkbox = order.ele('css:input[type="checkbox"]', timeout=2)
                    if not checkbox:
                        checkbox = order.ele('xpath:.//input[@type="checkbox"]', timeout=2)

                    if checkbox:
                        # 检查复选框是否可选
                        if not checkbox.states.is_enabled:
                            print(f"订单 {order_id} 的复选框不可选，将单独处理")
                            disabled_order_ids.append(order_id)
                            continue

                        # 勾选复选框
                        if not checkbox.attr('checked'):
                            browser.run_js("arguments[0].click();", checkbox)
                            time.sleep(0.2)
                    else:
                        print(f"未找到订单 {order_id} 的复选框，将单独处理")
                        disabled_order_ids.append(order_id)
                except Exception as e:
                    print(f"勾选订单时出错: {e}")
                    # 如果出错，尝试获取订单ID并添加到单独处理列表
                    try:
                        data_reactid = order.attr('data-reactid')
                        if data_reactid and '$order-' in data_reactid:
                            order_id = data_reactid.split('$order-')[1]
                            if order_id:
                                disabled_order_ids.append(order_id)
                    except:
                        pass

            # 有勾选的订单时才进行批量确认
            if len(filtered_orders) - len(disabled_order_ids) > 0:
                # 点击批量确认收货按钮
                batch_confirm_btn = browser.ele('xpath://button[contains(text(), "批量确认收货")]', timeout=5)
                if not batch_confirm_btn:
                    print("未找到批量确认收货按钮，尝试其他选择器")
                    batch_confirm_btn = browser.ele('xpath://div[contains(@class, "operations")]//button', timeout=5)
                print("点击批量确认收货按钮")

                batch_confirm_btn.click() # 点击按钮并且获取新标签tab对象
                pwd_input2 = browser.latest_tab
                pwd_input2.wait.load_start()
                print(pwd_input2.title)

                # 检查新标签页的URL是否包含风控标识
                new_tab_url = pwd_input2.url
                print(f"新标签页URL: {new_tab_url}")

                # 检查是否为风控页面 (BOPS_N_FREEZED表示账号被风控)
                if 'BOPS_N_FREEZED' in new_tab_url:
                    print(f"检测到账号风控，URL: {new_tab_url}")
                    print(f"用户 {profile_name} (ID: {profile_id}) 账号已被风控，跳过此账号")
                    # 通知app.py更新用户状态为风控
                    notify_risk_control(profile_id)
                    # 关闭新标签页
                    pwd_input2.close()
                    # 关闭浏览器并返回，继续处理下一个账号
                    browser.quit()
                    return True

                # 等待新页面加载
                # 点击确认按钮
                pwd_input2.ele('xpath://*[@id="J_btn"]/a', timeout=10).click() # 点击确认收货
                print("点击确认按钮")
                time.sleep(2)
                # 检查是否需要输入支付密码
                pwd_input = pwd_input2.ele('xpath://input[@id="payPassword_rsainput"]', timeout=5)
                if pwd_input:
                    print("输入支付密码...")
                    pwd_input.input(pay_password)
                    time.sleep(2)
                    # 点击提交按钮
                    pwd_input2.ele('xpath://*[@id="J_authSubmit"]', timeout=5).click()
                    time.sleep(2)
                    print('点击确定')
                    pwd_input2.handle_alert(accept=True, send='some text')
                    time.sleep(1)
                    ele1 = pwd_input2.ele('xpath://*[@id="J_authSubmit"]',timeout=3)
                    if ele1:
                        print('密码错误')
                        # 通知app.py更新用户状态为密码错误
                        notify_password_error(profile_id)
                        browser.quit()
                        return True
                    else:
                        print('支付密码正确')
                        # 通知app.py更新用户状态为正常
                        notify_password_normal(profile_id)
                        pwd_input2.close()

                else:
                    print("未配置支付密码，无法完成确认收货")
                    skipped_count = len(filtered_orders) - len(disabled_order_ids)
                    completed_count = 0

            # 处理无法勾选的订单
            if disabled_order_ids:
                print(f"开始处理 {len(disabled_order_ids)} 个无法批量勾选的订单")
                for i, order_id in enumerate(disabled_order_ids):
                    try:
                        # 直接访问确认收货页面
                        confirm_url = f"https://trade.taobao.com/trade/confirmGoods.htm?bizOrderId={order_id}"
                        browser.get(confirm_url)
                        time.sleep(2)

                        # 点击确认按钮
                        confirm_btn = browser.ele('xpath://*[@id="J_btn"]/a', timeout=10) # 点击确认收货
                        confirm_btn.click()
                        time.sleep(3)

                        # 检查当前页面URL是否包含风控标识
                        current_url = browser.url
                        print(f"单个订单确认页面URL: {current_url}")

                        # 检查是否为风控页面 (BOPS_N_FREEZED表示账号被风控)
                        if 'BOPS_N_FREEZED' in current_url:
                            print(f"检测到账号风控，URL: {current_url}")
                            print(f"用户 {profile_name} (ID: {profile_id}) 账号已被风控，跳过此账号")
                            # 通知app.py更新用户状态为风控
                            notify_risk_control(profile_id)
                            # 关闭浏览器并返回，继续处理下一个账号
                            browser.quit()
                            return True

                        # 检查是否需要输入支付密码
                        pwd_input = browser.ele('xpath://input[@id="payPassword_rsainput"]', timeout=5)
                        if pwd_input:
                            print("输入支付密码...")
                            pwd_input.input(pay_password)
                            time.sleep(1)
                            # 点击提交按钮
                            browser.ele('xpath://*[@id="J_authSubmit"]', timeout=5).click()
                            time.sleep(2)
                            print('点击确定')
                            browser.handle_alert(accept=True, send='some text')
                            time.sleep(1)

                            # 检查是否出现密码错误提示
                            ele1 = browser.ele('支付密码错误，请输入6位数字支付密码')
                            if ele1:
                                print('密码错误')
                                # 通知app.py更新用户状态为密码错误
                                notify_password_error(profile_id)
                            else:
                                print('支付密码正确')
                                # 通知app.py更新用户状态为正常
                                notify_password_normal(profile_id)
                        else:
                            print("未配置支付密码，无法完成确认收货")
                            skipped_count = len(filtered_orders) - len(disabled_order_ids)
                            completed_count = 0

                        completed_count += 1
                        # 更新任务计数并通知前端
                        notify_task_counts(profile_id, total_count, completed_count, skipped_count)
                        print(f"订单 {order_id} 单独确认收货成功")

                        # 单个订单确认收货后不立即评价，统一在批量确认收货后进行评价
                        # 这样与老版本保持一致，避免重复评价
                        if i < len(disabled_order_ids) - 1:  # 最后一个订单不需要延迟
                            delay = random.uniform(1, 3)
                            time.sleep(delay)

                    except Exception as e:
                        print(f"处理订单 {order_id} 时出错: {e}")
                        traceback.print_exc()
                        skipped_count += 1
                        # 更新任务计数并通知前端
                        notify_task_counts(profile_id, total_count, completed_count, skipped_count)
                        browser.get(wait_receipt_url)
                        time.sleep(3)

            # 如果任务配置中有针对特定用户的自动评价设置，使用该设置
            auto_review_enabled = task_config['profiles'][profile_id].get('auto_review_enabled', True)
            print(f"从任务配置中获取用户 {profile_id} 的自动评价设置: {auto_review_enabled}")
            if auto_review_enabled:
                # 首先尝试从任务配置中获取review_text字段
                review_text = task_config.get('review_text', '')

                # 如果找到review_text字段，按换行符分割成评价内容列表
                if review_text:
                    review_texts = review_text.split('\n')
                    # 过滤掉空行
                    review_texts = [text.strip() for text in review_texts if text.strip()]
                else:
                    # 如果没有review_text字段，尝试从rate字段获取
                    # 如果任务配置中有针对特定用户的评价文本，使用该设置
                    review_texts = task_config['profiles'][profile_id].get('rate')

                # 确保评价文本是列表格式
                if isinstance(review_texts, str):
                    review_texts = [review_texts]
                elif not review_texts:
                    # 如果没有找到评价文本，尝试从全局配置中获取
                    global_review_text = config.get('review_text', '')
                    if global_review_text:
                        # 按换行符分割成评价内容列表
                        review_texts = global_review_text.split('\n')
                        # 过滤掉空行
                        review_texts = [text.strip() for text in review_texts if text.strip()]
                    else:
                        # 如果全局配置中也没有review_text，尝试使用rate字段
                        review_texts = config.get('rate', ["宝贝收到了，很满意，谢谢卖家！"])

                # 获取cookie
                cookie_str = browser.cookies().as_str()
                if cookie_str:
                    print("开始批量自动评价...")
                    # 使用老版本的task_rate函数进行自动评价
                    # 获取日期范围
                    beginTime = start_date_str
                    endTime = end_date_str

                    # 调用task_rate函数
                    # 获取图片目录配置
                    image_directory = task_config.get('image_directory', '')
                    task_rate(cookie_str, beginTime, endTime, review_texts, None, image_directory)
                    print("自动评价完成")

                else:
                    print("无法获取cookie，跳过自动评价")

            # 最终更新任务计数并通知前端
            notify_task_counts(profile_id, total_count, completed_count, skipped_count)
            print(f"用户 {profile_name} (ID: {profile_id}) 确认收货任务完成，成功: {completed_count}，跳过: {skipped_count}")


    except Exception as e:
        print(f"处理用户 {profile_name} (ID: {profile_id}) 的确认收货任务失败: {e}")
        traceback.print_exc()
        return False

    finally:
        # 关闭浏览器
        if browser:
            print(f"关闭浏览器")
            browser.quit()

def process_tasks(profile_ids, max_threads=1, task_config=None):
    """处理多个用户的确认收货任务"""
    # 先结束所有Chrome进程
    kill_chrome_processes()

    # 计算窗口布局
    def calculate_window_positions(num_windows, max_threads):
        """计算窗口位置，返回 (x, y, width, height) 元组的列表"""
        # 获取屏幕分辨率
        import ctypes
        user32 = ctypes.windll.user32
        screen_width = user32.GetSystemMetrics(0)
        screen_height = user32.GetSystemMetrics(1)

        # 计算每个窗口的大小
        # 如果窗口数量小于等于2，横向排列
        if num_windows <= 2:
            cols = num_windows
            rows = 1
        # 如果窗口数量小于等于4，2x2排列
        elif num_windows <= 4:
            cols = 2
            rows = 2
        # 如果窗口数量小于等于6，3x2排列
        elif num_windows <= 6:
            cols = 3
            rows = 2
        # 如果窗口数量小于等于9，3x3排列
        elif num_windows <= 9:
            cols = 3
            rows = 3
        # 如果窗口数量更多，4x3排列
        else:
            cols = 4
            rows = 3

        # 限制最大线程数
        num_windows = min(num_windows, max_threads)

        # 计算窗口大小
        window_width = screen_width // cols
        window_height = screen_height // rows

        # 计算每个窗口的位置
        positions = []
        for i in range(num_windows):
            row = i // cols
            col = i % cols
            x = col * window_width
            y = row * window_height
            positions.append((x, y, window_width, window_height))
        return positions

    # 如果max_threads大于1，使用多线程处理
    if max_threads > 1 and len(profile_ids) > 1:
        # 计算窗口位置
        window_positions = calculate_window_positions(len(profile_ids), max_threads)
        # 创建线程列表和参数列表
        threads = []
        thread_args = []

        # 创建线程参数
        for i, profile_id in enumerate(profile_ids):
            # 如果i超出了window_positions的范围，使用默认位置
            window_position = window_positions[i % len(window_positions)] if window_positions else None
            thread_args.append((profile_id, task_config, window_position))

        # 创建线程
        for args in thread_args:
            thread = threading.Thread(target=process_receipt_task, args=args)
            thread.daemon = True
            threads.append(thread)

        # 控制同时运行的线程数
        running_threads = []
        for thread in threads:
            # 如果运行中的线程数达到最大值，等待某个线程完成
            while len(running_threads) >= max_threads:
                for t in running_threads[:]:
                    if not t.is_alive():
                        running_threads.remove(t)
                if len(running_threads) >= max_threads:
                    time.sleep(1)

            # 启动新线程
            thread.start()
            running_threads.append(thread)

            # 添加短暂延迟，避免同时启动多个浏览器
            time.sleep(2)

        # 等待所有线程完成
        for thread in threads:
            thread.join()
        print("所有用户的确认收货任务已完成")
    else:
        # 单线程模式，依次处理每个用户
        print(f"使用单线程模式处理 {len(profile_ids)} 个用户")
        for profile_id in profile_ids:
            # 单线程模式下使用默认窗口位置
            process_receipt_task(profile_id, task_config)
            # 添加短暂延迟，避免立即启动下一个浏览器
            time.sleep(2)

        print("所有用户的确认收货任务已完成")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='淘宝自动操作脚本')
    parser.add_argument('--profile_ids', type=str, help='用户ID列表，多个ID用逗号分隔')
    parser.add_argument('--max_threads', type=int, default=1, help='最大线程数，默认为1')
    parser.add_argument('--task_file', type=str, help='任务配置文件路径')
    parser.add_argument('--config_path', type=str, help='配置文件路径')
    parser.add_argument('--operation', type=str, choices=['confirm', 'order'], default='confirm',
                        help='操作类型：confirm(确认收货)或order(下单)，默认为confirm')

    args = parser.parse_args()

    # 设置全局配置文件路径
    global CONFIG_PATH
    if args.config_path and os.path.exists(args.config_path):
        CONFIG_PATH = args.config_path
        print(f"使用命令行指定的配置文件路径: {CONFIG_PATH}")

    task_config = None
    operation = args.operation

    # 如果指定了任务配置文件，从文件中读取任务
    if args.task_file and os.path.exists(args.task_file):
        try:
            with open(args.task_file, 'r', encoding='utf-8') as f:
                task_config = json.load(f)
                print(f"成功读取任务配置文件: {args.task_file}")

                # 打印任务配置中的自动评价设置（如果有）
                if 'auto_review_enabled' in task_config:
                    print(f"任务配置中的全局自动评价设置: {task_config['auto_review_enabled']}")

                # 打印任务配置中的用户自动评价设置（如果有）
                if 'profiles' in task_config:
                    for pid, pdata in task_config['profiles'].items():
                        if 'auto_review_enabled' in pdata:
                            print(f"任务配置中用户 {pid} 的自动评价设置: {pdata['auto_review_enabled']}")

            # 从任务配置中获取操作类型（如果有）
            if 'operation' in task_config:
                operation = task_config.get('operation')
                print(f"从任务配置文件获取操作类型: {operation}")

            profile_ids = task_config.get('profile_ids', [])
            max_threads = task_config.get('max_threads', 1)
            print(f"从任务配置文件加载任务: {args.task_file}")
            print(f"用户ID列表: {profile_ids}")
            print(f"最大线程数: {max_threads}")

            # 如果任务配置中包含Chrome路径，更新到全局配置中
            config = load_config()
            config_updated = False

            if 'chrome_path' in task_config and task_config['chrome_path']:
                print(f"使用任务配置中的Chrome路径: {task_config['chrome_path']}")
                # 更新Chrome路径
                config['chrome_path'] = task_config['chrome_path']
                config_updated = True

            # 如果配置有更新，保存到文件
            if config_updated:
                # 保存配置
                config_path = get_config_path()
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"读取任务配置文件失败: {e}")
            return
    else:
        # 从命令行参数获取任务
        if args.profile_ids:
            profile_ids = args.profile_ids.split(',')
        else:
            # 如果没有指定用户ID，使用配置文件中的所有用户
            config = load_config()
            profile_ids = list(config.get('profiles', {}).keys())
            if not profile_ids:
                print("未指定用户ID，且配置文件中没有用户配置")
                return

        max_threads = args.max_threads

    # 根据操作类型执行不同的任务
    if operation == 'order':
        # 执行下单任务
        print("执行下单任务...")
        has_links = False
        # 只检查profile_order_urls字段
        if task_config and 'profile_order_urls' in task_config:
            has_links = True

        if has_links:
            process_order_tasks(task_config)
        else:
            print("下单任务需要提供订单链接，请在任务配置文件中指定profile_order_urls参数")
    else:
        # 执行确认收货任务
        print("执行确认收货任务...")
        process_tasks(profile_ids, max_threads, task_config)

def process_order_tasks(task_config):
    """处理下单任务"""
    profile_ids = task_config.get('profile_ids', [])
    shuffle = task_config.get('shuffle', False)
    multi_thread = task_config.get('multi_thread', False)
    max_threads = task_config.get('max_threads', 1)

    # 获取每个用户的下单链接
    profile_order_urls = task_config.get('profile_order_urls', {})

    # 计算总链接数
    total_links = 0
    for urls in profile_order_urls.values():
        total_links += len(urls)
    print(f"是否随机排序: {shuffle}, 是否多线程: {multi_thread}, 最大线程数: {max_threads}")

    # 先结束所有Chrome进程
    kill_chrome_processes()


    # 如果使用多线程模式
    if multi_thread and max_threads > 1 and len(profile_ids) > 1:
        print(f"使用多线程模式处理下单任务，最大线程数: {max_threads}")
        # 计算窗口位置
        def calculate_window_positions(num_windows, max_threads):
            """计算窗口位置，返回 (x, y, width, height) 元组的列表"""
            # 获取屏幕分辨率
            import ctypes
            user32 = ctypes.windll.user32
            screen_width = user32.GetSystemMetrics(0)
            screen_height = user32.GetSystemMetrics(1)

            # 计算每个窗口的大小
            # 如果窗口数量小于等于2，横向排列
            if num_windows <= 2:
                cols = num_windows
                rows = 1
            # 如果窗口数量小于等于4，2x2排列
            elif num_windows <= 4:
                cols = 2
                rows = 2
            # 如果窗口数量小于等于6，3x2排列
            elif num_windows <= 6:
                cols = 3
                rows = 2
            # 如果窗口数量小于等于9，3x3排列
            elif num_windows <= 9:
                cols = 3
                rows = 3
            # 如果窗口数量更多，4x3排列
            else:
                cols = 4
                rows = 3

            # 限制最大线程数
            num_windows = min(num_windows, max_threads)

            # 计算窗口大小
            window_width = screen_width // cols
            window_height = screen_height // rows

            # 计算每个窗口的位置
            positions = []
            for i in range(num_windows):
                row = i // cols
                col = i % cols
                x = col * window_width
                y = row * window_height
                positions.append((x, y, window_width, window_height))

            return positions

        # 计算窗口位置
        window_positions = calculate_window_positions(len(profile_ids), max_threads)
        print(f"计算得到 {len(window_positions)} 个窗口位置")

        # 创建线程列表
        threads = []
        # 为每个用户创建一个线程
        for i, profile_id in enumerate(profile_ids):
            # 获取该用户的下单链接
            if profile_id in profile_order_urls:
                user_urls = profile_order_urls[profile_id]

                # 如果用户没有下单链接，跳过
                if not user_urls:
                    print(f"用户 {profile_id} 没有下单链接，跳过")
                    continue

                # 如果i超出了window_positions的范围，使用默认位置
                window_position = window_positions[i % len(window_positions)] if window_positions else None

                thread = threading.Thread(
                    target=process_order_task_for_profile,
                    args=(profile_id, user_urls, window_position, task_config)
                )
                thread.daemon = True
                threads.append(thread)
            else:
                print(f"用户 {profile_id} 在配置中没有下单链接，跳过")

        # 控制同时运行的线程数
        running_threads = []
        for thread in threads:
            # 如果运行中的线程数达到最大值，等待某个线程完成
            while len(running_threads) >= max_threads:
                for t in running_threads[:]:
                    if not t.is_alive():
                        running_threads.remove(t)
                if len(running_threads) >= max_threads:
                    time.sleep(1)

            # 启动新线程
            thread.start()
            running_threads.append(thread)

            # 添加短暂延迟，避免同时启动多个浏览器
            time.sleep(2)

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        print("所有下单任务已完成")
    else:
        # 单线程模式，依次处理每个用户
        print(f"使用单线程模式处理下单任务")
        for profile_id in profile_ids:
            # 获取该用户的下单链接
            if profile_id in profile_order_urls:
                user_urls = profile_order_urls[profile_id]

                # 如果用户没有下单链接，跳过
                if not user_urls:
                    print(f"用户 {profile_id} 没有下单链接，跳过")
                    continue

                process_order_task_for_profile(profile_id, user_urls, None, task_config)
                # 添加短暂延迟，避免立即启动下一个浏览器
                time.sleep(2)
            else:
                print(f"用户 {profile_id} 在配置中没有下单链接，跳过")

        print("所有下单任务已完成")
def getAliPay(cookie):
    result = []
    for i in range(5):
        t = str(int(time.time()))
        date = '{"OrderType":"OrderList","appName":"tborder","appVersion":"3.0","condition":"{\\"categoryText\\":[null],\\"filterSelectInfo\\":{\\"isFilterResult\\":true,\\"selectedCategoryIndex\\":\\"-1\\",\\"selectedEndTime\\":\\"\\",\\"selectedEndTimeIndex\\":\\"1\\",\\"selectedGiftIndex\\":\\"-1\\",\\"selectedSourceIndex\\":\\"-1\\",\\"selectedStartTime\\":\\"\\",\\"selectedStartTimeIndex\\":\\"1\\",\\"selectedTimeIndex\\":\\"-1\\"},\\"onlyGiftFilter\\":\\"false\\",\\"orderFilterExtParam\\":{\\"beginTime\\":\\"\\",\\"endTime\\":\\"\\",\\"filterSource\\":\\"\\",\\"giftType\\":\\"\\"},\\"version\\":\\"1.0.0\\",\\"wordType\\":\\"3\\"}","page":"' + str(i + 1) + '","tabCode":"waitPay","templateConfigVersion":"0"}'
        xapi = 'mtop.taobao.order.queryboughtlistv2'
        xv = '1.0'
        token = cookie.split('_m_h5_tk=')[1].split('_')[0]
        str1 = token + '&' + t + '&12574478&' + date
        str2 = bytes(str1, encoding='utf-8')  # md5
        sign = hashlib.md5(str2).hexdigest()
        data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&type=json&ttid=700170@taobao_android_10.27.10&dataType=json&data=' + str(
            quote(date, 'utf-8'))
        url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
        head = {
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
            'cookie': cookie
        }
        # 发送请求获取待付款订单
        r = requests.get(url2, timeout=20, headers=head, stream=False, verify=False)
        result.extend(extract_specific_fields2(r.json()))
    if len(result) == 0:
        return ''
    biz_order_id = '&'.join(f'biz_order_id={v["orderId"]}' for v in result[:20])
    urlpay = 'https://buyertrade.taobao.com/trade/AlipayListScreen.htm'
    headers2 = {'Host': 'buyertrade.taobao.com',
                'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Origin':'https://buyertrade.taobao.com',
                'Content-Type':'application/x-www-form-urlencoded',
                'Referer':'https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitPay&route_to=tm1',
                'Cookie':cookie}
    r2 = requests.post(urlpay,data=biz_order_id,headers=headers2, stream=False, verify=False)
    try:
        return 'https://tbapi.alipay.com' + r2.text.split('https://tbapi.alipay.com')[1].split('"')[0]
    except:
        return ''
def process_order_task_for_profile(profile_id, order_urls, window_position=None, task_config=None):
    """处理单个用户的下单任务"""
    browser = None

    # 获取用户名称，用于日志显示
    config = load_config()
    profile_name = ""

    # 从配置文件中获取用户名称
    if profile_id in config.get('profiles', {}):
        profile_name = config.get('profiles', {}).get(profile_id, {}).get('name', '')

    print(f"开始处理用户 {profile_name} (ID: {profile_id}) 的下单任务")

    # 检查是否启用自动付款
    auto_payment = False
    if task_config and 'auto_payment' in task_config:
        auto_payment = task_config.get('auto_payment', False)
        print(f"自动付款选项: {'启用' if auto_payment else '禁用'}")

    try:
        # 打开浏览器，传递profile_id和窗口位置
        browser = open_browser_with_profile(profile_id, window_position)
        if not browser:
            print(f"为用户 {profile_name} (ID: {profile_id}) 打开浏览器失败")
            return False

        # 检查登录状态
        browser.get("https://www.taobao.com")
        time.sleep(1)

        # 检查是否需要登录
        if "login.taobao.com" in browser.url:
            print(f"用户 {profile_name} (ID: {profile_id}) 登录已失效或未登录")
            return False

        # 处理每个订单链接
        for i, url in enumerate(order_urls):
            # 执行下单操作
            success = place_order(browser, url)
            if success:
                print(f"下单成功")
            else:
                print(f"下单失败")
            # 随机延迟，避免操作过快
            delay = random.uniform(2, 4)  # 随机延迟1-3秒
            print(f"等待 {delay:.1f} 秒后处理下一个链接...")
            time.sleep(delay)

        # 只有在启用自动付款选项时才执行付款流程
        if auto_payment:
            while True:
                browser.get("https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitPay&route_to=tm1")
                time.sleep(2)
                payUrl = getAliPay(browser.cookies().as_str())
                if payUrl == '':
                    print('所有付款执行完成')
                    break
                browser.get(payUrl)
                time.sleep(2)
                browser.ele('xpath://*[@id="channels"]/div/div[1]/button[3]').click() # 点击网银支付
                time.sleep(3)
                browser.wait.ele_displayed('css:.ant-modal-root', timeout=5)
                next_button = browser.ele('css:.ant-modal-root .ant-btn.ant-btn-primary')
                next_button.click() # 点击下一步
                time.sleep(2)

                browser.ele('xpath://*[@id="root"]/div/div[2]/div[2]/div[2]/div/div/button/span').click() # 点击登录网上银行
                time.sleep(2)
                iframe = browser.ele('#frame')
                iframe_src = iframe.attr('src')
                # 打印出iframe的src属性(调试用)
                # 构建完整URL并直接跳转
                base_url = browser.url.split('/EbusPerbankFront')[0]  # 获取基础URL
                # browser.get(iframe_src)
                time.sleep(1)
                k_bao_title = browser.ele('xpath://p[@class="color vtitle"]/em[@class="v v02"]/..')
                if k_bao_title:
                    k_bao_title.click()

                # 获取K宝/K令设备锁，确保同一时间只有一个线程操作K宝/K令
                print("等待获取K宝/K令设备锁...")
                k_device_lock.acquire()
                print("已获取K宝/K令设备锁，开始操作K宝/K令设备")

                try:
                    # 等待K宝/K令支付内容区域显示出来
                    browser.wait.ele_displayed('css:.vcon', timeout=3)
                    time.sleep(2)
                    # 注意有两个下一步按钮，我们需要点击显示状态的那个
                    xiayibu = browser.ele('xpath://div[@show="close"]//span[text()="下一步"]')
                    if xiayibu:
                        xiayibu.click()
                    while True:
                        if str(browser.url).find('ABCRegCardPayInitNewAct?TOKEN=') != -1 or str(browser.url).find('ABCRegCardPayCertAct?TOKEN=') -1:
                            time.sleep(2)
                            # 1. 选择第一个支付卡号
                            # 使用JavaScript选择第一个卡号
                            js_select_card = """
                            var select = document.querySelector('select[name="cardno"]');
                            if(select) {
                                select.selectedIndex = 1;  // 索引1对应第二个选项（第一个真实卡号）
                                select.dispatchEvent(new Event('change'));
                                return true;
                            }
                            return false;
                            """
                            browser.run_js(js_select_card) # 选择卡号
                            break
                        else:
                            print('未点击K宝')
                            time.sleep(2)

                finally:
                    # 释放K宝/K令设备锁，确保在任何情况下都能释放锁
                    print("K宝/K令操作初始化完成，释放设备锁")
                    k_device_lock.release()

                # 等待浏览器窗口关闭
                #iframe3 = browser.ele('#frame')
                imu = 0
                while True:
                    brurl = 'https://buyertrade.taobao.com/trade/batch_pay_success.htm'
                    if str(browser.latest_tab.url).find(brurl) != -1 or str(browser.latest_tab.url).find('cashier.alipay.com') != -1 or str(browser.latest_tab.url).find('cashieret2.alipay.com') != -1:
                        print("付款成功")
                        tabs = browser.get_tabs()
                        for i in range(len(tabs)):
                            if i == 1:
                                continue
                            else:
                                tabs[i].close()
                        break
                    else:
                        print("未跳转支付成功页面")
                        imu = imu + 1
                        time.sleep(1)
                        if imu == 60:
                            print('超时退出')
                            break
                continue
        else:
            while True:
                browser.get("https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitPay&route_to=tm1")
                time.sleep(2)
                payUrl = getAliPay(browser.cookies().as_str())
                if payUrl == '':
                    print('所有付款执行完成')
                    break
                url1 = browser.new_tab()
                url1.get(url=payUrl)
                time.sleep(2)
                while True:
                    tabs = browser.get_tabs()
                    if len(tabs) > 1:
                        time.sleep(2)
                        print('等待用户手动关闭付款窗口')
                    else:
                        break

        print(f"用户 {profile_name} (ID: {profile_id}) 下单任务完成")
        return True

    except Exception as e:
        print(f"处理用户 {profile_name} (ID: {profile_id}) 的下单任务失败: {e}")
        return False

    finally:
        # 关闭浏览器
        if browser:
            print(f"关闭浏览器")
            browser.quit()

def place_order(browser, url):
    """执行下单操作"""
    try:
        # 打开商品链接
        browser.get(url)
        # 额外等待确保页面完全加载
        time.sleep(2)
        # 检查是否需要登录
        if "login.taobao.com" in browser.url:
            print("需要登录，但用户未登录")
            return False

        # 查找并点击"提交订单"按钮
        submit_button = browser.ele('提交订单', timeout=10)
        if not submit_button:
            print("未找到提交订单按钮")
            return False
        print("点击提交订单按钮...")
        submit_button.click()
        # 等待订单提交完成
        for i in range(12):
            if str(browser.url).find('maliprod.alipay') != -1:
                print('订单提交成功!')
                return True
            else:
                print('等待提交成功')
                time.sleep(1)
                continue
        return False
    except Exception as e:
        print(f"下单操作失败: {e}")
        return False

if __name__ == "__main__":
    main()
