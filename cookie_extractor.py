import os
import json
import base64
import sqlite3
import win32crypt
from Crypto.Cipher import AES
import shutil
from datetime import datetime, timed<PERSON><PERSON>


def get_chrome_datetime(chrome_date):
    """将Chrome时间格式转换为可读格式"""
    return datetime(1601, 1, 1) + timedelta(microseconds=chrome_date)


def get_encryption_key():
    """获取用于解密的AES密钥"""
    # Chrome本地状态文件路径
    local_state_path = os.path.join(os.environ["USERPROFILE"],
                                    "AppData", "Local", "Google", "Chrome",
                                    "User Data", "Local State")

    with open(local_state_path, "r", encoding="utf-8") as f:
        local_state = json.loads(f.read())

    # 获取加密密钥
    key = base64.b64decode(local_state["os_crypt"]["encrypted_key"])

    # 移除DPAPI前缀'DPAPI'
    key = key[5:]

    # 使用Windows DPAPI解密
    return win32crypt.CryptUnprotectData(key, None, None, None, 0)[1]

def is_readable_text(text):
    """检查字符串是否为可读文本（不含乱码）"""
    # 检查是否全部是可打印ASCII字符或常见Unicode字符
    for char in text:
        # 如果不是可打印ASCII字符且不是常见Unicode字符，则认为是乱码
        if not (32 <= ord(char) <= 126 or ord(char) > 256):
            return False
    return True
def decrypt_cookie(encrypted_value, key):
    """解密Cookie值，只返回文本部分"""
    try:
        # 检查是否是v10/v11加密格式
        if encrypted_value.startswith(b'v10') or encrypted_value.startswith(b'v11'):
            # 提取初始化向量
            iv = encrypted_value[3:15]
            # 提取加密数据
            encrypted_data = encrypted_value[15:]
            # 创建AES-GCM解密器
            cipher = AES.new(key, AES.MODE_GCM, iv)
            # 解密
            decrypted_data = cipher.decrypt(encrypted_data)[:-16]

            # 更精确地检测文本部分
            # 查找连续可打印ASCII字符的最长序列
            max_printable_len = 0
            max_printable_start = -1
            current_len = 0
            current_start = -1

            for i, byte in enumerate(decrypted_data):
                if 32 <= byte <= 126:  # 可打印ASCII范围
                    if current_len == 0:
                        current_start = i
                    current_len += 1
                else:
                    if current_len > max_printable_len:
                        max_printable_len = current_len
                        max_printable_start = current_start
                    current_len = 0

            # 检查最后一段
            if current_len > max_printable_len:
                max_printable_len = current_len
                max_printable_start = current_start

            # 如果找到足够长的可打印序列（至少8个字符）
            if max_printable_len >= 8 and max_printable_start >= 0:
                try:
                    # 只返回文本部分
                    return decrypted_data[max_printable_start:].decode('utf-8')
                except UnicodeDecodeError:
                    # 如果UTF-8解码失败，尝试latin-1
                    try:
                        return decrypted_data[max_printable_start:].decode('latin-1')
                    except UnicodeDecodeError:
                        pass

            # 如果上面的方法失败，尝试常规解码方法
            try:
                return decrypted_data.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    return decrypted_data.decode('latin-1')
                except UnicodeDecodeError:
                    return decrypted_data.hex()
        else:
            # 对于旧版本的加密方式，直接使用DPAPI
            decrypted_data = win32crypt.CryptUnprotectData(encrypted_value, None, None, None, 0)[1]
            try:
                return decrypted_data.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    return decrypted_data.decode('latin-1')
                except UnicodeDecodeError:
                    return decrypted_data.hex()
    except Exception as e:
        print(f"解密失败: {e}")
        return encrypted_value.hex() if encrypted_value else ""


def get_chrome_cookies(domain=None):
    """获取Chrome浏览器的Cookie，过滤掉包含乱码的Cookie"""
    # Cookie数据库路径
    cookie_path = os.path.join(os.environ["USERPROFILE"],
                               "AppData", "Local", "Google", "Chrome",
                               "User Data", "Default", "Network", "Cookies")

    # 创建临时副本，因为数据库可能被锁定
    temp_cookie_path = os.path.join(os.environ["TEMP"], "temp_cookies.db")
    if os.path.exists(temp_cookie_path):
        os.remove(temp_cookie_path)
    shutil.copy2(cookie_path, temp_cookie_path)

    # 获取解密密钥
    key = get_encryption_key()

    # 连接到Cookie数据库
    conn = sqlite3.connect(temp_cookie_path)
    cursor = conn.cursor()

    # 查询Cookie
    if domain:
        cursor.execute(
            f"SELECT host_key, name, value, encrypted_value, path, expires_utc FROM cookies WHERE host_key LIKE '%{domain}%'")
    else:
        cursor.execute("SELECT host_key, name, value, encrypted_value, path, expires_utc FROM cookies")

    # 存储结果
    cookies = []
    for host_key, name, value, encrypted_value, path, expires_utc in cursor.fetchall():
        if not value and encrypted_value:
            # 解密Cookie值
            decrypted_value = decrypt_cookie(encrypted_value, key)
        else:
            decrypted_value = value

        # 只保留不含乱码的Cookie
        if is_readable_text(decrypted_value):
            # 转换过期时间
            expires = get_chrome_datetime(expires_utc).strftime('%Y-%m-%d %H:%M:%S') if expires_utc else None

            cookie = {
                "domain": host_key,
                "name": name,
                "value": decrypted_value,
                "path": path,
                "expires": expires
            }
            cookies.append(cookie)

    # 关闭连接并删除临时文件
    conn.close()
    os.remove(temp_cookie_path)

    return cookies
def format_cookie_string(cookies):
    """将Cookie列表格式化为标准Cookie字符串"""
    return "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])


if __name__ == "__main__":
    # 获取淘宝相关的Cookie
    domain = "taobao.com"  # 修改为淘宝域名
    cookies = get_chrome_cookies(domain)

    print(f"找到 {len(cookies)} 个淘宝Cookie:")
    for i, cookie in enumerate(cookies, 1):
        print(f"\nCookie {i}:")
        print(f"域名: {cookie['domain']}")
        print(f"名称: {cookie['name']}")
        print(f"值: {cookie['value']}")
        print(f"路径: {cookie['path']}")
        print(f"过期时间: {cookie['expires']}")

    # 生成标准Cookie字符串
    cookie_string = format_cookie_string(cookies)
    print("\n\n标准Cookie字符串:")
    print(cookie_string)

    # 将Cookie字符串保存到文件
    with open("taobao_cookies.txt", "w", encoding="utf-8") as f:
        f.write(cookie_string)
    print(f"\nCookie字符串已保存到 taobao_cookies.txt")