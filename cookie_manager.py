import os
import sys
import time
import json
import threading
import queue
import traceback
import subprocess
from DrissionPage import ChromiumOptions, ChromiumPage

class CookieManager:
    """Cookie管理类，负责获取和管理Chrome用户的Cookie"""

    def __init__(self, chrome_manager):
        self.chrome_manager = chrome_manager
        self.running = False
        self.threads = []
        self.profile_queue = queue.Queue()
        self.completed_count = 0
        self.total_count = 0
        self.logged_in_count = 0
        self.not_logged_in_count = 0
        self.lock = threading.Lock()
        self.window = None

    def set_window(self, window):
        """设置窗口引用，用于更新UI"""
        self.window = window

    def get_cookies(self, profile_ids, max_threads=1):
        """获取指定用户的Cookie（单线程版本）"""
        if self.running:
            return {"success": False, "message": "任务已在运行中"}

        # 重置计数器
        self.completed_count = 0
        self.total_count = len(profile_ids)
        self.logged_in_count = 0
        self.not_logged_in_count = 0

        # 清空队列
        while not self.profile_queue.empty():
            try:
                self.profile_queue.get_nowait()
            except queue.Empty:
                break

        # 将所有用户ID添加到队列
        for profile_id in profile_ids:
            self.profile_queue.put(profile_id)

        # 设置运行状态
        self.running = True

        # 确保UI中的停止按钮可用
        if self.window:
            try:
                self.window.evaluate_js("isTaskRunning = true; document.getElementById('stopBtn').disabled = false;")
                print("已启用停止按钮")
            except Exception as e:
                print(f"启用停止按钮失败: {e}")

        # 创建并启动单个线程
        thread = threading.Thread(target=self._process_all_profiles, args=(profile_ids,))
        thread.daemon = True
        thread.start()
        self.threads = [thread]

        return {"success": True, "message": f"已启动单线程处理{len(profile_ids)}个用户"}

    def stop_cookies(self):
        """停止获取Cookie任务，并立即结束所有相关进程"""
        if not self.running:
            return {"success": False, "message": "没有正在运行的任务"}

        print("正在停止Cookie获取任务...")
        self.running = False

        # 清空队列
        while not self.profile_queue.empty():
            try:
                self.profile_queue.get_nowait()
            except queue.Empty:
                break

        # 立即结束所有Chrome进程
        self._kill_chrome_processes()

        # 结束所有confirm_receipt.exe进程
        try:
            print("尝试关闭所有confirm_receipt进程...")
            if os.name == 'nt':  # Windows
                # 使用taskkill命令强制关闭所有confirm_receipt进程
                subprocess.run(['taskkill', '/F', '/IM', 'confirm_receipt.exe'],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE,
                               shell=True)
                print("已执行taskkill命令关闭confirm_receipt进程")
            else:  # Linux/Mac
                # 使用pkill命令强制关闭所有confirm_receipt进程
                subprocess.run(['pkill', '-f', 'confirm_receipt'],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE)
                print("已执行pkill命令关闭confirm_receipt进程")
        except Exception as e:
            print(f"关闭confirm_receipt进程失败: {e}")
            traceback.print_exc()

        # 更新UI状态
        if self.window:
            try:
                self.window.evaluate_js("resetTaskStatus();")
                print("已重置UI任务状态")
            except Exception as e:
                print(f"重置UI任务状态失败: {e}")

        return {"success": True, "message": "已停止Cookie获取任务并结束所有相关进程"}

    def _process_all_profiles(self, profile_ids):
        """单线程处理所有用户的Cookie获取"""
        print(f"开始单线程处理{len(profile_ids)}个用户的Cookie获取")

        for profile_id in profile_ids:
            # 检查是否需要停止
            if not self.running:
                print("Cookie获取任务被停止")
                break

            try:
                print(f"开始处理用户 {profile_id} 的Cookie获取")

                # 获取用户配置
                profile = self.chrome_manager.get_profile_by_id(profile_id)
                if not profile:
                    print(f"未找到ID为{profile_id}的用户配置")
                    self._update_progress()
                    continue

                # 确保之前的Chrome进程已关闭
                self._kill_chrome_processes()
                time.sleep(1)  # 减少等待时间，从2秒减少到1秒

                # 打开浏览器
                print(f"正在为用户 {profile['name']} 打开浏览器...")
                page = self._open_browser_with_profile(profile)
                if not page:
                    print(f"打开用户{profile['name']}的浏览器失败")
                    self._update_progress()
                    continue

                # 获取Cookie
                print(f"正在获取用户 {profile['name']} 的Cookie...")
                self._get_cookie_from_browser(page, profile)

                # 关闭浏览器
                print(f"正在关闭用户 {profile['name']} 的浏览器...")
                try:
                    page.quit()
                except Exception as e:
                    print(f"关闭浏览器失败: {e}")

                # 确保Chrome进程已关闭
                self._kill_chrome_processes()

                # 更新进度
                self._update_progress()

                # 等待一段时间再处理下一个用户
                print(f"等待1秒后处理下一个用户...")
                time.sleep(1)  # 减少等待时间，从3秒减少到1秒

            except Exception as e:
                print(f"处理用户{profile_id}时出错: {e}")
                traceback.print_exc()
                self._update_progress()
                # 确保Chrome进程已关闭
                self._kill_chrome_processes()

        print("所有用户的Cookie获取任务已完成")
        self.running = False

    def _worker_thread(self):
        """工作线程，处理队列中的用户（已弃用，保留兼容性）"""
        while self.running:
            try:
                # 获取下一个用户ID
                profile_id = self.profile_queue.get(block=False)
            except queue.Empty:
                break

            try:
                # 获取用户配置
                profile = self.chrome_manager.get_profile_by_id(profile_id)
                if not profile:
                    print(f"未找到ID为{profile_id}的用户配置")
                    self._update_progress()
                    continue

                # 打开浏览器
                page = self._open_browser_with_profile(profile)
                if not page:
                    print(f"打开用户{profile['name']}的浏览器失败")
                    self._update_progress()
                    continue

                # 获取Cookie
                self._get_cookie_from_browser(page, profile)

                # 关闭浏览器
                try:
                    page.quit()
                except:
                    pass

                # 更新进度
                self._update_progress()

            except Exception as e:
                print(f"处理用户{profile_id}时出错: {e}")
                self._update_progress()

        print("工作线程结束")

    def _open_browser_with_profile(self, profile):
        """使用指定的用户配置打开浏览器"""
        try:
            # 使用ChromeManager打开浏览器
            return self.chrome_manager.open_browser_with_profile(profile)
        except Exception as e:
            print(f"打开浏览器失败: {e}")
            return None

    def _get_cookie_from_browser(self, page, profile):
        """从浏览器获取Cookie"""
        try:
            # 打开淘宝首页
            page.get("https://www.taobao.com")
            print(f"已打开淘宝首页，用户: {profile['name']}")

            # 等待页面加载 - 使用更高效的等待方式
            try:
                # 尝试使用DrissionPage的等待方法
                if hasattr(page, 'wait') and hasattr(page.wait, 'load_complete'):
                    page.wait.load_complete()
                    print("使用wait.load_complete等待页面加载")
                else:
                    # 回退到短暂等待
                    time.sleep(1)
                    print("使用time.sleep(1)等待页面加载")
            except Exception as e:
                print(f"等待页面加载出错: {e}")
                # 出错时使用短暂等待
                time.sleep(1)

            # 再打开订单页面
            page.get("https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm")
            print(f"已打开淘宝订单页面，用户: {profile['name']}")

            # 等待页面加载 - 使用更高效的等待方式
            try:
                # 尝试使用DrissionPage的等待方法
                if hasattr(page, 'wait') and hasattr(page.wait, 'load_complete'):
                    page.wait.load_complete()
                    print("使用wait.load_complete等待页面加载")
                else:
                    # 回退到短暂等待
                    time.sleep(1)
                    print("使用time.sleep(1)等待页面加载")
            except Exception as e:
                print(f"等待页面加载出错: {e}")
                # 出错时使用短暂等待
                time.sleep(1)

            # 检查登录状态
            is_logged_in = self._check_login_status(page)

            # 获取当前时间
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")

            # 更新用户状态
            with self.lock:
                if is_logged_in:
                    self.logged_in_count += 1

                    # 获取淘宝Cookie
                    cookies_str = page.cookies().as_str()

                    # 获取支付宝Cookie
                    alipay_cookies_str = ""
                    try:
                        # 打开支付宝网站
                        print(f"正在访问支付宝网站获取Cookie，用户: {profile['name']}")
                        page.get("https://passport.taobao.com/ac/alipay_management.htm?fromSite=0&bizcode=taobao")

                        # 等待页面加载
                        try:
                            if hasattr(page, 'wait') and hasattr(page.wait, 'load_complete'):
                                page.wait.load_complete()
                                print("使用wait.load_complete等待支付宝页面加载")
                            else:
                                time.sleep(2)  # 支付宝页面可能需要更长的加载时间
                                print("使用time.sleep(2)等待支付宝页面加载")
                        except Exception as e:
                            print(f"等待支付宝页面加载出错: {e}")
                            time.sleep(2)
                        xp = page.ele('xpath://*[@id="ice-container"]/div/div[2]/div[1]/div[2]/div/div[1]', timeout=10)
                        if xp:
                            xp.click()
                        time.sleep(4)
                        # 获取支付宝Cookie
                        alipay_cookies_str = page.cookies().as_str()
                        print(f"已获取支付宝Cookie，用户: {profile['name']}")
                    except Exception as e:
                        print(f"获取支付宝Cookie失败: {e}")
                        # 获取支付宝Cookie失败不影响整体流程

                    # 更新配置，包括淘宝Cookie和支付宝Cookie
                    self._update_profile_config(profile['id'], cookies_str, is_logged_in, current_time, alipay_cookies_str)

                    # 更新UI - 确保UI更新与表格列顺序一致
                    self._update_profile_ui(profile['id'], is_logged_in, current_time)

                    print(f"用户{profile['name']}已登录，已获取Cookie")
                    return {"success": True, "is_logged_in": True}
                else:
                    self.not_logged_in_count += 1

                    # 更新配置
                    self._update_profile_config(profile['id'], "", is_logged_in, current_time, "")

                    # 更新UI - 确保UI更新与表格列顺序一致
                    self._update_profile_ui(profile['id'], is_logged_in, current_time)

                    print(f"用户{profile['name']}未登录")
                    return {"success": True, "is_logged_in": False}

        except Exception as e:
            print(f"获取Cookie失败: {e}")
            # 尝试更新UI，显示错误状态
            try:
                current_time = time.strftime("%Y-%m-%d %H:%M:%S")
                self._update_profile_config(profile['id'], "", False, current_time, "")
                self._update_profile_ui(profile['id'], False, current_time)
            except:
                pass
            return {"success": False, "message": str(e)}

    def _check_login_status(self, page):
        """检查登录状态"""
        # 检查当前URL是否包含login.taobao.com
        current_url = page.url
        print(f"当前URL: {current_url}")

        # 如果URL包含login.taobao.com，表示未登录
        if "login.taobao.com" in current_url:
            print("检测到登录页面，用户未登录")
            return False
        else:
            # 不包含login.taobao.com，表示已经登录
            print("用户已登录")
            return True

    def _update_profile_config(self, profile_id, cookies_str, is_logged_in, last_check, alipay_cookies_str=""):
        """更新用户配置，优先使用旧格式ID"""
        try:
            # 使用集中配置管理器获取配置
            from config_manager import config_manager

            # 获取用户配置对象
            profile = self.chrome_manager.get_profile_by_id(profile_id)
            if not profile:
                print(f"未找到ID为{profile_id}的用户配置")
                return False

            # 使用旧格式ID (profile_数字)
            old_id = profile.get('id')
            if not old_id:
                print(f"用户{profile_id}没有旧格式ID，使用原始ID")
                old_id = profile_id

            # 获取用户配置数据
            profile_data = config_manager.get_profile(old_id)

            # 更新Cookie和登录状态
            if is_logged_in and cookies_str:
                profile_data['cookie'] = cookies_str
                profile_data['status'] = '正常'

                # 更新支付宝Cookie（如果有）
                if alipay_cookies_str:
                    profile_data['alipay'] = alipay_cookies_str
                    print(f"已更新用户{old_id}的支付宝Cookie")
            else:
                profile_data['cookie'] = ''
                profile_data['status'] = '账号失效'
                # 不清除支付宝Cookie，因为它可能仍然有效

            # 更新最后检查时间
            profile_data['last_check'] = last_check

            # 添加display_name字段，保存用户名
            profile_data['display_name'] = profile.get('name', '')

            # 确保chrome_user_name字段存在
            if 'chrome_user_name' not in profile_data:
                profile_data['chrome_user_name'] = profile.get('name', '')

            # 确保profile_dir字段存在
            if 'profile_dir' not in profile_data:
                profile_data['profile_dir'] = profile.get('profile_dir', '')

            # 保存chrome_id字段以便兼容
            if 'chrome_id' not in profile_data and profile.get('chrome_id'):
                profile_data['chrome_id'] = profile.get('chrome_id')

            # 更新配置（使用旧格式ID）
            config_manager.update_profile(old_id, profile_data)

            # 保存配置
            success = config_manager.save_config()

            if success:
                print(f"已更新用户{old_id}的配置")
            else:
                print(f"保存用户{old_id}的配置失败")

            return success
        except Exception as e:
            print(f"更新用户配置失败: {e}")
            traceback.print_exc()
            return False

    def _update_profile_ui(self, profile_id, is_logged_in, last_check):
        """更新用户UI状态"""
        if self.window:
            try:
                js_code = f"""
                updateProfileStatus('{profile_id}', {str(is_logged_in).lower()}, '{last_check}');
                """
                self.window.evaluate_js(js_code)
            except Exception as e:
                print(f"更新用户UI状态失败: {e}")

    def _kill_chrome_processes(self):
        """强制关闭所有Chrome进程"""
        try:
            print("尝试关闭所有Chrome进程...")
            if os.name == 'nt':  # Windows
                # 使用taskkill命令强制关闭所有Chrome进程
                subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE,
                               shell=True)
                print("已执行taskkill命令关闭Chrome进程")
            else:  # Linux/Mac
                # 使用pkill命令强制关闭所有Chrome进程
                subprocess.run(['pkill', '-f', 'chrome'],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE)
                print("已执行pkill命令关闭Chrome进程")
            return True
        except Exception as e:
            print(f"关闭Chrome进程失败: {e}")
            traceback.print_exc()
            return False

    def _update_progress(self):
        """更新进度"""
        with self.lock:
            self.completed_count += 1

            # 更新UI
            if self.window:
                try:
                    js_code = f"""
                    updateTaskProgress({self.completed_count}, {self.total_count});
                    document.getElementById('loggedInCounter').textContent = '{self.logged_in_count}';
                    document.getElementById('notLoggedInCounter').textContent = '{self.not_logged_in_count}';
                    // 确保停止按钮可用
                    isTaskRunning = true;
                    document.getElementById('stopBtn').disabled = false;
                    """
                    self.window.evaluate_js(js_code)
                except Exception as e:
                    print(f"更新进度失败: {e}")

            # 检查是否所有任务都已完成
            if self.completed_count >= self.total_count:
                self.running = False
                print("所有任务已完成")
