import os
import sys
import json
import time
import hashlib
import re
import requests
import csv
from urllib.parse import quote, unquote
import urllib3
from datetime import datetime
import threading

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def goToken(cookie):
    """获取淘宝API所需的token"""
    t = str(int(time.time()))
    date = '{}'
    xapi = 'mtop.tmall.kangaroo.core.service.route.aldlampservicefixedresv2'
    xv = '1.0'
    token = ''
    str1 = token + '&' + t + '&12574478&' + date
    str2 = bytes(str1, encoding='utf-8')  # md5
    sign = hashlib.md5(str2).hexdigest()
    data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=2019@weex_h5_0.12.14&data=' + str(
        quote(date, 'utf-8'))
    url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
    head = {
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
        'cookie': cookie,
        'url': url2
    }
    r = requests.get(url2, timeout=20, headers=head, stream=False, verify=False)
    if r.text.find('为空') != -1 or r.text.find('过期') != -1:
        set_cookie = str(r.headers.get('Set-Cookie'))
        mk = '_m_h5_tk=' + set_cookie.split('_m_h5_tk=')[1].split(';')[0] + ';'
        enc = '_m_h5_tk_enc=' + set_cookie.split('_m_h5_tk_enc=')[1].split(';')[0] + ';'
        return mk + enc
    else:
        return ''

def decode_unicode2(text):
    """解码Unicode字符串，如\u6D9B913913"""
    if not text:
        return text

    try:
        # 替换Unicode编码字符
        pattern = re.compile(r'\\u([0-9a-fA-F]{4})')

        def replace_unicode(match):
            hex_val = match.group(1)
            return chr(int(hex_val, 16))

        decoded = pattern.sub(replace_unicode, text)
        return decoded
    except Exception as e:
        print(f"Unicode解码失败: {e}")
        return text

def is_time_in_range(time_str, start_time_str, end_time_str):
    """
    判断给定的时间字符串是否在指定的时间范围内

    参数:
        time_str (str): 需要判断的时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"
        start_time_str (str): 开始时间字符串，格式为 "YYYY-MM-DD"
        end_time_str (str): 结束时间字符串，格式为 "YYYY-MM-DD"

    返回:
        bool: 如果时间在范围内返回True，否则返回False
    """
    try:
        # 首先将time_str格式化为只有日期部分 "YYYY-MM-DD"
        if time_str and len(time_str) > 10:
            # 如果包含时分秒，只取日期部分
            date_only = time_str.split(' ')[0]
        else:
            date_only = time_str

        # 如果开始时间和结束时间都为空，则认为在范围内
        if not start_time_str and not end_time_str:
            return True

        # 如果只有开始时间，检查日期是否大于等于开始时间
        if start_time_str and not end_time_str:
            return date_only >= start_time_str

        # 如果只有结束时间，检查日期是否小于等于结束时间
        if not start_time_str and end_time_str:
            return date_only <= end_time_str

        # 如果开始时间和结束时间都有，检查日期是否在范围内
        return start_time_str <= date_only <= end_time_str

    except Exception as e:
        print(f"时间范围判断出错: {e}")
        return False  # 出错时返回False

def hqddxq(cookie, orderId):
    """获取订单详情"""
    if str(cookie).find('_m_h5_tk') == -1:  # 不带_m_h5_tk
        h5 = goToken(cookie)
        cookie = h5 + cookie
    date = '{"source":"1","bizOrderId":"' + orderId + '","archive":"0","serverV2":"true","downgrade2native":"true","spm":"a212db.24065183","appName":"tborder","appVersion":"3.0","ttid":"201200@taobao_h5_9.18.0","requestIdentity":"#t#ip#h5","condition":"{\\"version\\":\\"1.0.0\\"}","extParams":"{\\"useNewDetail\\":true}"}'
    xapi = 'mtop.taobao.query.detail'
    xv = '1.0'
    t = str(int(time.time()))
    token = cookie.split('_m_h5_tk=')[1].split('_')[0]
    str1 = token + '&' + t + '&12574478&' + date
    str2 = bytes(str1, encoding='utf-8')  # md5
    sign = hashlib.md5(str2).hexdigest()
    data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=700407@ltao_android_10.39.11&data=' + str(
        quote(date, 'utf-8'))
    url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
    head = {
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
        'cookie': cookie}
    r = requests.get(url2, timeout=20, headers=head, stream=False, verify=False)
    print(r.text)
    try:
        itemId = str(r.json()['data']['global']['events']['utExplosure']['fields']['args']['itemId']).replace(',','-')
        text = r.json()['data']['data']['orderInfoV2']['fields']['labels']
        order_type = r.json()['data']['data']['pageHeader']['fields']['title']
        price = r.json()['data']['data']['orderInfoV2']['fields']['payDetails'][0]['value']
        orderId_time = ''
        for x in text:
            if x['name'] == '创建时间:':
                orderId_time = x['value']
                break
        return orderId_time, itemId, order_type, price
    except Exception as e:
        print(f"获取订单详情失败: {e}")
        return "", "", "", ""

def extract_specific_fields3(data, user_name, cookie, beginTime, endTime, window=None):
    """提取订单数据中的特定字段"""
    result = {}
    processed_order_ids = set()
    out_of_range = False  # 添加一个标记，表示是否超出时间范围
    found_any_items = False  # 添加一个标记，表示是否找到任何item_节点

    # 直接处理data.data下的第一层节点
    for key, value in data.get("data", {}).get("data", {}).items():
        if key.startswith("item_"):
            found_any_items = True  # 找到了至少一个item_节点
            # 获取orderId
            order_id = value.get("fields", {}).get("basicInfo", {}).get("orderId")
            # 如果orderId已存在或为None，跳过此项
            if not order_id or order_id in processed_order_ids:
                continue

            # 显示当前正在导出的订单号
            if window:
                window.evaluate_js(f"document.getElementById('currentExportStatus').textContent = '正在导出订单: {order_id}'")

            order_time, itemId, order_type, price = hqddxq(cookie, order_id)  # 获取订单详情，返回提交时间和商品ID

            # 检查订单时间是否在范围内
            if not is_time_in_range(order_time, beginTime, endTime):
                print('不在时间范围之内')

                # 判断是早于开始时间还是晚于结束时间
                date_only = order_time.split(' ')[0] if ' ' in order_time else order_time

                if beginTime and date_only < beginTime:
                    # 订单时间早于开始时间，停止获取下一页
                    # 因为淘宝订单是按时间倒序排列的，后面的订单只会更早
                    print('订单时间早于开始时间，停止获取下一页')
                    out_of_range = True
                    break
                else:
                    # 订单时间晚于结束时间，继续检查下一个订单
                    print('订单时间晚于结束时间，继续检查下一个订单')
                    continue

            promotion = str(price).replace('￥','')
            account = unquote(cookie.split('tracknick=')[1].split(';')[0])
            account = decode_unicode2(account)

            # 提取特定字段
            item_data = {
                "orderId": order_id,
                "order_time": order_time,
                "itemId": itemId,
                "promotion": float(promotion) if promotion.replace('.', '', 1).isdigit() else 0.0,
                "title": value.get("fields", {}).get("item", {}).get("title"),
                "skuText": value.get("fields", {}).get("item", {}).get("skuText"),
                "quantity": value.get("fields", {}).get("item", {}).get("quantity"),
                "status": value.get("fields", {}).get("queryParams", {}).get("status"),
                "shopName": value.get("fields", {}).get("queryParams", {}).get("shopName"),
                "user_name": account,
                'chrome_user': user_name,
                'order_type': order_type
            }
            processed_order_ids.add(order_id)

            if order_id in result:
                # 如果存在，累加promotion的值
                result[order_id]['promotion'] += item_data['promotion']
            else:
                # 如果不存在，添加到结果字典中
                result[order_id] = item_data



    # 如果没有找到任何item_节点，也设置out_of_range为True
    if not found_any_items:
        print('没有找到任何订单数据，设置out_of_range为True')
        out_of_range = True

    final_result = list(result.values())
    return final_result, out_of_range  # 返回结果和标记

def hqdd_1(cookie, beginTime, endTime, sort, user_name, window=None, csv_file=None, columns_map=None, header_written=None, callback=None):
    """获取订单数据的实际实现"""
    all_results = []  # 创建列表存储所有页的结果
    for i in range(50):
        t = str(int(time.time()))
        date = '{"OrderType":"OrderFilter","appName":"tborder","appVersion":"3.0","condition":"{\\"categoryText\\":[null],\\"filterSelectInfo\\":{\\"isFilterResult\\":true,\\"selectedCategoryIndex\\":\\"-1\\",\\"selectedEndTime\\":\\"' + endTime + '\\",\\"selectedEndTimeIndex\\":\\"1\\",\\"selectedGiftIndex\\":\\"-1\\",\\"selectedSourceIndex\\":\\"-1\\",\\"selectedStartTime\\":\\"' + beginTime + '\\",\\"selectedStartTimeIndex\\":\\"1\\",\\"selectedTimeIndex\\":\\"-1\\"},\\"onlyGiftFilter\\":\\"false\\",\\"orderFilterExtParam\\":{\\"beginTime\\":\\"' + beginTime + '\\",\\"endTime\\":\\"' + endTime + '\\",\\"filterSource\\":\\"\\",\\"giftType\\":\\"\\"},\\"version\\":\\"1.0.0\\",\\"wordType\\":\\"3\\"}","page":"' + str(
            i + 1) + '","tabCode":"' + sort + '","templateConfigVersion":"0"}'
        print(date)
        xapi = 'mtop.taobao.order.queryboughtlistv2'
        xv = '1.0'
        token = cookie.split('_m_h5_tk=')[1].split('_')[0]
        str1 = token + '&' + t + '&12574478&' + date
        str2 = bytes(str1, encoding='utf-8')  # md5
        sign = hashlib.md5(str2).hexdigest()
        data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=700407@ltao_android_10.39.11&data=' + str(
            quote(date, 'utf-8'))
        url2 = 'https://trade-acs.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
        head = {
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
            'cookie': cookie,
            'url': url2}

        try:
            r = requests.get(url2, timeout=20, headers=head, stream=False, verify=False)
            print(r.text)
            # 调用extract_specific_fields3函数
            page_result, out_of_range = extract_specific_fields3(r.json(), user_name, cookie, beginTime, endTime, window)

            # 将当前页结果添加到总结果中
            all_results.extend(page_result)
            # 更新进度
            if callback:
                callback(f"用户 {user_name} 已获取 {len(all_results)} 条订单数据")

            print(f"已获取第{i + 1}页订单数据，共{len(page_result)}条")
            if out_of_range:
                print("发现订单不在时间范围内，停止获取下一页")
                break  # 如果发现订单不在时间范围内，停止获取下一页
        except Exception as e:
            print(f"获取订单数据失败: {e}")
            continue

    print(f"总共获取到{len(all_results)}条订单数据")
    return all_results
# 订单状态映射
ORDER_STATUS_MAP = {
    'all': 'ALL',           # 全部订单
    'waitPay': 'NOT_PAID',  # 待付款
    'waitSend': 'PAID',     # 待发货
    'waitConfirm': 'SEND',  # 待收货
    'waitRate': 'TRADE_FINISHED'  # 待评价
}

def date_to_timestamp(date_str):
    """将日期字符串转换为13位时间戳"""
    if not date_str:
        return ""
    try:
        # 假设日期格式为 YYYY-MM-DD
        dt = datetime.strptime(date_str, "%Y-%m-%d")
        # 转换为毫秒级时间戳
        timestamp = int(dt.timestamp() * 1000)
        return str(timestamp)
    except Exception as e:
        print(f"日期转换错误: {e}")
        return ""

def convert_order_data(json_data, user2, user_name):
    """将新接口的数据转换为旧接口格式"""
    result = []
    processed_order_ids = set()  # 用于跟踪已处理的订单ID
    global_processed_order_ids = getattr(convert_order_data, 'global_processed_order_ids', set())  # 全局已处理订单ID集合

    # 检查是否有订单数据
    if 'mainOrders' not in json_data or not json_data['mainOrders']:
        return result

    # 遍历主订单
    for main_order in json_data['mainOrders']:
        order_id = main_order.get('id', '')

        # 如果订单ID已处理过（本次或之前的调用），跳过此订单
        if not order_id or order_id in processed_order_ids or order_id in global_processed_order_ids:
            print(f"跳过重复订单: {order_id}")
            continue

        # 标记此订单ID为已处理
        processed_order_ids.add(order_id)
        global_processed_order_ids.add(order_id)  # 添加到全局集合

        order_info = main_order.get('orderInfo', {})
        order_time = f"{order_info.get('createDay', '')} {order_info.get('createTime', '').split(' ')[1] if ' ' in order_info.get('createTime', '') else ''}"

        # 获取订单状态
        status_text = main_order.get('statusInfo', {}).get('text', '')

        # 获取卖家信息
        seller = main_order.get('seller', {})
        shop_name = seller.get('shopName', '')

        # 用于累计订单总价
        price = main_order.get('payInfo', {}).get('actualFee', '0')

        # 遍历子订单
        for sub_order in main_order.get('subOrders', []):
            item_info = sub_order.get('itemInfo', {})
            title = item_info.get('title', '')
            item_id = item_info.get('id', '')

            # 获取数量
            quantity = sub_order.get('quantity', '1')

            # 获取SKU信息
            sku_text = ""
            if 'skuText' in item_info and item_info['skuText']:
                sku_parts = []
                for sku in item_info['skuText']:
                    if 'name' in sku and 'value' in sku:
                        sku_parts.append(f"{sku['name']}:{sku['value']}")
                sku_text = "; ".join(sku_parts)

        # 构建与旧接口兼容的订单数据
        order_data = {
            'orderId': order_id,
            'title': title if 'title' in locals() else '',  # 使用最后一个子订单的标题
            'price': price,  # 使用最后一个子订单的价格
            'promotion': price,  # 使用累计的总价
            'status': status_text,
            'order_time': order_time,
            'itemId': item_id if 'item_id' in locals() else '',  # 使用最后一个子订单的商品ID
            'user_name': user2,
            'chrome_user': user_name,
            'order_type': status_text,
            'shopName': shop_name,
            'quantity': quantity if 'quantity' in locals() else '1',  # 使用最后一个子订单的数量
            'skuText': sku_text if 'sku_text' in locals() else ''  # 使用最后一个子订单的SKU信息
        }

        result.append(order_data)

    # 保存全局已处理订单ID集合
    convert_order_data.global_processed_order_ids = global_processed_order_ids

    return result

def hqdd_2(cookie, order_type, beginTime, endTime, user_name, window=None, callback=None):
    """
    使用新接口获取订单数据

    Args:
        cookie: 用户cookie
        beginTime: 开始时间 (YYYY-MM-DD)
        endTime: 结束时间 (YYYY-MM-DD)
        order_type: 订单类型 (all, waitPay, waitSend, waitConfirm, waitRate)
        user_name: 用户名
        window: 窗口对象(可选)
        callback: 回调函数(可选)

    Returns:
        与旧接口兼容的订单数据列表
    """
    # 转换订单状态
    status = ORDER_STATUS_MAP.get(order_type, 'ALL')

    all_results = []  # 存储所有页的结果
    found_out_of_range = False  # 标记是否找到超出日期范围的订单

    # 最多获取8页数据
    for page in range(1, 9):
        try:
            # 构建URL和请求参数
            url = 'https://buyertrade.taobao.com/trade/itemlist/asyncBought.htm'
            params = {
                'action': 'itemlist/BoughtQueryAction',
                'event_submit_do_query': '1',
                'canGetHistoryCount': 'false',
                'historyCount': '0',
                'needQueryHistory': 'false',
                'onlineCount': '0',
                'pageNum': str(page),
                'pageSize': '100',
                'queryForV2': 'false',
                'unionSearchPageNum': '0',
                'orderStatus': status,
                'prePageNo': str(page - 1) if page > 1 else '1'
            }

            # 设置请求头
            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Connection': 'keep-alive',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'Host': 'buyertrade.taobao.com',
                'Origin': 'https://buyertrade.taobao.com',
                'Referer': 'https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Cookie': cookie
            }
            user2 = cookie.split('lgc=')[1].split(';')[0]
            # 显示当前正在导出的页码
            if window:
                window.evaluate_js(f"document.getElementById('currentExportStatus').textContent = '正在导出第 {page} 页订单数据'")

            # 发送GET请求
            response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
            print(response.text)
            # 检查响应状态
            if response.status_code != 200:
                print(f"请求失败，状态码: {response.status_code}")
                break

            # 解析JSON响应
            try:
                json_data = response.json()
            except Exception as e:
                print(f"解析JSON响应失败: {e}")
                print(f"响应内容: {response.text[:200]}...")  # 只打印前200个字符
                break

            # 检查是否有错误
            if json_data.get('error'):
                print(f"API返回错误: {json_data.get('error')}")
                break

            # 检查订单日期是否在范围内
            if beginTime and 'mainOrders' in json_data and json_data['mainOrders']:
                # 检查最后一个订单的日期（因为订单是按时间倒序排列的，最后一个是最早的）
                last_order = json_data['mainOrders'][-1]
                if 'orderInfo' in last_order and 'createDay' in last_order['orderInfo']:
                    create_day = last_order['orderInfo']['createDay']
                    # 如果订单日期小于开始日期，标记为超出范围并跳出循环
                    if create_day < beginTime:
                        print(f"发现订单日期 {create_day} 早于开始日期 {beginTime}，停止获取更多页")
                        found_out_of_range = True

            # 转换数据格式前先过滤掉不在日期范围内的订单
            filtered_json_data = json_data.copy()
            if beginTime and 'mainOrders' in filtered_json_data:
                filtered_orders = []
                for order in filtered_json_data['mainOrders']:
                    if 'orderInfo' in order and 'createDay' in order['orderInfo']:
                        create_day = order['orderInfo']['createDay']
                        # 只保留日期在范围内的订单
                        if beginTime <= create_day <= endTime:
                            filtered_orders.append(order)
                        elif create_day < beginTime:
                            # 如果订单日期小于开始日期，标记为超出范围
                            found_out_of_range = True

                # 更新过滤后的订单列表
                filtered_json_data['mainOrders'] = filtered_orders

            # 转换数据格式
            page_results = convert_order_data(filtered_json_data, user2, user_name)
            all_results.extend(page_results)

            # 更新进度
            if callback:
                callback(f"用户 {user_name} 已获取 {len(all_results)} 条订单数据")

            print(f"已获取第{page}页订单数据，共{len(page_results)}条")

            # 如果发现订单日期超出范围，停止获取更多页
            if found_out_of_range:
                print("由于订单日期超出范围，停止获取更多页")
                break

            # 检查是否有更多页
            page_info = json_data.get('page', {})
            current_page = page_info.get('currentPage', 1)
            total_page = page_info.get('totalPage', 1)

            # 如果当前页是最后一页，停止获取
            if current_page >= total_page:
                print(f"已到达最后一页 {current_page}/{total_page}")
                break

            # 添加延迟，避免请求过快
            time.sleep(1)

        except Exception as e:
            print(f"获取订单数据出错: {e}")
            import traceback
            traceback.print_exc()
            if window:
                window.evaluate_js(f"showNotification(false, '获取订单数据出错: {str(e)}')")
            break

    print(f"总共获取到{len(all_results)}条订单数据")
    return all_results
def hqdd(user_name, cookie, beginTime, endTime, window=None, order_type='all', callback=None):
    """
    获取订单数据

    Args:
        user_name: 用户名
        cookie: 用户cookie
        beginTime: 开始时间
        endTime: 结束时间
        window: 窗口对象(可选)
        order_type: 订单类型(默认'all')
        callback: 回调函数(可选)

    Returns:
        订单数据列表
    """
    # 订单类型: all=全部订单, waitSend=待发货, waitConfirm=待收货, waitPay=待付款, waitRate=待评价

    # 处理cookie，删除令牌部分
    clean_cookie = cookie
    # 删除_m_h5_tk部分
    if "_m_h5_tk=" in clean_cookie:
        clean_cookie = re.sub(r'_m_h5_tk=[^;]+;', '', clean_cookie)
    # 删除_m_h5_tk_enc部分
    if "_m_h5_tk_enc=" in clean_cookie:
        clean_cookie = re.sub(r'_m_h5_tk_enc=[^;]+;', '', clean_cookie)
    h5 = goToken(clean_cookie)
    cookie = h5 + clean_cookie
    return hqdd_2(cookie, order_type, beginTime, endTime, user_name, window, callback)  # 返回订单数据

def export_orders_new(profile_ids, profile_cookies, start_date, end_date, order_type='all', export_format='csv', callback=None):
    """
    导出订单数据

    Args:
        profile_ids: 用户ID列表
        profile_cookies: 用户cookie字典
        start_date: 开始日期
        end_date: 结束日期
        order_type: 订单类型
        export_format: 导出格式(csv)
        callback: 回调函数

    Returns:
        导出结果
    """
    try:
        # 重置全局已处理订单ID集合，确保每次导出都是从零开始
        if hasattr(convert_order_data, 'global_processed_order_ids'):
            convert_order_data.global_processed_order_ids = set()
            print("已重置全局已处理订单ID集合")

        if callback:
            callback("开始导出订单数据...")

        # 创建文件名
        timestamp = time.strftime("%Y%m%d%H%M%S")
        order_type_name = {
            'all': '全部订单',
            'waitPay': '待付款',
            'waitSend': '待发货',
            'waitConfirm': '待收货',
            'waitRate': '待评价'
        }.get(order_type, '全部订单')

        filename = f"淘宝订单_{order_type_name}_{timestamp}"

        # 确保导出目录存在
        try:
            # 获取应用程序根目录（打包后也能正确工作）
            if getattr(sys, 'frozen', False):
                # 如果是打包后的环境
                app_root = os.path.dirname(sys.executable)
            else:
                # 如果是开发环境 - 使用当前文件所在目录
                current_file_dir = os.path.dirname(os.path.abspath(__file__))
                # 获取taobao_order_system目录
                app_root = current_file_dir

            # 创建order_exports文件夹 - 与应用程序同级
            exports_dir = os.path.join(app_root, "order_exports")

            # 打印导出目录路径，便于调试
            print(f"当前文件目录: {os.path.abspath(__file__)}")
            print(f"应用程序根目录: {app_root}")
            print(f"导出目录路径: {exports_dir}")
        except Exception as e:
            print(f"计算导出目录路径时出错: {e}")
            # 使用当前工作目录作为备选
            exports_dir = os.path.join(os.getcwd(), "order_exports")
            print(f"使用备选导出目录路径: {exports_dir}")
        if not os.path.exists(exports_dir):
            os.makedirs(exports_dir)

        # 定义文件路径 - 只支持CSV格式
        file_path = os.path.join(exports_dir, f"{filename}.csv")

        # 收集所有用户的订单数据
        all_orders = []

        # 处理每个用户
        for i, profile_id in enumerate(profile_ids):
            if profile_id not in profile_cookies:
                if callback:
                    callback(f"用户 {profile_id} 没有cookie，跳过")
                continue

            cookie = profile_cookies[profile_id]

            if callback:
                callback(f"正在获取用户 {profile_id} 的订单数据...")

            # 调用hqdd函数获取订单数据
            try:
                orders = hqdd(profile_id, cookie, start_date, end_date, None, order_type, callback)

                # 添加到总订单列表
                all_orders.extend(orders)

                if callback:
                    callback(f"已处理 {i+1}/{len(profile_ids)} 个用户，当前共获取 {len(all_orders)} 条订单数据")
            except Exception as e:
                print(f"获取用户 {profile_id} 的订单数据失败: {e}")
                if callback:
                    callback(f"获取用户 {profile_id} 的订单数据失败: {str(e)}")

        # 检查是否有数据被获取
        if len(all_orders) == 0:
            if callback:
                callback("没有找到符合条件的订单数据")
            return {
                "success": False,
                "message": "没有找到符合条件的订单数据"
            }

        # 将所有订单数据写入CSV文件
        try:
            # 定义列映射
            columns_map = {
                'orderId': '订单号',
                'title': '商品名称',
                'user_name': '用户名',
                'promotion': '金额',
                'status': '状态',
                'order_time': '创建时间',
                'chrome_user': 'Chrome用户',
                'order_type': '订单类型',
                'shopName': '店铺名称',
                'quantity': '数量',
                'skuText': '规格'
            }

            # 写入CSV文件
            with open(file_path, mode='w', newline='', encoding='utf-8-sig') as f:
                # 获取所有可能的字段
                fieldnames = list(columns_map.keys())
                # 创建CSV写入器
                writer = csv.DictWriter(f, fieldnames=[columns_map.get(field, field) for field in fieldnames])
                # 写入表头
                writer.writeheader()

                # 写入数据行
                for order in all_orders:
                    row_data = {}
                    for field in fieldnames:
                        if field in order:
                            row_data[columns_map.get(field, field)] = order[field]
                    writer.writerow(row_data)

            if callback:
                callback(f"订单导出完成，共导出 {len(all_orders)} 条订单数据")

            return {
                "success": True,
                "message": f"订单导出完成，共导出 {len(all_orders)} 条订单数据",
                "file_path": file_path,
                "total_orders": len(all_orders)
            }
        except Exception as e:
            print(f"写入CSV文件失败: {e}")
            if callback:
                callback(f"写入CSV文件失败: {str(e)}")
            return {
                "success": False,
                "message": f"写入CSV文件失败: {str(e)}"
            }
    except Exception as e:
        import traceback
        traceback.print_exc()
        if callback:
            callback(f"导出订单数据失败: {str(e)}")
        return {
            "success": False,
            "message": f"导出订单数据失败: {str(e)}"
        }
