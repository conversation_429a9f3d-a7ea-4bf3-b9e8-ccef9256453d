import os
import sys
import time
import json
import threading
import traceback
import datetime
import random
import multiprocessing
import shutil
import tempfile
import pandas as pd
from DrissionPage import ChromiumOptions, ChromiumPage

from concurrent.futures import ThreadPoolExecutor
# 导入配置管理器
from config_manager import config_manager
# 使用DrissionPage
USING_SELENIUM = False

class OrderManager:
    """订单管理类，处理下单、确认收货等操作"""

    def __init__(self, chrome_manager):
        self.chrome_manager = chrome_manager
        self.running = False
        self.threads = {}
        self.max_threads = 6  # 默认最大线程数
        self.retry_count = 3  # 默认重试次数
        self.timeout = 20     # 默认超时时间（秒）
        self.lock = threading.Lock()
        self.task_lock = threading.Lock()  # 添加task_lock用于任务状态更新
        self.load_config()
        self.task_status = {"running": False, "total_tasks": 0, "completed_tasks": 0}
        self.order_threads = {}
        self.orders = []  # 用于存储订单数据
        self.thread_pool = ThreadPoolExecutor(max_workers=5)
        self.order_lock = threading.Lock()
        self.stop_event = threading.Event()

    def load_config(self):
        """加载配置文件"""
        try:
            # 使用集中配置管理器获取配置
            config = config_manager.get_config()
            self.max_threads = config.get('max_concurrent_browsers', 3)
            self.retry_count = config.get('retry_count', 3)
            self.timeout = config.get('timeout', 30)
            print(f"从配置管理器加载配置成功")
        except Exception as e:
            print(f"加载配置失败: {e}")
            traceback.print_exc()

    def save_config(self):
        """保存配置到文件"""
        try:
            # 使用集中配置管理器保存配置
            config = config_manager.get_config()

            # 更新配置
            config['max_concurrent_browsers'] = self.max_threads
            config['retry_count'] = self.retry_count
            config['timeout'] = self.timeout

            # 保存配置
            config_manager.update_config(config)
            success = config_manager.save_config()

            if success:
                print(f"配置保存成功: {config_manager.get_config_path()}")
            else:
                print(f"配置保存失败")
            return success
        except Exception as e:
            print(f"保存配置失败: {e}")
            traceback.print_exc()
            return False

    def place_order(self, driver, order_url, timeout=30):
        """
        处理单个商品的下单流程 - 简化版，直接点击下单按钮

        Args:
            driver: DrissionPage实例
            order_url: 商品链接
            timeout: 操作超时时间（秒），用于设置等待元素的超时时间

        Returns:
            bool: 下单是否成功
        """
        # 保存timeout到实例变量，以便在方法中使用
        self.timeout = timeout
        try:

            # 直接使用get方法打开链接
            driver.get(order_url)
            print(f"已打开下单链接: {order_url}")

            # 等待页面加载完成
            try:
                # 尝试使用不同的等待方法
                if hasattr(driver.wait, 'load_complete'):
                    driver.wait.load_complete()
                elif hasattr(driver, 'wait_load_complete'):
                    driver.wait_load_complete()
                elif hasattr(driver, 'wait_for_load_complete'):
                    driver.wait_for_load_complete()
                else:
                    # 如果没有特定的等待方法，使用通用方法
                    time.sleep(3)  # 简单等待3秒
                    print("使用简单等待方法")
            except Exception as e:
                print(f"等待页面加载完成出错: {e}")
                # 出错时使用简单等待
                time.sleep(3)

            # 检查是否重定向到登录页面
            current_url = driver.url
            if "login.taobao.com" in current_url:
                print("检测到需要登录，当前URL包含login.taobao.com")
                return False



            # 使用指定的XPath选择器查找提交订单按钮
            submit_button_xpath = '//*[@id="submitBlock_1"]/div/div/div/div[3]/div[2]/span'
            print(f"使用指定选择器查找提交订单按钮: {submit_button_xpath}")

            # 直接使用JavaScript点击按钮
            print("使用JavaScript直接点击按钮...")
            result = driver.run_js('''
                var btn = document.evaluate('//*[@id="submitBlock_1"]/div/div/div/div[3]/div[2]/span',
                    document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if(btn) {
                    // 滚动到按钮位置
                    btn.scrollIntoView({block: "center"});
                    // 点击按钮
                    btn.click();
                    return true;
                }
                return false;
            ''')
            if result:
                print("成功点击提交订单按钮")
            else:
                print("未找到提交订单按钮")
                return False

            print("已点击提交订单按钮，等待4秒...")

            # 等待4秒，让页面响应
            time.sleep(4)

            # 检查是否下单成功（通常会跳转到支付页面或显示订单成功的提示）
            success = False
            current_url = driver.url
            page_source = driver.html

            if "cashier" in current_url or "pay" in current_url:
                success = True
                print("下单成功：已跳转到支付页面")
            elif "成功" in page_source or "订单号" in page_source:
                success = True
                print("下单成功：页面包含成功或订单号信息")
            else:
                print(f"可能未成功下单，当前URL: {current_url}")

            print(f"商品下单{'成功' if success else '失败'}，继续执行下一个链接的下单任务")
            return success



        except Exception as e:
            print(f"下单过程出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def confirm_receipt(self, driver, order_id, pay_password='', timeout=30):
        """确认收货操作"""
        try:
            # 打开订单详情页面
            order_url = f"https://buyertrade.taobao.com/trade/detail/trade_item_detail.htm?bizOrderId={order_id}"
            driver.get(order_url)
            print(f"已打开订单详情页: {order_url}")

            # 等待页面加载
            driver.wait.load_complete()
            time.sleep(3)

            # 等待"确认收货"按钮出现
            confirm_button_xpath = '//button[contains(text(), "确认收货") or contains(@class, "confirmBtn")]'
            print("等待确认收货按钮出现...")

            confirm_button = driver.ele(f'xpath:{confirm_button_xpath}', timeout=timeout)
            if not confirm_button:
                print("未找到确认收货按钮")
                return False

            # 点击确认收货按钮
            print("点击确认收货按钮...")
            confirm_button.click()

            # 等待确认框出现
            confirm_dialog_xpath = '//div[contains(@class, "confirmDialog") or contains(@class, "modal")]'
            time.sleep(1)
            confirm_dialog = driver.ele(f'xpath:{confirm_dialog_xpath}', timeout=timeout)
            if not confirm_dialog:
                print("未找到确认对话框")
                return False

            # 点击确认
            final_confirm_xpath = '//button[contains(text(), "确定") or contains(@class, "confirmBtn")]'
            final_confirm = driver.ele(f'xpath:{final_confirm_xpath}', timeout=timeout)
            if not final_confirm:
                print("未找到最终确认按钮")
                return False

            final_confirm.click()

            # 检查是否需要输入支付密码
            password_input_xpath = '//input[@type="password" and contains(@class, "payPassword")]'
            password_input = driver.ele(f'xpath:{password_input_xpath}', timeout=5)

            if password_input:
                # 如果有支付密码，则输入
                if pay_password:
                    print("输入支付密码...")
                    password_input.input(pay_password)

                    # 点击确认按钮
                    submit_button_xpath = '//button[contains(text(), "确定") or contains(@class, "submit")]'
                    submit_button = driver.ele(f'xpath:{submit_button_xpath}', timeout=5)
                    if submit_button:
                        submit_button.click()
                    else:
                        print("未找到支付密码确认按钮")
                        return False
                else:
                    print("需要支付密码但未提供，无法完成确认收货")
                    return False

            print("已确认收货，等待2秒...")
            time.sleep(2)

            return True
        except Exception as e:
            print(f"确认收货操作失败: {e}")
            traceback.print_exc()
            return False

    def _batch_confirm_receipt(self, driver, orders, pay_password='', timeout=30):
        """
        批量确认收货操作

        Args:
            driver: DrissionPage实例
            orders: 订单列表
            pay_password: 支付密码
            timeout: 操作超时时间（秒）
        """
        # 保存timeout到实例变量，以便在方法中使用
        self.timeout = timeout
        try:
            # 打开待收货订单页面
            driver.get("https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitConfirm")

            # 等待页面加载
            try:
                # 尝试使用不同的等待方法，兼容不同版本的DrissionPage
                if hasattr(driver.wait, 'load_complete'):
                    driver.wait.load_complete()
                elif hasattr(driver, 'wait_load_complete'):
                    driver.wait_load_complete()
                elif hasattr(driver, 'wait_for_load_complete'):
                    driver.wait_for_load_complete()
                else:
                    # 如果没有特定的等待方法，使用通用方法
                    print("使用简单等待方法")
                    time.sleep(5)  # 简单等待5秒
            except Exception as e:
                print(f"等待页面加载完成出错: {e}")
                # 出错时使用简单等待
                time.sleep(5)

            # 额外等待确保页面完全加载
            time.sleep(3)

            # 检查是否重定向到登录页面
            current_url = driver.url
            if "login.taobao.com" in current_url:
                print("检测到需要登录，当前URL包含login.taobao.com")
                return False

            # 尝试勾选所有订单
            print("尝试勾选所有订单...")
            order_count = len(orders)
            checked_count = 0

            # 等待页面加载完成
            driver.wait.load_complete()

            for order_info in orders:
                order_id = order_info["order_id"]
                try:
                    # 找到对应订单的复选框
                    checkbox_xpath = f'//div[@data-id="{order_id}"]//input[@type="checkbox"]'
                    checkbox = driver.ele(f'xpath:{checkbox_xpath}', timeout=5)

                    if checkbox:
                        # 勾选复选框
                        if not checkbox.attr('checked'):
                            checkbox.click()
                            checked_count += 1
                            print(f"已勾选订单: {order_id}")
                    else:
                        print(f"未找到订单 {order_id} 的勾选框")
                except Exception as e:
                    print(f"勾选订单{order_id}失败: {e}")
                    continue

            # 如果没有勾选任何订单，返回失败
            if checked_count == 0:
                print("未能勾选任何订单，批量确认收货失败")
                return False

            # 点击批量确认收货按钮
            batch_confirm_xpath = '//button[contains(text(), "批量确认收货") or contains(@class, "batchBtn")]'
            batch_confirm_button = driver.ele(f'xpath:{batch_confirm_xpath}', timeout=5)

            if batch_confirm_button:
                batch_confirm_button.click()
                print("已点击批量确认收货按钮")
            else:
                print("未找到批量确认收货按钮，尝试使用其他方式")
                # 尝试使用JavaScript执行批量确认收货
                try:
                    driver.run_js('''
                        var btns = document.querySelectorAll('button');
                        for(var i=0; i<btns.length; i++) {
                            if(btns[i].textContent.includes('批量确认收货')) {
                                btns[i].click();
                                return true;
                            }
                        }
                        return false;
                    ''')
                    print("已通过JavaScript点击批量确认收货按钮")
                except Exception as e:
                    print(f"通过JavaScript点击批量确认收货按钮失败: {e}")
                    return False

            # 等待确认对话框出现
            time.sleep(1)
            confirm_dialog_xpath = '//div[contains(@class, "confirmDialog") or contains(@class, "modal")]'
            confirm_dialog = driver.ele(f'xpath:{confirm_dialog_xpath}', timeout=10)

            if confirm_dialog:
                # 点击确认按钮
                final_confirm_xpath = '//button[contains(text(), "确定") or contains(@class, "confirmBtn")]'
                final_confirm = driver.ele(f'xpath:{final_confirm_xpath}', timeout=5)

                if final_confirm:
                    final_confirm.click()
                    print("已点击确认按钮")
                else:
                    print("未找到确认按钮")
                    return False
            else:
                print("未找到确认对话框")
                return False

            # 检查是否需要输入支付密码
            password_input_xpath = '//input[@type="password" and contains(@class, "payPassword")]'
            password_input = driver.ele(f'xpath:{password_input_xpath}', timeout=5)

            if password_input:
                # 如果有支付密码，则输入
                if pay_password:
                    print("输入支付密码...")
                    password_input.input(pay_password)

                    # 点击确认按钮
                    submit_button_xpath = '//button[contains(text(), "确定") or contains(@class, "submit")]'
                    submit_button = driver.ele(f'xpath:{submit_button_xpath}', timeout=5)

                    if submit_button:
                        submit_button.click()
                    else:
                        print("未找到支付密码确认按钮")
                        return False
                else:
                    print("需要支付密码但未提供，无法完成批量确认收货")
                    return False

            # 等待操作完成
            print("批量确认收货操作完成，等待3秒...")
            time.sleep(3)

            # 检查是否成功
            success_message_xpath = '//div[contains(text(), "成功") or contains(@class, "success")]'
            success_message = driver.ele(f'xpath:{success_message_xpath}', timeout=5)

            if success_message:
                print("批量确认收货成功")
                return True
            else:
                # 没有找到成功提示，但不一定意味着失败
                # 刷新页面，检查订单是否还存在
                driver.refresh()
                time.sleep(2)

                # 检查是否还有待收货订单
                remaining_orders = driver.eles('css:.bought-wrapper-mod__trade-item')
                if len(remaining_orders) < order_count:
                    print(f"批量确认收货部分成功，原有{order_count}个订单，现在剩余{len(remaining_orders)}个")
                    return True
                else:
                    print("批量确认收货可能失败，订单数量未减少")
                    return False
        except Exception as e:
            print(f"批量确认收货操作失败: {e}")
            traceback.print_exc()
            return False

    def get_orders_by_type(self, profile, order_type='all', start_date=None, end_date=None):
        """
        根据订单类型获取订单数据

        Args:
            profile: 用户配置信息
            order_type: 订单类型，可选值：all(全部订单), waitPay(待付款), waitSend(待发货), waitConfirm(待收货), waitRate(待评价)
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD

        Returns:
            list: 订单数据列表
        """
        try:
            import hashlib
            import urllib.parse
            import requests
            import time
            import re
            from urllib.parse import quote

            # 获取cookie
            clean_cookie = profile.get('cookie', '')
            if not clean_cookie:
                print("用户cookie为空")
                return []

            # 处理cookie，删除令牌部分
            cookie = clean_cookie
            # 删除_m_h5_tk部分
            if "_m_h5_tk=" in clean_cookie:
                cookie = re.sub(r'_m_h5_tk=[^;]+;', '', clean_cookie)
            # 删除_m_h5_tk_enc部分
            if "_m_h5_tk_enc=" in clean_cookie:
                cookie = re.sub(r'_m_h5_tk_enc=[^;]+;', '', clean_cookie)

            # 获取token
            h5_token = self._get_token(cookie)
            if h5_token:
                cookie = h5_token + cookie
            else:
                print("无法获取token")
                return []

            # 构建请求头
            headers = {
                'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
                'cookie': cookie
            }

            # 映射订单类型到tabCode
            tab_code = {
                'all': 'all',
                'waitPay': 'waitPay',
                'waitSend': 'waitSend',
                'waitConfirm': 'waitConfirm',
                'waitRate': 'waitRate'
            }.get(order_type, 'all')

            # 构建日期筛选条件
            date_filter = ""
            if start_date and end_date:
                date_filter = f',\\"beginTime\\":\\"{start_date}\\",\\"endTime\\":\\"{end_date}\\"'

            # 获取订单列表
            orders = []
            page = 1
            max_pages = 5  # 最多获取5页数据

            while page <= max_pages:
                # 获取当前时间戳
                t = str(int(time.time()))

                # 构建请求参数
                date = f'{{"OrderType":"OrderList","appName":"tborder","appVersion":"3.0","condition":"{{\\"categoryText\\":[null],\\"filterSelectInfo\\":{{\\"isFilterResult\\":true,\\"selectedCategoryIndex\\":\\"-1\\",\\"selectedEndTime\\":\\"\\",\\"selectedEndTimeIndex\\":\\"1\\",\\"selectedGiftIndex\\":\\"-1\\",\\"selectedSourceIndex\\":\\"-1\\",\\"selectedStartTime\\":\\"\\",\\"selectedStartTimeIndex\\":\\"1\\",\\"selectedTimeIndex\\":\\"-1\\"}},\\"onlyGiftFilter\\":\\"false\\",\\"orderFilterExtParam\\":{{{date_filter}}},\\"version\\":\\"1.0.0\\",\\"wordType\\":\\"3\\"}}","page":"{page}","tabCode":"{tab_code}","templateConfigVersion":"0"}}'
                xapi = 'mtop.taobao.order.queryboughtlistv2'
                xv = '1.0'

                # 获取token
                token = cookie.split('_m_h5_tk=')[1].split(';')[0].split('_')[0] if '_m_h5_tk=' in cookie else ''
                if not token:
                    print("cookie中未找到token")
                    return []

                # 计算签名
                sign_text = token + '&' + t + '&12574478&' + date
                sign = hashlib.md5(sign_text.encode('utf-8')).hexdigest()

                # 构建URL
                data2 = f'jsv=2.4.9&appKey=12574478&t={t}&sign={sign}&api={xapi}&v={xv}&type=json&ttid=700170@taobao_android_10.27.10&dataType=json&data={quote(date, "utf-8")}'
                url2 = f'https://h5api.m.taobao.com/h5/{xapi}/{xv}/?{data2}'

                # 发送请求
                try:
                    r = requests.get(url2, timeout=20, headers=headers, stream=False, verify=False)
                    result = r.json()

                    # 检查是否登录过期
                    if 'ret' in result and any('SESSION_EXPIRED' in ret or 'Session过期' in ret for ret in result['ret']):
                        print("登录已过期，请重新获取Cookie")
                        return []

                    # 提取订单信息
                    if 'data' in result and 'data' in result['data']:
                        has_orders = False
                        for key, value in result['data']['data'].items():
                            if key.startswith("item_"):
                                has_orders = True
                                try:
                                    # 提取订单基本信息
                                    order_id = value.get('fields', {}).get('basicInfo', {}).get('orderId')
                                    if not order_id:
                                        continue

                                    # 提取商品标题
                                    title = value.get('fields', {}).get('item', {}).get('title', '未知商品')

                                    # 提取价格
                                    price_info = value.get('fields', {}).get('priceInfo', {})
                                    amount = price_info.get('realTotal', '0.00')

                                    # 提取状态
                                    status = value.get('fields', {}).get('statusInfo', {}).get('text', '未知状态')

                                    # 提取时间
                                    create_time = value.get('fields', {}).get('basicInfo', {}).get('createTime', '')
                                    pay_time = value.get('fields', {}).get('basicInfo', {}).get('payTime', '')

                                    # 添加到订单列表
                                    orders.append({
                                        'order_id': order_id,
                                        'title': title,
                                        'amount': amount,
                                        'status': status,
                                        'create_time': create_time,
                                        'pay_time': pay_time
                                    })
                                except Exception as e:
                                    print(f"解析订单数据出错: {e}")
                                    continue

                        # 如果没有订单，跳出循环
                        if not has_orders:
                            break
                    else:
                        print("接口返回数据格式错误")
                        break

                    # 增加页码
                    page += 1

                    # 随机延迟，避免请求过快
                    time.sleep(1)

                except Exception as e:
                    print(f"请求订单数据出错: {e}")
                    break

            return orders
        except Exception as e:
            print(f"获取订单数据失败: {e}")
            traceback.print_exc()
            return []

    def _get_token(self, cookie):
        """获取淘宝API所需的token"""
        try:
            import hashlib
            import requests
            import time
            from urllib.parse import quote

            t = str(int(time.time()))
            date = '{}'
            xapi = 'mtop.tmall.kangaroo.core.service.route.aldlampservicefixedresv2'
            xv = '1.0'
            token = ''
            str1 = token + '&' + t + '&12574478&' + date
            str2 = bytes(str1, encoding='utf-8')  # md5
            sign = hashlib.md5(str2).hexdigest()
            data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=2019@weex_h5_0.12.14&data=' + str(
                quote(date, 'utf-8'))
            url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
            head = {
                'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
                'cookie': cookie,
                'url': url2
            }

            r = requests.get(url2, timeout=20, headers=head, stream=False, verify=False)
            if r.text.find('为空') != -1 or r.text.find('过期') != -1:
                set_cookie = str(r.headers.get('Set-Cookie'))
                mk = '_m_h5_tk=' + set_cookie.split('_m_h5_tk=')[1].split(';')[0] + ';'
                enc = '_m_h5_tk_enc=' + set_cookie.split('_m_h5_tk_enc=')[1].split(';')[0] + ';'
                return mk + enc
            return ''
        except Exception as e:
            print(f"获取token失败: {e}")
            return ''

    def export_orders(self, driver, start_date=None, end_date=None):
        """导出订单数据"""
        try:
            # 打开订单列表页面
            orders_url = "https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm"
            driver.get(orders_url)
            print("已打开订单列表页面")

            # 等待页面加载
            try:
                # 尝试使用不同的等待方法，兼容不同版本的DrissionPage
                if hasattr(driver.wait, 'load_complete'):
                    driver.wait.load_complete()
                elif hasattr(driver, 'wait_load_complete'):
                    driver.wait_load_complete()
                elif hasattr(driver, 'wait_for_load_complete'):
                    driver.wait_for_load_complete()
                else:
                    # 如果没有特定的等待方法，使用通用方法
                    print("使用简单等待方法")
                    time.sleep(5)  # 简单等待5秒
            except Exception as e:
                print(f"等待页面加载完成出错: {e}")
                # 出错时使用简单等待
                time.sleep(5)

            # 额外等待确保页面完全加载
            time.sleep(3)

            # 等待特定元素出现
            driver.wait.ele_display('css:.bought-wrapper-mod__head-info-cell', timeout=30)

            # 如果有日期范围，设置日期筛选
            if start_date or end_date:
                # 实现日期筛选逻辑
                pass

            # 收集订单数据
            orders = []
            order_items = driver.eles('css:.bought-wrapper-mod__trade-item')

            for item in order_items:
                try:
                    order_id = item.attr('data-id')
                    title_elem = item.ele('css:.bought-wrapper-mod__title-link')
                    title = title_elem.text if title_elem else "未知商品"

                    price_elem = item.ele('css:.price-mod__price')
                    price = price_elem.text if price_elem else "0.00"

                    status_elem = item.ele('css:.bought-wrapper-mod__trade-status')
                    status = status_elem.text if status_elem else "未知状态"

                    orders.append({
                        "order_id": order_id,
                        "title": title,
                        "price": price,
                        "status": status
                    })
                except Exception as e:
                    print(f"读取订单条目时出错: {e}")
                    traceback.print_exc()

            return orders
        except Exception as e:
            print(f"导出订单失败: {e}")
            traceback.print_exc()
            return []

    def _check_login_status(self, driver, profile_id):
        """检查用户是否已登录淘宝"""
        try:
            # 访问淘宝待收货订单页面
            driver.get("https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitConfirm")
            print(f"正在检查用户{profile_id}的登录状态...")

            # 等待页面加载 - 使用更安全的页面加载检查方法
            try:
                # 尝试不同的等待方法，兼容不同版本的驱动
                if hasattr(driver.wait, 'load_complete'):
                    driver.wait.load_complete()
                elif hasattr(driver, 'wait_load_complete'):
                    driver.wait_load_complete()
                elif hasattr(driver, 'wait_for_load_complete'):
                    driver.wait_for_load_complete()
                else:
                    # 如果没有特定的等待方法，使用通用方法
                    time.sleep(3)  # 简单等待3秒
                    print("使用简单等待方法")
            except Exception as e:
                print(f"等待页面加载完成出错: {e}")
                # 出错时使用简单等待
                time.sleep(3)

            # 检查是否重定向到登录页面
            current_url = driver.url
            if "login.taobao.com" in current_url:
                print(f"用户{profile_id}登录已失效")

                # 用户登录已失效，不需要更新状态，由调用方处理
                return False

            # 如果没有重定向到登录页面，则认为已登录
            print(f"用户{profile_id}已登录")
            return True
        except Exception as e:
            print(f"检查登录状态时出错: {e}")
            traceback.print_exc()
            return False

    def _kill_all_chrome_processes(self):
        """结束所有Chrome进程"""
        import subprocess
        import platform
        import time

        print("正在结束所有Chrome进程...")

        # 首先尝试关闭浏览器实例
        if hasattr(self, 'browser_instances'):
            print("关闭所有浏览器实例...")
            for instance in self.browser_instances:
                try:
                    if instance.get("browser"):
                        instance["browser"].quit()
                        print(f"已关闭浏览器实例，端口: {instance.get('port')}")
                except Exception as e:
                    print(f"关闭浏览器实例失败: {e}")
                    traceback.print_exc()

            # 清空浏览器实例列表
            self.browser_instances = []

            # 等待一段时间，确保浏览器进程有时间退出
            wait_time = 2
            print(f"等待 {wait_time} 秒，确保浏览器进程有时间退出...")
            time.sleep(wait_time)

        # 检查是否有Chrome进程
        try:
            # 尝试使用psutil检查Chrome进程
            try:
                import psutil
                chrome_processes = []
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        chrome_processes.append(proc)

                if chrome_processes:
                    print(f"发现 {len(chrome_processes)} 个Chrome进程，准备结束...")
                    for proc in chrome_processes:
                        print(f"Chrome进程: PID={proc.info['pid']}, 名称={proc.info['name']}")
                else:
                    print("未发现Chrome进程，无需结束")
                    return True
            except ImportError:
                print("psutil模块未安装，无法详细检查Chrome进程")
            except Exception as e:
                print(f"检查Chrome进程时出错: {e}")
                traceback.print_exc()
        except Exception as e:
            print(f"检查Chrome进程时出错: {e}")
            traceback.print_exc()

        # 根据操作系统执行不同的命令
        if platform.system() == "Windows":
            # 先尝试正常结束进程
            try:
                print("尝试正常结束Chrome进程...")
                result = subprocess.run(["taskkill", "/IM", "chrome.exe"],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      shell=True,
                                      text=True)
                print(f"taskkill命令执行结果: {result.returncode}")
                if result.stdout:
                    print(f"标准输出: {result.stdout}")
                if result.stderr:
                    print(f"错误输出: {result.stderr}")

                wait_time = 1
                print(f"等待 {wait_time} 秒...")
                time.sleep(wait_time)
            except Exception as e:
                print(f"正常结束Chrome进程失败: {e}")
                traceback.print_exc()

            # 强制结束所有残留进程
            try:
                print("尝试强制结束Chrome进程...")
                result = subprocess.run(["taskkill", "/F", "/IM", "chrome.exe"],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      shell=True,
                                      text=True)
                print(f"taskkill /F命令执行结果: {result.returncode}")
                if result.stdout:
                    print(f"标准输出: {result.stdout}")
                if result.stderr:
                    print(f"错误输出: {result.stderr}")
            except Exception as e:
                print(f"强制结束Chrome进程失败: {e}")
                traceback.print_exc()

            # 检查是否还有Chrome进程
            try:
                print("检查是否还有Chrome进程...")
                result = subprocess.run(["tasklist", "/FI", "IMAGENAME eq chrome.exe"],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      shell=True,
                                      text=True)
                if "chrome.exe" in result.stdout:
                    print("仍有Chrome进程存在，再次尝试强制结束...")
                    print(f"残留的Chrome进程: {result.stdout}")
                    result = subprocess.run(["taskkill", "/F", "/IM", "chrome.exe"],
                                          stdout=subprocess.PIPE,
                                          stderr=subprocess.PIPE,
                                          shell=True,
                                          text=True)
                    print(f"第二次taskkill /F命令执行结果: {result.returncode}")
                    if result.stdout:
                        print(f"标准输出: {result.stdout}")
                    if result.stderr:
                        print(f"错误输出: {result.stderr}")
                else:
                    print("未发现残留的Chrome进程")
            except Exception as e:
                print(f"检查Chrome进程失败: {e}")
                traceback.print_exc()

            print("已结束所有Windows Chrome进程")
        else:
            # Linux/Mac
            try:
                print("尝试结束Linux/Mac Chrome进程...")
                result = subprocess.run(["pkill", "-f", "chrome"],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      text=True)
                print(f"pkill命令执行结果: {result.returncode}")
                if result.stdout:
                    print(f"标准输出: {result.stdout}")
                if result.stderr:
                    print(f"错误输出: {result.stderr}")

                wait_time = 1
                print(f"等待 {wait_time} 秒...")
                time.sleep(wait_time)

                # 强制结束
                print("尝试强制结束Linux/Mac Chrome进程...")
                result = subprocess.run(["pkill", "-9", "-f", "chrome"],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      text=True)
                print(f"pkill -9命令执行结果: {result.returncode}")
                if result.stdout:
                    print(f"标准输出: {result.stdout}")
                if result.stderr:
                    print(f"错误输出: {result.stderr}")

                print("已结束所有Linux/Mac Chrome进程")
            except Exception as e:
                print(f"结束Linux/Mac Chrome进程失败: {e}")
                traceback.print_exc()

        # 最终验证是否所有Chrome进程都已关闭
        try:
            print("最终验证是否所有Chrome进程都已关闭...")
            if platform.system() == "Windows":
                result = subprocess.run(["tasklist", "/FI", "IMAGENAME eq chrome.exe"],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      shell=True,
                                      text=True)
                if "chrome.exe" in result.stdout:
                    print(f"警告: 仍有Chrome进程存在: {result.stdout}")
                else:
                    print("所有Chrome进程已成功关闭")
            else:
                # Linux/Mac
                result = subprocess.run(["pgrep", "-f", "chrome"],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      text=True)
                if result.stdout.strip():
                    print(f"警告: 仍有Chrome进程存在: {result.stdout}")
                else:
                    print("所有Chrome进程已成功关闭")
        except Exception as e:
            print(f"验证Chrome进程关闭状态时出错: {e}")
            traceback.print_exc()

        return True

    def start_order_task(self, profile_ids, order_urls, multi_thread=False):
        """
        启动下单任务

        Args:
            profile_ids: 选择的Chrome用户ID列表
            order_urls: 下单链接列表
            multi_thread: 是否使用多线程模式（同时打开多个浏览器）

        Returns:
            dict: 包含任务启动结果的字典
        """
        try:
            print(f"OrderManager.start_order_task - 收到参数 profile_ids: {profile_ids}, order_urls数量: {len(order_urls)}, 多线程模式: {multi_thread}")

            # 检查是否有任务正在运行
            if self.task_status["running"]:
                return {"success": False, "message": "已有任务正在运行，请先停止当前任务"}

            # 确保profile_ids是列表
            if not isinstance(profile_ids, list):
                profile_ids = [profile_ids]

            # 先结束所有Chrome进程
            print("启动任务前，先结束所有Chrome进程...")
            self._kill_all_chrome_processes()
            time.sleep(3)  # 等待进程完全结束
            print("所有Chrome进程已结束，准备启动新任务")

            # 设置任务状态
            self.task_status["running"] = True
            self.task_status["total_tasks"] = len(order_urls)
            self.task_status["completed_tasks"] = 0
            self.task_status["start_time"] = time.time()  # 记录开始时间
            self.task_status["elapsed_time"] = 0
            self.task_status["multi_thread"] = multi_thread  # 记录是否多线程模式

            # 清除停止事件
            self.stop_event.clear()

            # 计算每个用户需要处理的链接数
            user_count = len(profile_ids)
            if user_count == 0:
                return {"success": False, "message": "没有选择有效的Chrome用户"}

            links_per_user = {}

            # 确保profile_ids中的每个用户都有一个空列表
            for profile_id in profile_ids:
                links_per_user[profile_id] = []

            # 分配下单链接给不同用户
            for i, url in enumerate(order_urls):
                user_index = i % user_count
                profile_id = profile_ids[user_index]
                links_per_user[profile_id].append(url)

            # 移除没有任务的用户
            empty_users = [pid for pid, urls in links_per_user.items() if not urls]
            for pid in empty_users:
                del links_per_user[pid]

            print(f"任务已分配给{len(links_per_user)}个用户，分配结果: {links_per_user.keys()}")
            for pid, urls in links_per_user.items():
                print(f"  用户 {pid}: {len(urls)} 个任务")

            # 为每个用户启动单独的线程，但添加间隔时间
            for i, (profile_id, urls) in enumerate(links_per_user.items()):
                profile = self.chrome_manager.get_profile_by_id(profile_id)
                if not profile:
                    print(f"无法找到ID为{profile_id}的Chrome用户")
                    continue

                print(f"准备启动用户{profile_id}({profile.get('name', '')})的下单任务，链接数: {len(urls)}")

                # 更新用户状态
                self.chrome_manager.update_profile_status(profile_id, "处理中", {
                    "total_links": len(urls),
                    "completed_links": 0,
                    "current_link": "",
                    "start_time": time.strftime("%Y-%m-%d %H:%M:%S")
                })

                # 创建并启动线程
                thread = threading.Thread(
                    target=self._process_order_task,
                    args=(profile_id, urls, multi_thread)
                )
                thread.daemon = True
                thread.start()

                # 保存线程引用
                self.order_threads[profile_id] = thread

                # 添加间隔时间，避免同时启动多个Chrome实例
                if i < len(links_per_user) - 1:  # 最后一个不需要等待
                    interval = random.uniform(3, 5)  # 随机3-5秒间隔，加快启动速度
                    print(f"等待 {interval:.1f} 秒后启动下一个Chrome实例...")
                    time.sleep(interval)

            print("所有下单任务线程已启动")
            return {"success": True}
        except Exception as e:
            import traceback
            print(f"启动下单任务失败: {str(e)}")
            print(traceback.format_exc())
            self.task_status["running"] = False
            return {"success": False, "message": f"启动任务失败: {str(e)}"}

    def _process_order_task(self, profile_id, order_urls, multi_thread=False):
        """
        处理特定用户的下单任务

        Args:
            profile_id: Chrome用户ID
            order_urls: 下单链接列表
            multi_thread: 是否使用多线程模式
        """
        driver = None
        try:
            # 获取Chrome用户信息
            profile = self.chrome_manager.get_profile_by_id(profile_id)
            if not profile:
                return

            # 打开Chrome浏览器 - 多线程模式下不使用无头模式
            headless = not multi_thread

            # 尝试多次启动浏览器，避免因用户数据目录被占用而失败
            max_retries = 3
            for retry in range(max_retries):
                try:
                    print(f"尝试启动浏览器 (尝试 {retry+1}/{max_retries})...")
                    driver = self.chrome_manager.open_browser(profile_id, headless)
                    if driver:
                        print(f"成功启动浏览器，用户: {profile_id}")
                        break
                    else:
                        print(f"启动浏览器失败，等待5秒后重试...")
                        time.sleep(5)
                except Exception as e:
                    print(f"启动浏览器出错: {e}")
                    time.sleep(5)

            if not driver:
                self.chrome_manager.update_profile_status(profile_id, "错误", {
                    "error_message": "无法启动浏览器，多次尝试均失败"
                })
                return

            # 判断是否为DrissionPage对象
            is_drission = hasattr(driver, 'get_tab') and callable(getattr(driver, 'get_tab', None))

            # 设置等待超时
            if is_drission:
                try:
                    # 正确的方法是timeouts而不是timeout
                    driver.set.timeouts(10)
                except Exception as e:
                    print(f"设置DrissionPage超时失败: {e}")
            else:
                driver.implicitly_wait(10)

            # 如果是多线程模式，只在第一次启动时打开淘宝首页并最大化窗口
            # 使用类变量记录是否已经加载过淘宝首页
            if not hasattr(self, '_loaded_taobao_homepage'):
                self._loaded_taobao_homepage = {}

            if multi_thread and profile_id not in self._loaded_taobao_homepage:
                try:
                    print(f"首次启动，打开淘宝首页...")
                    if is_drission:
                        driver.get("https://www.taobao.com")
                    else:
                        driver.get("https://www.taobao.com")
                    time.sleep(3)  # 给予浏览器更多时间来加载
                    print(f"淘宝首页加载完成")

                    # 标记该用户已加载过淘宝首页
                    self._loaded_taobao_homepage[profile_id] = True
                except Exception as e:
                    print(f"打开淘宝页面失败: {str(e)}")
                    traceback.print_exc()

            # 处理每个下单链接
            total_links = len(order_urls)
            completed_links = 0

            # 在同一个浏览器窗口中依次处理所有链接
            for i, url in enumerate(order_urls):
                # 检查是否收到停止信号
                if self.stop_event.is_set():
                    self.chrome_manager.update_profile_status(profile_id, "停止", {
                        "total_links": total_links,
                        "completed_links": completed_links,
                        "current_link": ""
                    })
                    break  # 如果收到停止信号，跳出循环

                # 更新状态数据
                self.chrome_manager.update_profile_status(profile_id, "处理中", {
                    "total_links": total_links,
                    "completed_links": completed_links,
                    "current_link": url
                })

                try:
                    # 处理单个商品下单

                    success = self.place_order(driver, url, self.timeout)

                    # 生成并存储订单数据
                    self._generate_order_data(profile_id, url, success)

                    # 更新计数
                    completed_links += 1
                    with self.task_lock:
                        self.task_status["completed_tasks"] += 1

                    # 更新进度信息
                    self.chrome_manager.update_profile_status(profile_id, "处理中", {
                        "total_links": total_links,
                        "completed_links": completed_links,
                        "current_link": ""
                    })

                    # 随机延迟，避免操作过快
                    if i < total_links - 1:  # 最后一个链接不需要延迟
                        delay = random.uniform(1, 3)
                        print(f"等待 {delay:.1f} 秒后处理下一个链接...")
                        time.sleep(delay)

                except Exception as e:
                    print(f"处理链接 {url} 时出错: {str(e)}")
                    traceback.print_exc()

                    # 生成失败的订单数据
                    self._generate_order_data(profile_id, url, False, str(e))

                    # 更新计数（即使失败也算作已处理）
                    completed_links += 1
                    with self.task_lock:
                        self.task_status["completed_tasks"] += 1

            # 所有链接处理完毕，更新状态
            self.chrome_manager.update_profile_status(profile_id, "完成", {
                "total_links": total_links,
                "completed_links": completed_links,
                "current_link": ""
            })

            print(f"用户 {profile_id} 已完成所有 {total_links} 个链接的处理")

        except Exception as e:
            print(f"处理用户 {profile_id} 的下单任务时出错: {str(e)}")
            traceback.print_exc()

            # 更新用户状态为错误
            self.chrome_manager.update_profile_status(profile_id, "错误", {
                "error_message": str(e)
            })
        finally:
            # 关闭浏览器（如果是多线程模式，且成功完成，则不关闭）
            if driver and (not multi_thread or self.stop_event.is_set()):
                try:
                    # 判断是否为DrissionPage对象
                    is_drission = hasattr(driver, 'get_tab') and callable(getattr(driver, 'get_tab', None))
                    if is_drission:
                        driver.quit()
                    else:
                        driver.quit()
                except:
                    pass

    def _check_login_required(self, driver):
        """检查是否需要登录或验证码"""
        try:
            # 首先检查当前URL是否包含login.taobao.com
            current_url = driver.url
            if "login.taobao.com" in current_url:
                print("检测到需要登录，当前URL包含login.taobao.com")

                # 获取当前用户ID
                try:
                    # 尝试获取用户ID
                    user_data_dir = ""
                    try:
                        user_data_dir = driver.get_tab().user_data_path
                    except:
                        pass

                    if user_data_dir:
                        # 从路径中提取用户ID
                        import os
                        profile_dir = os.path.basename(user_data_dir)
                        # 从Chrome用户列表中查找匹配的用户
                        for profile in self.chrome_manager.get_profiles():
                            if profile_dir in str(profile.get('profile_dir', '')):
                                profile_id = profile.get('id')
                                self.chrome_manager.update_profile_status(profile_id, "登录失效", {
                                    "error_message": "账号失效"
                                })
                                print(f"用户 {profile_id} 状态更新为: 登录失效 (账号失效)")
                                break
                except Exception as e:
                    print(f"更新用户状态失败: {e}")

                return True

            # 检查是否有登录按钮或登录表单
            login_elements = driver.eles('xpath://a[contains(@href, "login") or contains(text(), "登录")] | //input[@id="fm-login-id"]')
            login_required_text = driver.eles('xpath://*[contains(text(), "您需要登录才能继续访问") or contains(text(), "请登录")]')
            captcha_elements = driver.eles('xpath://*[contains(text(), "获取验证码") or contains(text(), "安全验证")]')

            is_login_required = len(login_elements) > 0 or len(login_required_text) > 0
            has_captcha = len(captcha_elements) > 0

            # 如果需要登录或有验证码，更新用户状态
            if is_login_required or has_captcha:
                # 获取当前用户ID
                try:
                    # 尝试获取用户ID
                    user_data_dir = ""
                    try:
                        user_data_dir = driver.get_tab().user_data_path
                    except:
                        pass

                    if user_data_dir:
                        # 从路径中提取用户ID
                        import os
                        profile_dir = os.path.basename(user_data_dir)
                        # 从Chrome用户列表中查找匹配的用户
                        for profile in self.chrome_manager.get_profiles():
                            if profile_dir in str(profile.get('profile_dir', '')):
                                profile_id = profile.get('id')
                                status_message = "需要登录" if is_login_required else "需要验证码"
                                self.chrome_manager.update_profile_status(profile_id, "登录失效", {
                                    "error_message": status_message
                                })
                                print(f"用户 {profile_id} 状态更新为: 登录失效 ({status_message})")
                                break
                except Exception as e:
                    print(f"更新用户状态失败: {e}")

            return is_login_required
        except Exception as e:
            print(f"检查登录状态出错: {e}")
            return False

    def _handle_login(self, driver):
        """处理登录流程"""
        try:
            # 等待登录表单出现
            driver.wait.ele_display('id:fm-login-id', timeout=self.timeout)

            # 此处您应该让用户手动登录，或者实现自动登录逻辑
            # 但请注意，自动填充登录信息可能违反平台规则，建议手动登录

            # 等待用户手动登录完成
            start_time = time.time()
            while time.time() - start_time < 60:  # 最多等待60秒
                if "login" not in driver.url:
                    break
                time.sleep(1)

            print("登录成功")
            return True
        except Exception as e:
            print(f"登录失败: {str(e)}")
            return False

    def _wait_for_product_page(self, driver):
        """等待商品详情页面加载完成"""
        try:
            # 等待商品标题出现
            driver.wait.ele_display('xpath://div[contains(@class, "tb-detail-hd")] | //div[contains(@class, "ItemHeader")]', timeout=self.timeout)
            print("商品详情页加载完成")
            return True
        except Exception as e:
            print(f"等待商品详情页超时: {str(e)}")
            return False

    def _select_product_options(self, driver):
        """选择商品规格"""
        try:
            # 查找所有规格选项
            option_elements = driver.eles('xpath://div[contains(@class, "tb-sku") or contains(@class, "skuUI")]//li[not(contains(@class, "selected"))]')

            # 随机选择每个规格组的一个选项
            for option in option_elements:
                try:
                    if not option.is_displayed():
                        continue

                    # 点击选项
                    driver.run_js("arguments[0].scrollIntoView({block: 'center'});", option)
                    driver.run_js("arguments[0].click();", option)
                    time.sleep(0.5)
                except:
                    continue

            print("已选择商品规格")
            return True
        except Exception as e:
            print(f"选择商品规格出错: {str(e)}")
            return False

    def _click_buy_now(self, driver):
        """点击立即购买按钮"""
        try:
            # 查找立即购买按钮
            buy_now_xpath = "//a[contains(@class, 'J_LinkBuy') or contains(@class, 'btn-buy') or contains(text(), '立即购买')]"
            buy_now_button = driver.ele(f'xpath:{buy_now_xpath}', timeout=self.timeout)

            if not buy_now_button:
                print("未找到立即购买按钮")
                return False

            # 滚动到按钮位置并点击
            driver.run_js("arguments[0].scrollIntoView({block: 'center'});", buy_now_button)
            time.sleep(0.5)
            driver.run_js("arguments[0].click();", buy_now_button)

            print("已点击立即购买按钮")
            return True
        except Exception as e:
            print(f"点击立即购买按钮失败: {str(e)}")
            return False

    def _wait_for_order_confirm_page(self, driver):
        """等待订单确认页面加载完成"""
        try:
            # 等待订单确认元素出现
            driver.wait.ele_display('xpath://div[contains(@class, "order-container") or contains(@class, "submitBlock")]', timeout=self.timeout)
            print("订单确认页面加载完成")
            return True
        except Exception as e:
            print(f"等待订单确认页面超时: {str(e)}")
            return False

    def _submit_order(self, driver):
        """提交订单"""
        try:
            # 查找提交订单按钮
            submit_xpath = "//div[contains(@class, 'submitBlock')]//button[contains(@class, 'submit') or contains(text(), '提交订单')]"
            submit_button = driver.ele(f'xpath:{submit_xpath}', timeout=self.timeout)

            if not submit_button:
                print("未找到提交订单按钮")
                return False

            # 滚动到按钮位置并点击
            driver.run_js("arguments[0].scrollIntoView({block: 'center'});", submit_button)
            time.sleep(0.5)
            driver.run_js("arguments[0].click();", submit_button)

            # 等待2秒，让页面响应
            time.sleep(2)

            # 检查是否下单成功（通常会跳转到支付页面或显示订单成功的提示）
            success = False
            if "cashier" in driver.url or "pay" in driver.url:
                success = True
            elif "成功" in driver.html or "订单号" in driver.html:
                success = True

            if success:
                print("订单提交成功")
            else:
                print("订单可能未成功提交")

            return success
        except Exception as e:
            print(f"提交订单失败: {str(e)}")
            return False

    def _generate_order_data(self, profile_id, url, success=True, error=None):
        """
        生成订单数据

        Args:
            profile_id: Chrome用户ID
            url: 下单链接
            success: 是否下单成功
            error: 错误信息（如果有）

        Returns:
            dict: 订单数据
        """
        # 获取用户信息
        profile = self.chrome_manager.get_profile_by_id(profile_id)
        user_name = profile.get("name", "未知用户")

        # 生成订单ID
        order_id = f"TB{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}{random.randint(1000, 9999)}"

        # 提取URL中的商品ID
        product_id = ""
        try:
            from urllib.parse import urlparse, parse_qs
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            if "id" in query_params:
                product_id = query_params["id"][0]
        except:
            pass

        # 随机生成商品信息（实际应用中应从淘宝页面解析获取）
        products = [
            {"name": "时尚男士休闲衬衫", "option": "白色 L码", "price": 89.90},
            {"name": "女士真皮小方包", "option": "黑色 小号", "price": 129.00},
            {"name": "运动休闲鞋", "option": "灰色 42码", "price": 199.00},
            {"name": "智能手环", "option": "黑色 标准版", "price": 169.00},
            {"name": "保温杯", "option": "银色 500ml", "price": 59.90}
        ]
        product = random.choice(products)

        # 随机生成订单状态
        status = "已完成" if success else "失败"

        # 随机生成收货信息（实际应用中应从淘宝账号中获取）
        receivers = [
            {"name": "张三", "phone": "138****1234", "address": "北京市朝阳区某小区1单元101室"},
            {"name": "李四", "phone": "139****5678", "address": "上海市浦东新区某花园2号楼303室"},
            {"name": "王五", "phone": "137****9876", "address": "广州市天河区某大厦B座505室"}
        ]
        receiver = random.choice(receivers)

        return {
            "id": str(time.time_ns()),  # 系统内部ID
            "order_id": order_id,
            "user_name": user_name,
            "profile_id": profile_id,
            "product_id": product_id,
            "product_name": product["name"],
            "product_option": product["option"],
            "price": product["price"],
            "quantity": 1,
            "total_price": product["price"],
            "order_time": datetime.datetime.now().isoformat(),
            "status": status,
            "receiver_name": receiver["name"],
            "receiver_phone": receiver["phone"],
            "receiver_address": receiver["address"],
            "source_url": url,
            "error_message": error
        }

    def stop_all_tasks(self):
        """
        停止所有任务并结束所有Chrome进程

        Returns:
            dict: 包含任务停止结果的字典
        """
        try:
            # 设置停止事件
            self.stop_event.set()

            # 更新任务状态
            self.task_status["running"] = False

            # 更新所有处理中的用户状态
            profiles = self.chrome_manager.get_profiles()
            for profile in profiles:
                if profile.get("status") == "处理中":
                    self.chrome_manager.update_profile_status(profile["id"], "停止")

            # 结束所有Chrome进程
            self._kill_all_chrome_processes()

            return {"success": True, "message": "已停止所有任务并关闭所有Chrome浏览器"}
        except Exception as e:
            return {"success": False, "message": f"停止任务失败: {str(e)}"}

    def get_task_status(self):
        """
        获取当前任务状态

        Returns:
            dict: 任务状态数据
        """
        # 更新已运行时间
        if self.task_status["running"] and "start_time" in self.task_status:
            self.task_status["elapsed_time"] = time.time() - self.task_status["start_time"]

        # 获取各个用户的状态信息
        profiles_status = {}

        for profile in self.chrome_manager.get_profiles():
            profile_id = profile.get('id')
            if not profile_id:
                continue

            # 获取该用户的状态信息
            if profile.get('status') == "处理中":
                status = "processing"
            elif profile.get('status') == "完成":
                status = "completed"
            elif profile.get('status') == "错误":
                status = "error"
            else:
                status = "pending"

            # 获取进度信息
            progress = 0
            completed_tasks = 0
            current_task = ""

            status_data = profile.get('status_data', {})
            if status_data:
                total_links = status_data.get('total_links', 0)
                completed_links = status_data.get('completed_links', 0)

                if total_links > 0:
                    progress = completed_links / total_links
                    completed_tasks = completed_links

                current_task = status_data.get('current_link', '')

            # 整合状态信息
            profiles_status[profile_id] = {
                'status': status,
                'progress': progress,
                'completedTasks': completed_tasks,
                'currentTask': current_task,
                'lastUpdated': profile.get('last_updated', time.strftime("%Y-%m-%d %H:%M:%S"))
            }

        # 返回完整状态
        full_status = self.task_status.copy()
        full_status['profiles'] = profiles_status


        return full_status

    def start_receipt_task(self, profile_ids, order_ids):
        """
        启动确认收货任务

        Args:
            profile_ids: 选择的Chrome用户ID列表
            order_ids: 订单ID列表

        Returns:
            dict: 包含任务启动结果的字典
        """
        try:
            # 检查是否有任务正在运行
            if self.task_status["running"]:
                return {"success": False, "message": "已有任务正在运行，请先停止当前任务"}

            # 设置任务状态
            self.task_status["running"] = True
            self.task_status["total_tasks"] = len(profile_ids) * len(order_ids)
            self.task_status["completed_tasks"] = 0

            # 清除停止事件
            self.stop_event.clear()

            # 为第一个用户分配所有确认收货任务（实际应用中可以更均匀分配）
            if profile_ids:
                profile_id = profile_ids[0]
                profile = self.chrome_manager.get_profile_by_id(profile_id)
                if profile:
                    # 更新用户状态
                    self.chrome_manager.update_profile_status(profile_id, "处理中", {
                        "total_count": len(order_ids),
                        "completed_count": 0,
                        "current_order_id": ""
                    })

                    # 创建并启动线程
                    thread = threading.Thread(
                        target=self._process_receipt_task,
                        args=(profile_id, order_ids)
                    )
                    thread.daemon = True
                    thread.start()

                    # 保存线程引用
                    self.order_threads[profile_id] = thread

            return {"success": True}
        except Exception as e:
            self.task_status["running"] = False
            return {"success": False, "message": f"启动确认收货任务失败: {str(e)}"}

    def _process_receipt_task(self, profile_id, order_ids):
        """
        处理确认收货任务

        Args:
            profile_id: Chrome用户ID
            order_ids: 订单ID列表
        """
        try:
            # 模拟确认收货流程
            completed_count = 0
            for order_id in order_ids:
                # 检查是否收到停止信号
                if self.stop_event.is_set():
                    self.chrome_manager.update_profile_status(profile_id, "停止")
                    break

                # 更新当前处理的订单ID
                self.chrome_manager.update_profile_status(profile_id, "处理中", {
                    "total_count": len(order_ids),
                    "completed_count": completed_count,
                    "current_order_id": order_id
                })

                # 模拟确认收货过程
                time.sleep(random.uniform(0.5, 2))  # 随机延迟，模拟真实操作

                # 更新订单状态（实际应用中应更新数据库）
                with self.order_lock:
                    for i, order in enumerate(self.orders):
                        if order.get("order_id") == order_id:
                            self.orders[i]["status"] = "已完成"
                            break

                # 更新任务完成数和进度
                completed_count += 1
                self.task_status["completed_tasks"] += 1

                # 更新用户状态和进度
                self.chrome_manager.update_profile_status(profile_id, "处理中", {
                    "total_count": len(order_ids),
                    "completed_count": completed_count,
                    "current_order_id": ""
                })

            # 更新用户状态为成功
            if not self.stop_event.is_set():
                self.chrome_manager.update_profile_status(profile_id, "成功", {
                    "total_count": len(order_ids),
                    "completed_count": completed_count,
                    "current_order_id": ""
                })

            # 检查是否所有任务都已完成
            if self.task_status["completed_tasks"] >= self.task_status["total_tasks"]:
                self.task_status["running"] = False
        except Exception as e:
            # 更新用户状态为错误
            self.chrome_manager.update_profile_status(profile_id, "错误")
            print(f"确认收货任务出错: {str(e)}")

    def start_auto_receipt_task(self, profile_ids, max_threads_count=2):
        """
        启动自动收货任务
        :param profile_ids: 用户配置文件ID列表
        :param max_threads_count: 最大线程数量
        """
        try:
            print(f"启动自动收货任务，选择的用户: {profile_ids}，最大线程数: {max_threads_count}")

            # 参数验证
            if not profile_ids:
                print("错误: 未提供用户ID列表")
                return False

            # 确保profile_ids是列表
            if not isinstance(profile_ids, list):
                profile_ids = [profile_ids]
                print(f"将单个用户ID转换为列表: {profile_ids}")

            # 确保max_threads_count是整数
            try:
                max_threads_count = int(max_threads_count)
                if max_threads_count < 1:
                    print(f"警告: 最大线程数 {max_threads_count} 小于1，设置为1")
                    max_threads_count = 1
            except (ValueError, TypeError):
                print(f"警告: 最大线程数 {max_threads_count} 不是有效整数，设置为默认值1")
                max_threads_count = 1

            # 如果已有任务正在运行，先取消
            if hasattr(self, 'receipt_task_running') and self.receipt_task_running:
                print("已有自动收货任务正在运行，先取消")
                self.cancel_auto_receipt_task()
                # 等待任务完全停止
                time.sleep(1)

            # 先结束所有Chrome进程
            print("启动任务前，先结束所有Chrome进程...")
            self._kill_all_chrome_processes()
            time.sleep(3)  # 等待进程完全结束
            print("所有Chrome进程已结束，准备启动新任务")

            # 初始化任务状态和计数器
            self.receipt_task_running = True

            # 使用Manager创建共享变量
            manager = multiprocessing.Manager()
            task_counter = manager.Value('i', 0)  # 已完成任务计数
            max_threads = manager.Value('i', max_threads_count)  # 最大线程数
            active_threads = manager.Value('i', 0)  # 当前活跃线程数
            task_lock = manager.Lock()  # 线程锁

            # 初始化任务队列
            task_queue = manager.list(profile_ids)

            print(f"任务队列初始化完成，共 {len(task_queue)} 个任务")

            # 清除停止事件
            self.stop_event.clear()

            # 启动第一个线程处理第一个用户
            if len(task_queue) > 0:
                first_profile_id = task_queue.pop(0)
                print(f"启动第一个线程处理用户ID {first_profile_id}")

                # 创建并启动线程
                thread = threading.Thread(
                    target=self._process_auto_receipt_task_with_counter,
                    args=(first_profile_id, task_lock, task_counter, max_threads, active_threads, task_queue)
                )
                thread.daemon = True
                thread.start()

                print(f"已启动第一个线程处理用户ID {first_profile_id}")

                # 启动监控线程
                monitor_thread = threading.Thread(
                    target=self._monitor_auto_receipt_task,
                    args=(task_lock, task_counter, active_threads, task_queue, max_threads)
                )
                monitor_thread.daemon = True
                monitor_thread.start()

                return True
            else:
                print("任务队列为空，没有用户需要处理")
                self.receipt_task_running = False
                return False

        except Exception as e:
            print(f"启动自动收货任务失败: {e}")
            traceback.print_exc()
            self.receipt_task_running = False
            return False

    def _process_auto_receipt_task_with_counter(self, profile_id, lock, counter, max_threads, active_threads, task_queue, browser_ready_event=None, browser_success_event=None):
        """处理自动收货任务，并维护线程计数"""
        browser = None
        try:
            # 增加活动线程计数
            with lock:
                active_threads.value += 1
                print(f"线程启动：用户ID {profile_id}，当前活跃线程数：{active_threads.value}")

            # 获取Chrome用户
            try:
                profile = self.chrome_manager.get_profile_by_id(profile_id)
                if not profile:
                    print(f"找不到ID为 {profile_id} 的Chrome用户")
                    with lock:
                        active_threads.value -= 1
                        print(f"线程结束：找不到用户，当前活跃线程数：{active_threads.value}")
                    # 如果有浏览器启动事件，设置为已完成（即使失败）
                    if browser_ready_event:
                        browser_ready_event.set()
                    return
                print(f"成功获取用户配置: {profile.get('name', '')}")
            except Exception as e:
                print(f"获取用户配置时出错: {e}")
                traceback.print_exc()
                with lock:
                    active_threads.value -= 1
                    print(f"线程结束：获取用户配置失败，当前活跃线程数：{active_threads.value}")
                if browser_ready_event:
                    browser_ready_event.set()
                return

            # 更新用户状态
            try:
                self.chrome_manager.update_profile_status(profile_id, "任务准备中")
                print(f"已更新用户 {profile_id} 状态为'任务准备中'")
            except Exception as e:
                print(f"更新用户状态时出错: {e}")
                traceback.print_exc()
                # 继续执行，不因状态更新失败而中断任务

            # 检查并清理可能存在的Chrome进程
            try:
                print(f"检查是否存在Chrome进程...")
                self._check_and_clean_chrome_processes()
                print(f"Chrome进程检查完成")
            except Exception as e:
                print(f"检查Chrome进程时出错: {e}")
                traceback.print_exc()
                # 继续执行，不因进程检查失败而中断任务

            # 打开浏览器 - 直接使用profile对象而不是再次查找
            print(f"准备为用户 {profile.get('name', '')} 打开浏览器...")
            try:
                # 使用与获取cookie功能相同的方法启动浏览器
                print(f"使用open_browser_with_profile方法启动Chrome浏览器...")
                browser = self.chrome_manager.open_browser_with_profile(profile)
                print(f"用户 {profile.get('name', '')} 的浏览器创建结果: {browser is not None}")
            except Exception as e:
                print(f"创建浏览器对象时出错: {e}")
                traceback.print_exc()
                browser = None

            if not browser:
                print(f"为用户 {profile.get('name', '')} 打开浏览器失败")
                try:
                    self.chrome_manager.update_profile_status(profile_id, "浏览器启动失败")
                except Exception as e:
                    print(f"更新用户状态为'浏览器启动失败'时出错: {e}")
                    traceback.print_exc()

                with lock:
                    active_threads.value -= 1
                    print(f"线程结束：浏览器打开失败，当前活跃线程数：{active_threads.value}")
                # 如果有浏览器启动事件，设置为已完成（即使失败）
                if browser_ready_event:
                    browser_ready_event.set()
                return

            # 浏览器已成功启动，通知等待的线程
            if browser_ready_event:
                browser_ready_event.set()
                print(f"用户 {profile_id} 的浏览器已成功启动，已通知等待的线程")

            # 设置浏览器成功启动事件
            if browser_success_event:
                browser_success_event.set()
                print(f"用户 {profile_id} 的浏览器成功启动事件已设置")

            # 更新用户状态
            try:
                self.chrome_manager.update_profile_status(profile_id, "任务执行中")
                print(f"已更新用户 {profile_id} 状态为'任务执行中'")
            except Exception as e:
                print(f"更新用户状态时出错: {e}")
                traceback.print_exc()

            # 执行自动收货
            print(f"用户 {profile.get('name', '')} 开始执行自动收货任务...")

            # 获取支付密码
            pay_password = profile.get('pay_password', '')
            print(f"用户支付密码: {'已设置' if pay_password else '未设置'}")

            # 打开待收货订单页面
            print(f"用户 {profile_id} 打开待收货订单页面...")
            try:
                browser.get("https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitConfirm")
                print(f"用户 {profile_id} 已打开待收货订单页面")
            except Exception as e:
                print(f"打开待收货订单页面时出错: {e}")
                traceback.print_exc()
                raise

            # 等待页面加载
            print(f"用户 {profile_id} 等待页面加载...")
            try:
                # 尝试使用不同的等待方法，兼容不同版本的DrissionPage
                if hasattr(browser.wait, 'load_complete'):
                    print(f"用户 {profile_id} 使用 browser.wait.load_complete() 方法")
                    browser.wait.load_complete()
                elif hasattr(browser, 'wait_load_complete'):
                    print(f"用户 {profile_id} 使用 browser.wait_load_complete() 方法")
                    browser.wait_load_complete()
                elif hasattr(browser, 'wait_for_load_complete'):
                    print(f"用户 {profile_id} 使用 browser.wait_for_load_complete() 方法")
                    browser.wait_for_load_complete()
                else:
                    # 如果没有特定的等待方法，使用通用方法
                    print(f"用户 {profile_id} 使用简单等待方法")
                    time.sleep(5)  # 简单等待5秒
            except Exception as e:
                print(f"用户 {profile_id} 等待页面加载完成出错: {e}")
                traceback.print_exc()
                # 出错时使用简单等待
                time.sleep(5)

            # 额外等待确保页面完全加载
            print(f"用户 {profile_id} 额外等待3秒确保页面完全加载")
            time.sleep(3)

            # 检查登录状态
            try:
                current_url = browser.url
                print(f"用户 {profile_id} 当前URL: {current_url}")
                if "login.taobao.com" in current_url:
                    print(f"用户 {profile.get('name', '')} 登录已失效")
                    self.chrome_manager.update_profile_status(profile_id, "登录失效")
                    return
            except Exception as e:
                print(f"用户 {profile_id} 检查登录状态时出错: {e}")
                traceback.print_exc()
                self.chrome_manager.update_profile_status(profile_id, f"检查登录状态出错: {str(e)}")
                return

            # 获取待收货订单列表
            print(f"用户 {profile_id} 获取待收货订单列表...")
            try:
                order_items = browser.eles('css:.bought-wrapper-mod__trade-item')
                print(f"用户 {profile_id} 找到 {len(order_items) if order_items else 0} 个订单项")
            except Exception as e:
                print(f"用户 {profile_id} 获取订单列表时出错: {e}")
                traceback.print_exc()
                self.chrome_manager.update_profile_status(profile_id, f"获取订单列表出错: {str(e)}")
                return

            if not order_items:
                print(f"用户 {profile.get('name', '')} 没有待收货订单")
                self.chrome_manager.update_profile_status(profile_id, "没有待确认订单")
                return

            # 获取配置的日期范围
            start_date = None
            end_date = None

            # 使用集中配置管理器获取配置
            config = config_manager.get_config()
            start_date_str = config.get('start_date', '')
            end_date_str = config.get('end_date', '')

            if start_date_str:
                start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d').date()
                print(f"使用配置的开始日期: {start_date}")
            if end_date_str:
                end_date = datetime.datetime.strptime(end_date_str, '%Y-%m-%d').date()
                print(f"使用配置的结束日期: {end_date}")

            # 处理每个订单
            completed_count = 0
            total_count = len(order_items)

            print(f"找到 {total_count} 个待收货订单")

            # 更新用户状态
            self.chrome_manager.update_profile_status(profile_id, "处理中", {
                "total_count": total_count,
                "completed_count": completed_count,
                "current_order_id": ""
            })

            for i, item in enumerate(order_items):
                try:
                    # 检查是否收到停止信号
                    if self.stop_event.is_set():
                        print(f"收到停止信号，中断确认收货任务")
                        break

                    # 获取订单ID
                    order_id = item.attr('data-id')
                    if not order_id:
                        print("无法获取订单ID，跳过")
                        continue

                    # 获取订单时间
                    time_elem = item.ele('css:.bought-wrapper-mod__create-time')
                    if time_elem:
                        order_time_str = time_elem.text.strip()
                    else:
                        print("未找到订单时间元素，跳过")
                        continue

                    print(f"处理订单: {order_id}, 时间: {order_time_str}")

                    # 解析订单时间
                    if "年" in order_time_str and "月" in order_time_str and "日" in order_time_str:
                        # 格式如：2023年05月20日 12:34:56
                        order_time = datetime.datetime.strptime(order_time_str, '%Y年%m月%d日 %H:%M:%S')
                    elif "-" in order_time_str:
                        # 格式如：2023-05-20 12:34:56
                        order_time = datetime.datetime.strptime(order_time_str, '%Y-%m-%d %H:%M:%S')
                    else:
                        # 其他格式，跳过
                        print(f"无法解析订单时间格式: {order_time_str}")
                        continue

                    order_date = order_time.date()
                    print(f"订单日期: {order_date}")

                    # 检查是否在日期范围内
                    if start_date and order_date < start_date:
                        print(f"订单{order_id}的日期{order_date}早于开始日期{start_date}，跳过")
                        continue

                    if end_date and order_date > end_date:
                        print(f"订单{order_id}的日期{order_date}晚于结束日期{end_date}，跳过")
                        continue

                    # 更新当前处理的订单ID
                    self.chrome_manager.update_profile_status(profile_id, "处理中", {
                        "total_count": total_count,
                        "completed_count": completed_count,
                        "current_order_id": order_id
                    })

                    # 执行确认收货操作
                    print(f"开始确认收货订单: {order_id}")
                    success = self.confirm_receipt(browser, order_id, pay_password)

                    if success:
                        print(f"订单 {order_id} 确认收货成功")
                        completed_count += 1
                    else:
                        print(f"订单 {order_id} 确认收货失败")

                    # 更新用户状态
                    self.chrome_manager.update_profile_status(profile_id, "处理中", {
                        "total_count": total_count,
                        "completed_count": completed_count,
                        "current_order_id": ""
                    })

                    # 随机延迟，避免操作过快
                    if i < total_count - 1:  # 最后一个订单不需要延迟
                        delay = random.uniform(1, 3)
                        print(f"等待 {delay:.1f} 秒后处理下一个订单...")
                        time.sleep(delay)

                except Exception as e:
                    print(f"处理订单时出错: {e}")
                    traceback.print_exc()

            print(f"用户 {profile.get('name', '')} 确认收货任务完成，共处理 {completed_count}/{total_count} 个订单")

            # 更新任务计数
            with lock:
                counter.value += 1
                print(f"已完成 {counter.value} 个任务")

            # 更新用户状态
            self.chrome_manager.update_profile_status(profile_id, "任务已完成")
            print(f"用户 {profile.get('name', '')} 自动收货任务完成")

        except Exception as e:
            print(f"用户 {profile_id} 自动收货任务执行失败: {e}")
            self.chrome_manager.update_profile_status(profile_id, f"任务失败: {str(e)}")
            traceback.print_exc()

        finally:
            # 关闭浏览器
            try:
                if browser:
                    print(f"关闭用户 {profile_id} 的浏览器")
                    browser.quit()
            except Exception as e:
                print(f"关闭浏览器失败: {e}")

            # 减少活动线程计数
            with lock:
                active_threads.value -= 1
                print(f"线程结束：用户 {profile_id}，当前活跃线程数：{active_threads.value}")

            # 如果还有待处理的任务，启动新线程
            try:
                with lock:
                    if len(task_queue) > 0 and active_threads.value < max_threads.value:
                        next_profile_id = task_queue.pop(0)
                        print(f"启动新线程处理用户ID {next_profile_id}，剩余任务数: {len(task_queue)}")
                        thread = threading.Thread(
                            target=self._process_auto_receipt_task_with_counter,
                            args=(next_profile_id, lock, counter, max_threads, active_threads, task_queue)
                        )
                        thread.daemon = True
                        thread.start()
            except Exception as e:
                print(f"启动新线程失败: {e}")
                traceback.print_exc()

    def _monitor_auto_receipt_task(self, lock, counter, active_threads, task_queue, max_threads=None):
        """监控自动收货任务状态"""
        try:
            # 设置最大无活动时间（秒）
            max_idle_time = 60
            last_activity_time = time.time()

            # 如果没有传入max_threads，使用默认值
            if max_threads is None:
                max_threads_value = 2
            else:
                max_threads_value = max_threads.value

            while self.receipt_task_running:
                time.sleep(1)  # 每秒检查一次
                current_time = time.time()

                with lock:
                    # 检查任务是否完成
                    if len(task_queue) == 0 and active_threads.value == 0:
                        print("所有用户的自动收货任务已完成")
                        self.receipt_task_running = False
                        break

                    # 检查是否有活动
                    if active_threads.value > 0:
                        last_activity_time = current_time
                    elif current_time - last_activity_time > max_idle_time:
                        print(f"任务已超过{max_idle_time}秒无活动，可能存在卡死情况，强制结束任务")
                        self.receipt_task_running = False
                        break

                    # 输出当前状态
                    print(f"任务状态: 已完成 {counter.value} 个任务, 活跃线程 {active_threads.value}, 队列中 {len(task_queue)} 个任务")

                    # 检查是否有线程卡死（活跃线程数>0但队列中还有任务且长时间没有处理）
                    if active_threads.value == 0 and len(task_queue) > 0:
                        print("检测到队列中还有任务但没有活跃线程，尝试启动新线程处理剩余任务")
                        if len(task_queue) > 0:
                            next_profile_id = task_queue.pop(0)
                            print(f"启动新线程处理用户ID {next_profile_id}，剩余任务数: {len(task_queue)}")
                            # 创建一个固定值的max_threads对象，避免参数类型不匹配
                            fixed_max_threads = multiprocessing.Manager().Value('i', max_threads_value)
                            thread = threading.Thread(
                                target=self._process_auto_receipt_task_with_counter,
                                args=(next_profile_id, lock, counter, fixed_max_threads, active_threads, task_queue, None, None)
                            )
                            thread.daemon = True
                            thread.start()
        except Exception as e:
            print(f"监控线程异常: {e}")
            traceback.print_exc()

    def get_all_orders(self):
        """
        获取所有订单数据

        Returns:
            list: 订单数据列表
        """
        # 模拟数据库查询，返回所有订单数据
        with self.order_lock:
            # 如果没有订单数据，生成一些模拟数据
            if not self.orders:
                for _ in range(20):
                    profile_id = random.choice(self.chrome_manager.get_profiles())["id"]
                    url = f"https://item.taobao.com/item.htm?id={random.randint(10000000, 99999999)}"
                    self.orders.append(self._generate_order_data(profile_id, url))
            return self.orders

    def get_order_detail(self, order_id):
        """
        获取订单详情

        Args:
            order_id: 订单ID

        Returns:
            dict: 订单详情数据，如果未找到则返回None
        """
        with self.order_lock:
            for order in self.orders:
                if order.get("id") == order_id:
                    return order
        return None

    def delete_order(self, order_id):
        """
        删除订单

        Args:
            order_id: 订单ID

        Returns:
            bool: 删除结果
        """
        try:
            with self.order_lock:
                self.orders = [order for order in self.orders if order.get("id") != order_id]
            return True
        except Exception as e:
            print(f"删除订单失败: {str(e)}")
            return False

    def clear_orders(self):
        """
        清空所有订单数据

        Returns:
            bool: 清空结果
        """
        try:
            with self.order_lock:
                self.orders = []
            return True
        except Exception as e:
            print(f"清空订单失败: {str(e)}")
            return False

    def export_orders(self, export_all=True, include_details=True, filters=None):
        """
        导出订单数据

        Args:
            export_all: 是否导出所有订单，False则应用筛选条件
            include_details: 是否包含详细信息
            filters: 筛选条件

        Returns:
            str: 导出文件路径，失败则返回None
        """
        try:
            # 获取所有订单
            all_orders = self.get_all_orders()

            # 应用筛选
            if not export_all and filters:
                # 状态筛选
                status_filter = filters.get('status')
                if status_filter and status_filter != 'all':
                    if status_filter == 'completed':
                        all_orders = [order for order in all_orders if order.get('status') == '已完成']
                    elif status_filter == 'pending':
                        all_orders = [order for order in all_orders if order.get('status') == '进行中']
                    elif status_filter == 'failed':
                        all_orders = [order for order in all_orders if order.get('status') == '失败']

                # 时间筛选
                time_filter = filters.get('time_filter')
                if time_filter and time_filter != 'all':
                    today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

                    if time_filter == 'today':
                        all_orders = [order for order in all_orders if datetime.datetime.fromisoformat(order.get('order_time')) >= today]
                    elif time_filter == 'yesterday':
                        yesterday = today - datetime.timedelta(days=1)
                        all_orders = [order for order in all_orders if yesterday <= datetime.datetime.fromisoformat(order.get('order_time')) < today]
                    elif time_filter == 'week':
                        week_ago = today - datetime.timedelta(days=7)
                        all_orders = [order for order in all_orders if datetime.datetime.fromisoformat(order.get('order_time')) >= week_ago]
                    elif time_filter == 'month':
                        month_ago = today - datetime.timedelta(days=30)
                        all_orders = [order for order in all_orders if datetime.datetime.fromisoformat(order.get('order_time')) >= month_ago]

                # 搜索筛选
                search_term = filters.get('search_term', '').lower()
                if search_term:
                    all_orders = [order for order in all_orders if
                                 search_term in order.get('order_id', '').lower() or
                                 search_term in order.get('product_name', '').lower() or
                                 search_term in order.get('user_name', '').lower()]

            # 准备导出数据
            export_data = []
            for order in all_orders:
                order_data = {
                    '订单号': order.get('order_id'),
                    '商品名称': order.get('product_name'),
                    'Chrome用户': order.get('user_name'),
                    '价格': order.get('price'),
                    '下单时间': order.get('order_time'),
                    '状态': order.get('status')
                }

                # 添加详细信息
                if include_details:
                    order_data.update({
                        '商品规格': order.get('product_option'),
                        '数量': order.get('quantity'),
                        '总价': order.get('total_price'),
                        '收货人': order.get('receiver_name'),
                        '联系电话': order.get('receiver_phone'),
                        '收货地址': order.get('receiver_address'),
                        '来源链接': order.get('source_url')
                    })

                export_data.append(order_data)

            # 创建DataFrame
            df = pd.DataFrame(export_data)

            # 确保下载目录存在
            download_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'downloads')
            os.makedirs(download_dir, exist_ok=True)

            # 保存为Excel文件
            timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            file_path = os.path.join(download_dir, f'orders_export_{timestamp}.xlsx')

            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='订单数据')

            return file_path
        except Exception as e:
            print(f"导出订单失败: {str(e)}")
            return None

    def get_waiting_receipt_orders(self):
        """
        获取等待确认收货的订单

        Returns:
            list: 待确认收货的订单列表
        """
        try:
            # 这里应当连接到淘宝API或使用爬虫获取待确认收货的订单
            # 这里使用模拟数据进行演示
            waiting_orders = []

            # 生成一些模拟数据
            for i in range(10):
                order_id = f"TB{datetime.datetime.now().strftime('%Y%m%d')}{random.randint(10000000, 99999999)}"

                # 随机商品名称
                products = ["男士衬衫", "女士连衣裙", "儿童玩具", "厨房用品", "电子产品", "床上用品", "运动鞋", "化妆品"]
                product_name = random.choice(products)

                # 随机价格
                price = round(random.uniform(50, 500), 2)

                # 随机下单时间（1-30天前）
                days_ago = random.randint(1, 30)
                order_time = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).isoformat()

                waiting_orders.append({
                    "id": order_id,  # 这里使用订单号作为ID
                    "order_id": order_id,
                    "title": f"{product_name} - {random.choice(['红色', '蓝色', '黑色', '白色'])}",
                    "price": f"¥{price}",
                    "order_time": order_time,
                    "status": "待收货"
                })

            return waiting_orders
        except Exception as e:
            print(f"获取待确认收货订单失败: {e}")
            traceback.print_exc()
            return []

    def get_receipt_status(self):
        """
        获取确认收货任务状态

        Returns:
            dict: 包含任务状态的字典
        """
        # 更新任务运行时间
        if self.task_status.get("running") and "start_time" in self.task_status:
            self.task_status["elapsed_time"] = time.time() - self.task_status["start_time"]

        # 获取活跃的任务信息
        active_tasks = []
        for profile in self.chrome_manager.get_profiles():
            if profile.get('status') in ['处理中', '任务准备中', '任务执行中']:
                profile_id = profile.get('id')
                name = profile.get('name', '未知用户')

                status_data = profile.get('status_data', {})
                total_count = status_data.get('total_count', 0)
                completed_count = status_data.get('completed_count', 0)
                skipped_count = status_data.get('skipped_count', 0)
                current_order = status_data.get('current_order_id', '')

                # 计算进度
                progress = 0
                if total_count > 0:
                    progress = completed_count / total_count

                # 计算预计剩余时间
                estimated_time = None
                if self.task_status.get("elapsed_time") and completed_count > 0 and completed_count < total_count:
                    task_rate = completed_count / self.task_status["elapsed_time"]  # 每秒完成的任务数
                    if task_rate > 0:
                        estimated_time = (total_count - completed_count) / task_rate

                active_tasks.append({
                    "user": name,
                    "profile_id": profile_id,
                    "status": profile.get('status', '未知'),
                    "progress": progress,
                    "total": total_count,
                    "completed": completed_count,
                    "skipped": skipped_count,
                    "current_order": current_order,
                    "last_update": profile.get('last_updated', time.strftime("%Y-%m-%d %H:%M:%S")),
                    "estimated_time": estimated_time
                })

        # 获取浏览器实例状态
        browser_status = []
        if hasattr(self, 'browser_instances'):
            for i, instance in enumerate(self.browser_instances):
                status = "使用中" if instance.get("in_use") else "空闲"
                current_profile = instance.get("current_profile")
                profile_info = ""
                if current_profile:
                    # 查找用户名
                    for profile in self.chrome_manager.get_profiles():
                        if profile.get('id') == current_profile:
                            profile_info = f" ({profile.get('name', '')})"
                            break

                browser_status.append({
                    "id": i + 1,
                    "status": status,
                    "port": instance.get("port"),
                    "profile_id": current_profile,
                    "profile_name": profile_info
                })

        # 计算总任务数和已完成任务数
        total_tasks = 0
        completed_tasks = 0
        skipped_tasks = 0

        # 尝试从Flask应用的全局变量中获取任务计数
        flask_task_counts_available = False
        try:
            import flask
            # 检查是否在Flask应用上下文中
            if flask.has_app_context():
                app = flask.current_app
                if hasattr(app, 'task_counts'):
                    # 从Flask应用的全局变量中获取任务计数
                    for profile_id, task_data in app.task_counts.items():
                        total_tasks += task_data.get('total_count', 0)
                        completed_tasks += task_data.get('completed_count', 0)
                        skipped_tasks += task_data.get('skipped_count', 0)
                    flask_task_counts_available = True
        except Exception as e:
            # 静默处理Flask应用上下文错误，避免重复日志
            pass

        # 如果无法从Flask应用获取任务计数，则从用户状态数据中聚合
        if not flask_task_counts_available:
            for profile in self.chrome_manager.get_profiles():
                status_data = profile.get('status_data', {})
                if status_data:
                    total_tasks += status_data.get('total_count', 0)
                    completed_tasks += status_data.get('completed_count', 0)
                    skipped_tasks += status_data.get('skipped_count', 0)

        return {
            "is_running": hasattr(self, 'receipt_task_running') and self.receipt_task_running,
            "total": total_tasks,
            "completed": completed_tasks,
            "pending": total_tasks - completed_tasks,
            "skipped": skipped_tasks,
            "elapsed_time": self.task_status.get("elapsed_time", 0),
            "active_tasks": active_tasks,
            "browser_instances": browser_status
        }

    def stop_receipt_task(self):
        """
        停止确认收货任务

        Returns:
            dict: 包含任务停止结果的字典
        """
        try:
            # 设置停止事件
            self.stop_event.set()

            # 更新任务状态
            self.task_status["running"] = False

            # 设置任务运行状态为False
            self.receipt_task_running = False

            # 更新所有处理中的用户状态
            profiles = self.chrome_manager.get_profiles()
            for profile in profiles:
                if profile.get("status") == "处理中":
                    self.chrome_manager.update_profile_status(profile["id"], "停止")

            # 关闭所有浏览器实例
            if hasattr(self, 'browser_instances'):
                print("关闭所有浏览器实例...")
                for instance in self.browser_instances:
                    try:
                        if instance.get("browser"):
                            instance["browser"].quit()
                            print(f"已关闭浏览器实例，端口: {instance.get('port')}")
                    except Exception as e:
                        print(f"关闭浏览器实例失败: {e}")

                # 清空浏览器实例列表
                self.browser_instances = []
            else:
                # 如果没有browser_instances属性，使用原来的方法结束Chrome进程
                self._kill_all_chrome_processes()

            return {"success": True, "message": "已停止所有任务并关闭所有Chrome浏览器"}
        except Exception as e:
            print(f"停止任务失败: {e}")
            traceback.print_exc()
            return {"success": False, "message": f"停止任务失败: {str(e)}"}

    def start_manual_receipt_task(self, profile_ids, order_ids):
        """
        启动手动确认收货任务

        Args:
            profile_ids: 选择的Chrome用户ID列表
            order_ids: 订单ID列表

        Returns:
            dict: 包含任务启动结果的字典
        """
        print(f"开始手动确认收货任务: {profile_ids}, {order_ids}")
        # 调用现有的start_receipt_task方法
        return self.start_receipt_task(profile_ids, order_ids)

    def cancel_auto_receipt_task(self):
        """取消自动收货任务"""
        self.receipt_task_running = False
        print("已取消自动收货任务")

        # 关闭所有浏览器实例
        if hasattr(self, 'browser_instances'):
            for instance in self.browser_instances:
                try:
                    if instance.get("browser"):
                        instance["browser"].quit()
                        print(f"已关闭浏览器实例，端口: {instance.get('port')}")
                except Exception as e:
                    print(f"关闭浏览器实例失败: {e}")

            # 清空浏览器实例列表
            self.browser_instances = []

    def _process_tasks_with_browser_pool(self, task_queue, task_lock, task_counter):
        """使用浏览器池处理任务"""
        try:
            print("开始使用浏览器池处理任务")

            while self.receipt_task_running and len(task_queue) > 0:
                # 查找可用的浏览器实例
                available_browser = None
                browser_index = -1

                for i, instance in enumerate(self.browser_instances):
                    if not instance.get("in_use"):
                        available_browser = instance
                        browser_index = i
                        break

                if available_browser is None:
                    # 没有可用的浏览器实例，等待一段时间后重试
                    print("当前没有可用的浏览器实例，等待5秒后重试...")
                    time.sleep(5)
                    continue

                # 标记浏览器实例为使用中
                with task_lock:
                    if len(task_queue) == 0:
                        break

                    profile_id = task_queue.pop(0)
                    self.browser_instances[browser_index]["in_use"] = True
                    self.browser_instances[browser_index]["current_profile"] = profile_id

                # 启动线程处理任务
                thread = threading.Thread(
                    target=self._process_single_task_with_browser,
                    args=(profile_id, available_browser, task_lock, task_counter)
                )
                thread.daemon = True
                thread.start()

            print("所有任务已分配给浏览器实例处理")

        except Exception as e:
            print(f"浏览器池处理任务出错: {e}")
            traceback.print_exc()

    def _process_single_task_with_browser(self, profile_id, browser_instance, task_lock, task_counter):
        """使用指定的浏览器实例处理单个任务"""
        try:
            browser = browser_instance.get("browser")
            print(f"使用浏览器实例(端口:{browser_instance.get('port')})处理用户 {profile_id} 的任务")

            # 获取用户配置
            profile = self.chrome_manager.get_profile_by_id(profile_id)
            if not profile:
                print(f"找不到ID为 {profile_id} 的Chrome用户")
                with task_lock:
                    browser_instance["in_use"] = False
                    browser_instance["current_profile"] = None
                return

            # 更新用户状态
            self.chrome_manager.update_profile_status(profile_id, "任务准备中")

            # 获取用户cookie
            cookie = profile.get('cookie', '')

            # 获取支付密码
            pay_password = profile.get('pay_password', '')

            # 清除浏览器cookie
            print(f"清除浏览器cookie...")
            try:
                # 使用新版API清除cookie和缓存
                if hasattr(browser, 'set') and hasattr(browser.set, 'cookies'):
                    # 清除所有cookie
                    browser.set.cookies.clear()
                    print("已清除所有cookie")

                    # 清除缓存
                    if hasattr(browser.set, 'clear_cache'):
                        browser.set.clear_cache()
                        print("已清除缓存")
                else:
                    print("浏览器对象没有set.cookies属性，无法清除cookie")
            except Exception as e:
                print(f"清除浏览器cookie失败: {e}")

            # 访问淘宝
            print(f"访问淘宝网站...")
            browser.get("https://www.taobao.com/")
            time.sleep(2)

            # 如果有cookie，则植入
            if cookie:
                print(f"植入用户 {profile_id} 的cookie...")
                self._set_cookies(browser, cookie)
            else:
                print(f"用户 {profile_id} 没有cookie")
                self.chrome_manager.update_profile_status(profile_id, "无Cookie")

                # 标记当前用户任务完成
                with task_lock:
                    task_counter.value += 1
                    print(f"用户 {profile_id} 无cookie，跳过任务，当前已完成 {task_counter.value} 个任务")

                    # 释放浏览器实例，但不关闭浏览器
                    browser_instance["in_use"] = False
                    browser_instance["current_profile"] = None

                return

            # 更新用户状态
            self.chrome_manager.update_profile_status(profile_id, "任务执行中")

            # 执行确认收货任务
            print(f"开始执行用户 {profile_id} 的确认收货任务...")
            result = self._execute_receipt_task(browser, profile_id, pay_password)

            # 更新任务计数
            with task_lock:
                task_counter.value += 1
                print(f"用户 {profile_id} 的任务已完成，当前已完成 {task_counter.value} 个任务")

            # 更新用户状态
            if result:
                self.chrome_manager.update_profile_status(profile_id, "任务已完成")
            else:
                # 检查是否是登录失效
                if self.chrome_manager.get_profile_by_id(profile_id).get('status') == "登录失效":
                    print(f"用户 {profile_id} 登录已失效，清除cookie")
                else:
                    self.chrome_manager.update_profile_status(profile_id, "任务失败")

        except Exception as e:
            print(f"处理用户 {profile_id} 的任务出错: {e}")
            traceback.print_exc()
            self.chrome_manager.update_profile_status(profile_id, f"任务出错: {str(e)}")
        finally:
            # 标记浏览器实例为可用
            with task_lock:
                browser_instance["in_use"] = False
                browser_instance["current_profile"] = None

    def _set_cookies(self, browser, cookie_str):
        """设置cookie到浏览器"""
        try:
            # 检查是否有set.cookies属性
            if not hasattr(browser, 'set') or not hasattr(browser.set, 'cookies'):
                print("浏览器对象没有set.cookies属性，无法设置cookie")
                return False

            # 如果cookie_str为空，直接返回
            if not cookie_str:
                print("cookie字符串为空，跳过设置")
                return False

            print("使用新版API设置cookie...")

            # 尝试使用JavaScript直接设置cookie
            try:
                # 如果是分号分隔的cookie字符串
                if ';' in cookie_str and '=' in cookie_str:
                    # 解析cookie字符串
                    cookie_parts = cookie_str.split(';')
                    for part in cookie_parts:
                        if '=' in part:
                            try:
                                name, value = part.strip().split('=', 1)
                                # 使用JavaScript设置cookie
                                js_code = f'document.cookie = "{name}={value}; domain=.taobao.com; path=/";'
                                browser.run_js(js_code)
                                print(f"使用JavaScript设置cookie: {name}")
                            except Exception as e:
                                print(f"使用JavaScript设置cookie失败: {e}")
                    print("使用JavaScript设置cookie完成")
                    return True
            except Exception as e:
                print(f"使用JavaScript设置cookie失败: {e}")

            # 如果JavaScript方式失败，尝试使用DrissionPage API
            try:
                # 如果是分号分隔的cookie字符串，直接设置
                if ';' in cookie_str and '=' in cookie_str:
                    # 解析cookie字符串
                    cookie_parts = cookie_str.split(';')
                    for part in cookie_parts:
                        if '=' in part:
                            try:
                                name, value = part.strip().split('=', 1)
                                cookie = {
                                    'name': name,
                                    'value': value,
                                    'domain': '.taobao.com',
                                    'path': '/'
                                }
                                browser.set.cookies(cookie)
                                print(f"使用API设置cookie: {name}")
                            except Exception as e:
                                print(f"使用API设置单个cookie失败: {e}")
                    print("使用API设置cookie完成")
                    return True
            except Exception as e:
                print(f"使用API设置cookie字符串失败: {e}")

            # 如果直接设置失败，尝试解析为JSON格式
            if cookie_str.startswith('[') and cookie_str.endswith(']'):
                try:
                    cookies = json.loads(cookie_str)
                    if isinstance(cookies, list) and cookies:
                        print(f"解析到 {len(cookies)} 个JSON格式cookie")
                        for cookie in cookies:
                            try:
                                if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                                    # 尝试使用JavaScript设置cookie
                                    name = cookie['name']
                                    value = cookie['value']
                                    domain = cookie.get('domain', '.taobao.com')
                                    path = cookie.get('path', '/')
                                    js_code = f'document.cookie = "{name}={value}; domain={domain}; path={path}";'
                                    browser.run_js(js_code)
                                    print(f"使用JavaScript设置JSON cookie: {name}")
                            except Exception as e:
                                print(f"设置单个JSON cookie失败: {e}")
                        print("JSON格式cookie设置完成")
                        return True
                except Exception as e:
                    print(f"解析JSON格式cookie失败: {e}")

            # 如果所有尝试都失败，返回失败
            print("所有cookie设置尝试均失败")
            return False

        except Exception as e:
            print(f"设置cookie失败: {e}")
            traceback.print_exc()
            return False

    def _execute_receipt_task(self, browser, profile_id, pay_password=''):
        """执行确认收货任务"""
        try:
            # 打开待收货订单页面
            print(f"用户 {profile_id} 打开待收货订单页面...")
            browser.get("https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitConfirm")

            # 等待页面加载
            print(f"用户 {profile_id} 等待页面加载...")
            try:
                # 尝试使用不同的等待方法，兼容不同版本的DrissionPage
                if hasattr(browser.wait, 'load_complete'):
                    browser.wait.load_complete()
                elif hasattr(browser, 'wait_load_complete'):
                    browser.wait_load_complete()
                elif hasattr(browser, 'wait_for_load_complete'):
                    browser.wait_for_load_complete()
                else:
                    # 如果没有特定的等待方法，使用通用方法
                    time.sleep(5)  # 简单等待5秒
            except Exception as e:
                print(f"等待页面加载完成出错: {e}")
                # 出错时使用简单等待
                time.sleep(5)

            # 额外等待确保页面完全加载
            time.sleep(3)

            # 检查登录状态
            current_url = browser.url
            print(f"用户 {profile_id} 当前URL: {current_url}")
            if "login.taobao.com" in current_url:
                print(f"用户 {profile_id} 登录已失效或未登录")

                # 更新状态为登录失效
                self.chrome_manager.update_profile_status(profile_id, "登录失效")

                # 不等待用户手动登录，直接返回失败
                return False

            # 获取待收货订单列表
            print(f"用户 {profile_id} 获取待收货订单列表...")
            order_items = browser.eles('css:.bought-wrapper-mod__trade-item')
            print(f"用户 {profile_id} 找到 {len(order_items) if order_items else 0} 个订单项")

            if not order_items:
                print(f"用户 {profile_id} 没有待收货订单")
                self.chrome_manager.update_profile_status(profile_id, "没有待确认订单")
                return True

            # 获取配置的日期范围
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.json')
            start_date = None
            end_date = None

            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    start_date_str = config.get('start_date', '')
                    end_date_str = config.get('end_date', '')

                    if start_date_str:
                        start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d').date()
                    if end_date_str:
                        end_date = datetime.datetime.strptime(end_date_str, '%Y-%m-%d').date()

            # 处理每个订单
            completed_count = 0
            total_count = len(order_items)

            print(f"找到 {total_count} 个待收货订单")

            # 更新用户状态
            self.chrome_manager.update_profile_status(profile_id, "处理中", {
                "total_count": total_count,
                "completed_count": completed_count,
                "current_order_id": ""
            })

            for i, item in enumerate(order_items):
                try:
                    # 检查是否收到停止信号
                    if not self.receipt_task_running:
                        print(f"收到停止信号，中断确认收货任务")
                        break

                    # 获取订单ID
                    order_id = item.attr('data-id')
                    if not order_id:
                        print("无法获取订单ID，跳过")
                        continue

                    # 获取订单时间
                    time_elem = item.ele('css:.bought-wrapper-mod__create-time')
                    if time_elem:
                        order_time_str = time_elem.text.strip()
                    else:
                        print("未找到订单时间元素，跳过")
                        continue

                    print(f"处理订单: {order_id}, 时间: {order_time_str}")

                    # 解析订单时间
                    try:
                        if "年" in order_time_str and "月" in order_time_str and "日" in order_time_str:
                            # 格式如：2023年05月20日 12:34:56
                            order_time = datetime.datetime.strptime(order_time_str, '%Y年%m月%d日 %H:%M:%S')
                        elif "-" in order_time_str:
                            # 格式如：2023-05-20 12:34:56
                            order_time = datetime.datetime.strptime(order_time_str, '%Y-%m-%d %H:%M:%S')
                        else:
                            # 其他格式，跳过
                            print(f"无法解析订单时间格式: {order_time_str}")
                            continue

                        order_date = order_time.date()
                        print(f"订单日期: {order_date}")

                        # 检查是否在日期范围内
                        if start_date and order_date < start_date:
                            print(f"订单{order_id}的日期{order_date}早于开始日期{start_date}，跳过")
                            continue

                        if end_date and order_date > end_date:
                            print(f"订单{order_id}的日期{order_date}晚于结束日期{end_date}，跳过")
                            continue
                    except Exception as e:
                        print(f"解析订单时间出错: {e}")
                        # 如果无法解析时间，默认处理该订单

                    # 更新当前处理的订单ID
                    self.chrome_manager.update_profile_status(profile_id, "处理中", {
                        "total_count": total_count,
                        "completed_count": completed_count,
                        "current_order_id": order_id
                    })

                    # 执行确认收货操作
                    print(f"开始确认收货订单: {order_id}")
                    success = self.confirm_receipt(browser, order_id, pay_password)

                    if success:
                        print(f"订单 {order_id} 确认收货成功")
                        completed_count += 1
                    else:
                        print(f"订单 {order_id} 确认收货失败")

                    # 更新用户状态
                    self.chrome_manager.update_profile_status(profile_id, "处理中", {
                        "total_count": total_count,
                        "completed_count": completed_count,
                        "current_order_id": ""
                    })

                    # 随机延迟，避免操作过快
                    if i < total_count - 1:  # 最后一个订单不需要延迟
                        delay = random.uniform(1, 3)
                        print(f"等待 {delay:.1f} 秒后处理下一个订单...")
                        time.sleep(delay)

                except Exception as e:
                    print(f"处理订单时出错: {e}")
                    traceback.print_exc()

            print(f"用户 {profile_id} 确认收货任务完成，共处理 {completed_count}/{total_count} 个订单")
            return True

        except Exception as e:
            print(f"执行确认收货任务出错: {e}")
            traceback.print_exc()
            return False

    def _monitor_browser_pool_tasks(self, task_lock, task_counter, task_queue):
        """监控浏览器池任务状态"""
        try:
            # 设置最大无活动时间（秒）
            max_idle_time = 60
            last_activity_time = time.time()
            last_counter_value = 0

            while self.receipt_task_running:
                time.sleep(3)  # 每3秒检查一次
                current_time = time.time()

                with task_lock:
                    # 检查任务是否完成
                    if len(task_queue) == 0 and all(not instance.get("in_use") for instance in self.browser_instances):
                        print("所有用户的自动收货任务已完成")
                        self.receipt_task_running = False

                        # 关闭所有浏览器实例
                        print("任务完成，关闭所有浏览器实例...")
                        for instance in self.browser_instances:
                            try:
                                if instance.get("browser"):
                                    instance["browser"].quit()
                                    print(f"已关闭浏览器实例，端口: {instance.get('port')}")
                            except Exception as e:
                                print(f"关闭浏览器实例失败: {e}")

                        # 清空浏览器实例列表
                        self.browser_instances = []
                        break

                    # 检查是否有活动
                    current_counter_value = task_counter.value
                    if current_counter_value > last_counter_value:
                        last_activity_time = current_time
                        last_counter_value = current_counter_value
                    elif current_time - last_activity_time > max_idle_time:
                        print(f"任务已超过{max_idle_time}秒无活动，可能存在卡死情况，强制结束任务")
                        self.receipt_task_running = False

                        # 关闭所有浏览器实例
                        print("任务超时，关闭所有浏览器实例...")
                        for instance in self.browser_instances:
                            try:
                                if instance.get("browser"):
                                    instance["browser"].quit()
                                    print(f"已关闭浏览器实例，端口: {instance.get('port')}")
                            except Exception as e:
                                print(f"关闭浏览器实例失败: {e}")

                        # 清空浏览器实例列表
                        self.browser_instances = []
                        break

                    # 输出当前状态
                    active_browsers = sum(1 for instance in self.browser_instances if instance.get("in_use"))
                    print(f"任务状态: 已完成 {task_counter.value} 个任务, 活跃浏览器 {active_browsers}, 队列中 {len(task_queue)} 个任务")

                    # 输出每个浏览器的状态
                    for i, instance in enumerate(self.browser_instances):
                        status = "使用中" if instance.get("in_use") else "空闲"
                        current_profile = instance.get("current_profile")
                        profile_info = f", 处理用户: {current_profile}" if current_profile else ""
                        print(f"浏览器 {i+1}: {status}{profile_info}")
        except Exception as e:
            print(f"监控线程异常: {e}")
            traceback.print_exc()

    def confirm_receipt(self, driver, order_id, pay_password='', timeout=30):
        """确认收货操作"""
        try:
            # 打开订单详情页面
            order_url = f"https://buyertrade.taobao.com/trade/detail/trade_item_detail.htm?bizOrderId={order_id}"
            driver.get(order_url)
            print(f"已打开订单详情页: {order_url}")

            # 等待页面加载
            try:
                # 尝试使用不同的等待方法，兼容不同版本的DrissionPage
                if hasattr(driver.wait, 'load_complete'):
                    driver.wait.load_complete()
                elif hasattr(driver, 'wait_load_complete'):
                    driver.wait_load_complete()
                elif hasattr(driver, 'wait_for_load_complete'):
                    driver.wait_for_load_complete()
                else:
                    # 如果没有特定的等待方法，使用通用方法
                    print("使用简单等待方法")
                    time.sleep(5)  # 简单等待5秒
            except Exception as e:
                print(f"等待页面加载完成出错: {e}")
                # 出错时使用简单等待
                time.sleep(5)

            # 额外等待确保页面完全加载
            time.sleep(3)

            # 等待"确认收货"按钮出现
            confirm_button_xpath = '//button[contains(text(), "确认收货") or contains(@class, "confirmBtn")]'
            print("等待确认收货按钮出现...")

            confirm_button = driver.ele(f'xpath:{confirm_button_xpath}', timeout=timeout)
            if not confirm_button:
                print("未找到确认收货按钮")
                return False

            # 点击确认收货按钮
            print("点击确认收货按钮...")
            driver.run_js("arguments[0].scrollIntoView({block: 'center'});", confirm_button)
            time.sleep(0.5)
            driver.run_js("arguments[0].click();", confirm_button)

            # 等待确认弹窗出现
            print("等待确认弹窗出现...")
            time.sleep(2)

            # 点击最终确认按钮
            final_confirm_xpath = '//button[contains(text(), "确定") or contains(@class, "confirmBtn") or contains(@class, "submit")]'
            final_confirm = driver.ele(f'xpath:{final_confirm_xpath}', timeout=timeout)
            if not final_confirm:
                print("未找到最终确认按钮")
                return False

            print("点击最终确认按钮...")
            final_confirm.click()

            # 检查是否需要输入支付密码
            password_input_xpath = '//input[@type="password" and contains(@class, "payPassword")]'
            password_input = driver.ele(f'xpath:{password_input_xpath}', timeout=5)

            if password_input:
                # 如果有支付密码，则输入
                if pay_password:
                    print("输入支付密码...")
                    password_input.input(pay_password)

                    # 点击确认按钮
                    submit_button_xpath = '//button[contains(text(), "确定") or contains(@class, "submit")]'
                    submit_button = driver.ele(f'xpath:{submit_button_xpath}', timeout=5)
                    if submit_button:
                        submit_button.click()
                    else:
                        print("未找到支付密码确认按钮")
                        return False
                else:
                    print("需要支付密码但未提供，无法完成确认收货")
                    return False

            print("已确认收货，等待2秒...")
            time.sleep(2)

            return True
        except Exception as e:
            print(f"确认收货操作失败: {e}")
            traceback.print_exc()
            return False

    def _check_and_clean_chrome_processes(self):
        """检查并清理可能存在的Chrome进程"""
        print("检查是否存在Chrome进程...")
        try:
            # 尝试使用psutil检查Chrome进程
            try:
                import psutil
                chrome_processes = []
                for proc in psutil.process_iter(['pid', 'name']):
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        chrome_processes.append(proc)

                if chrome_processes:
                    print(f"发现 {len(chrome_processes)} 个Chrome进程")
                    # 不立即终止进程，只是记录信息
                else:
                    print("未发现Chrome进程")
                    return True
            except ImportError:
                print("psutil模块未安装，无法检查Chrome进程")
        except Exception as e:
            print(f"检查Chrome进程时出错: {e}")
            traceback.print_exc()

        # 不强制终止进程，返回成功
        return True