#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
订单相关任务模块
处理取消待付款订单和刷新用户列表等任务
"""

import time
import json
import re
import hashlib
import traceback
import requests
from urllib.parse import quote
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 异步任务处理函数
def process_cancel_orders(progress_callback=None, config_manager=None, profile_ids=None):
    """
    异步处理取消待付款订单任务

    Args:
        progress_callback: 进度回调函数
        config_manager: 配置管理器实例
        profile_ids: 用户ID列表

    Returns:
        dict: 处理结果
    """
    if not profile_ids:
        return {"success": False, "message": "请选择至少一个用户"}

    # 确保profile_ids是列表
    if not isinstance(profile_ids, list):
        profile_ids = [profile_ids]

    results = []
    success_count = 0
    fail_count = 0
    total_count = len(profile_ids)
    processed_count = 0

    # 处理每个用户
    for profile_id in profile_ids:
        # 更新进度
        processed_count += 1
        progress = int((processed_count / total_count) * 100)
        progress_callback(progress, f"正在处理用户 {processed_count}/{total_count}")

        # 从配置文件中获取用户的cookie
        profile = config_manager.get_profile(profile_id)
        if not profile or not profile.get('cookie'):
            results.append({
                "profile_id": profile_id,
                "success": False,
                "message": "未找到用户cookie"
            })
            fail_count += 1
            continue

        # 获取用户名称
        profile_name = profile.get('name', profile_id)

        # 获取待付款订单列表
        wait_pay_orders = get_wait_pay_orders(profile)
        if not wait_pay_orders:
            results.append({
                "profile_id": profile_id,
                "profile_name": profile_name,
                "success": True,
                "message": "没有待付款订单"
            })
            success_count += 1
            continue

        # 取消每个待付款订单
        order_results = []
        for order in wait_pay_orders:
            order_id = order.get('orderId')
            if not order_id:
                continue

            # 调用取消订单API
            cancel_result = cancel_order(profile, order_id)
            order_results.append({
                "order_id": order_id,
                "success": cancel_result.get('success', False),
                "message": cancel_result.get('message', '')
            })

            if cancel_result.get('success', False):
                success_count += 1
            else:
                fail_count += 1

        results.append({
            "profile_id": profile_id,
            "profile_name": profile_name,
            "success": True,
            "orders": order_results
        })

    return {
        "success": True,
        "message": f"处理完成: {success_count}个成功, {fail_count}个失败",
        "success_count": success_count,
        "fail_count": fail_count,
        "results": results
    }

def process_refresh_profiles(progress_callback=None, chrome_manager=None):
    """
    异步处理刷新用户列表任务

    Args:
        progress_callback: 进度回调函数
        chrome_manager: Chrome管理器实例

    Returns:
        dict: 处理结果
    """
    try:
        # 导入app模块以访问全局缓存变量
        import sys
        import os
        # 获取当前模块的目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 确保当前目录在sys.path中
        if current_dir not in sys.path:
            sys.path.append(current_dir)
        # 导入app模块
        import app as app_module
        # 更新进度
        progress_callback(10, "开始刷新用户列表")

        # 刷新用户列表
        chrome_manager.refresh_profiles()

        # 更新进度
        progress_callback(50, "用户列表刷新完成，开始获取订单数量")

        # 获取所有用户
        profiles = chrome_manager.get_profiles()
        total_profiles = len(profiles)
        processed_count = 0

        # 获取每个用户的订单数量
        for profile in profiles:
            processed_count += 1
            progress = 50 + int((processed_count / total_profiles) * 50)
            progress_callback(progress, f"正在获取用户订单数量 {processed_count}/{total_profiles}")

            # 获取订单数量
            try:
                # 直接使用profile中的cookie获取订单数量
                if profile.get('cookie'):
                    # 获取cookie
                    cookie = profile.get('cookie')

                    # 导入必要的模块
                    import hashlib
                    import urllib.parse
                    import requests
                    import re
                    # 订单类型: all=全部订单, waitSend=待发货, waitConfirm=待收货, waitPay=待付款, waitRate=待评价
                    clean_cookie = cookie
                    # 删除_m_h5_tk部分
                    if "_m_h5_tk=" in clean_cookie:
                        clean_cookie = re.sub(r'_m_h5_tk=[^;]+;', '', clean_cookie)
                    # 删除_m_h5_tk_enc部分
                    if "_m_h5_tk_enc=" in clean_cookie:
                        clean_cookie = re.sub(r'_m_h5_tk_enc=[^;]+;', '', clean_cookie)
                    h5 = goToken(clean_cookie)
                    t_cookie = h5 + cookie

                    # 准备请求参数
                    t = str(int(time.time() * 1000))
                    data = '{}'
                    token = t_cookie.split('_m_h5_tk=')[1].split('_')[0]

                    # 构建请求头
                    headers = {
                        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
                        'cookie': t_cookie
                    }

                    # 计算签名
                    sign_text = token + '&' + t + '&' + '12574478' + '&' + data
                    sign = hashlib.md5(sign_text.encode('utf-8')).hexdigest()

                    # 构建URL
                    api = 'mtop.order.taobao.countv2'
                    v = '1.0'
                    url = f'https://h5api.m.taobao.com/h5/{api}/{v}/?jsv=2.4.9&appKey=12574478&t={t}&sign={sign}&api={api}&v={v}&data={urllib.parse.quote(data)}'

                    # 发送请求
                    response = requests.get(url, headers=headers)
                    result = response.json()
                    print(result)
                    # 检查是否登录过期
                    if 'ret' in result and any('SESSION_EXPIRED' in ret or 'Session过期' in ret for ret in result['ret']):
                        # 更新用户状态为账号失效
                        chrome_manager.update_profile_status(profile['id'], 'inactive')
                        print(f"获取用户 {profile['id']} 订单数量失败: 登录已过期，请重新获取Cookie")
                        continue

                    # 检查响应是否成功
                    if 'data' not in result or 'result' not in result['data']:
                        print(f"获取用户 {profile['id']} 订单数量失败: 接口返回数据格式错误")
                        continue

                    # 提取订单数量信息
                    order_counts = {}
                    for item in result['data']['result']:
                        order_counts[item['tabCode']] = item['count']

                    # 更新用户状态为正常
                    chrome_manager.update_profile_status(profile['id'], 'active')

                    # 更新profile中的order_counts字段
                    profile['order_counts'] = {
                        "waitPay": order_counts.get('waitPay', '0'),     # 待付款
                        "waitSend": order_counts.get('waitSend', '0'),   # 待发货
                        "waitConfirm": order_counts.get('waitConfirm', '0'), # 待收货
                        "waitRate": order_counts.get('waitRate', '0')    # 待评价
                    }

                    print(f"成功获取用户 {profile['id']} 的订单数量")
                else:
                    print(f"获取用户 {profile['id']} 订单数量失败: 未找到cookie")
            except Exception as e:
                print(f"获取用户 {profile['id']} 订单数量失败: {e}")

        # 更新app.py中的缓存
        app_module.profiles_cache = profiles
        app_module.profiles_cache_timestamp = time.time()
        print(f"异步任务已更新Chrome用户列表缓存，共 {len(profiles)} 个用户")

        # 更新进度
        progress_callback(100, "刷新完成")

        return {
            "success": True,
            "message": f"成功刷新 {total_profiles} 个用户信息",
            "profile_count": total_profiles
        }
    except Exception as e:
        error_msg = f"刷新用户列表失败: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        return {
            "success": False,
            "message": error_msg
        }

def goToken(cookie):
    """获取淘宝API所需的token"""
    t = str(int(time.time()))
    date = '{}'
    xapi = 'mtop.tmall.kangaroo.core.service.route.aldlampservicefixedresv2'
    xv = '1.0'
    token = ''
    str1 = token + '&' + t + '&12574478&' + date
    str2 = bytes(str1, encoding='utf-8')  # md5
    sign = hashlib.md5(str2).hexdigest()
    data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=2019@weex_h5_0.12.14&data=' + str(
        quote(date, 'utf-8'))
    url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
    head = {
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
        'cookie': cookie,
        'url': url2
    }

    r = requests.get(url2, timeout=20, headers=head, stream=False, verify=False)
    if r.text.find('为空') != -1 or r.text.find('过期') != -1:
        set_cookie = str(r.headers.get('Set-Cookie'))
        mk = '_m_h5_tk=' + set_cookie.split('_m_h5_tk=')[1].split(';')[0] + ';'
        enc = '_m_h5_tk_enc=' + set_cookie.split('_m_h5_tk_enc=')[1].split(';')[0] + ';'
        return mk + enc
    return ''

def get_wait_pay_orders(profile):
    """获取用户的待付款订单列表"""
    try:
        clean_cookie = profile.get('cookie', '')
        # 处理cookie，删除令牌部分
        cookie = clean_cookie
        # 删除_m_h5_tk部分
        if "_m_h5_tk=" in clean_cookie:
            cookie = re.sub(r'_m_h5_tk=[^;]+;', '', clean_cookie)
        # 删除_m_h5_tk_enc部分
        if "_m_h5_tk_enc=" in clean_cookie:
            cookie = re.sub(r'_m_h5_tk_enc=[^;]+;', '', clean_cookie)
        h5 = goToken(cookie)
        cookie = h5 + cookie

        # 构建请求头
        headers = {
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
            'cookie': cookie
        }

        # 获取待付款订单列表
        orders = []
        for i in range(1):  # 只获取第一页
            t = str(int(time.time()))
            date = '{"OrderType":"OrderList","appName":"tborder","appVersion":"3.0","condition":"{\\"categoryText\\":[null],\\"filterSelectInfo\\":{\\"isFilterResult\\":true,\\"selectedCategoryIndex\\":\\"-1\\",\\"selectedEndTime\\":\\"\\",\\"selectedEndTimeIndex\\":\\"1\\",\\"selectedGiftIndex\\":\\"-1\\",\\"selectedSourceIndex\\":\\"-1\\",\\"selectedStartTime\\":\\"\\",\\"selectedStartTimeIndex\\":\\"1\\",\\"selectedTimeIndex\\":\\"-1\\"},\\"onlyGiftFilter\\":\\"false\\",\\"orderFilterExtParam\\":{\\"beginTime\\":\\"\\",\\"endTime\\":\\"\\",\\"filterSource\\":\\"\\",\\"giftType\\":\\"\\"},\\"version\\":\\"1.0.0\\",\\"wordType\\":\\"3\\"}","page":"' + str(i) + '","tabCode":"waitPay","templateConfigVersion":"0"}'
            xapi = 'mtop.taobao.order.queryboughtlistv2'
            xv = '1.0'
            token = cookie.split('_m_h5_tk=')[1].split(';')[0].split('_')[0]
            str1 = token + '&' + t + '&12574478&' + date
            str2 = bytes(str1, encoding='utf-8')  # md5
            sign = hashlib.md5(str2).hexdigest()
            data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&type=json&ttid=700170@taobao_android_10.27.10&dataType=json&data=' + str(
                quote(date, 'utf-8'))
            url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2

            # 发送请求
            r = requests.get(url2, timeout=20, headers=headers, stream=False, verify=False)
            result = r.json()
            # 提取订单信息
            if 'data' in result and 'data' in result['data']:
                for key, value in result['data']['data'].items():
                    if key.startswith("item_"):
                        order_id = value.get('fields', {}).get('basicInfo', {}).get('orderId')
                        if order_id:
                            orders.append({
                                'orderId': order_id,
                                'title': value.get('fields', {}).get('item', {}).get('title', ''),
                                'status': value.get('fields', {}).get('queryParams', {}).get('status', '')
                            })

        return orders
    except Exception as e:
        print(f"获取待付款订单失败: {e}")
        traceback.print_exc()
        return []

def cancel_order(profile, order_id):
    """取消指定的订单"""
    try:
        clean_cookie = profile.get('cookie', '')
        # 处理cookie，删除令牌部分
        cookie = clean_cookie
        # 删除_m_h5_tk部分
        if "_m_h5_tk=" in clean_cookie:
            cookie = re.sub(r'_m_h5_tk=[^;]+;', '', clean_cookie)
        # 删除_m_h5_tk_enc部分
        if "_m_h5_tk_enc=" in clean_cookie:
            cookie = re.sub(r'_m_h5_tk_enc=[^;]+;', '', clean_cookie)
        h5 = goToken(cookie)
        cookie = h5 + cookie

        # 构建请求头
        headers = {
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
            'cookie': cookie
        }

        # 构建取消订单请求
        t = str(int(time.time()))
        date = '{"orderId":"' + str(order_id) + '","code":"cancelOrder","map":"{\\"reasonId\\":\\"1\\"}","ttid":"201200@taobao_h5_9.18.0","requestIdentity":"1@chrome_windows_131.0.0.0#pc","condition":"{\\"version\\":\\"1.0.0\\"}","extParams":"{\\"useNewDetail\\":true}"}'
        xapi = 'mtop.order.doOp'
        xv = '3.0'
        token = cookie.split('_m_h5_tk=')[1].split(';')[0].split('_')[0]
        str1 = token + '&' + t + '&12574478&' + date
        str2 = bytes(str1, encoding='utf-8')  # md5
        sign = hashlib.md5(str2).hexdigest()
        data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&type=json&ttid=201200@taobao_h5_9.18.0&dataType=json&data=' + str(
            quote(date, 'utf-8'))
        url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2

        # 发送请求
        r = requests.get(url2, timeout=20, headers=headers, stream=False, verify=False)
        result = r.json()

        # 检查响应
        if 'ret' in result and 'SUCCESS::调用成功' in result['ret'][0]:
            return {
                "success": True,
                "message": "订单取消成功"
            }
        else:
            error_msg = result.get('ret', ['未知错误'])[0]
            return {
                "success": False,
                "message": f"订单取消失败: {error_msg}"
            }
    except Exception as e:
        print(f"取消订单失败: {e}")
        traceback.print_exc()
        return {
            "success": False,
            "message": f"取消订单失败: {str(e)}"
        }
