import json

# 测试URL转义
test_url = "https://img.alicdn.com/imgextra/i4/test.jpg"

# 方法1：手动转义（错误的方法）
manual_escaped = test_url.replace("/", "\\/")
print("手动转义结果:", manual_escaped)

# 方法2：JSON序列化自动转义（正确的方法）
data = {"url": test_url}
json_result = json.dumps(data, separators=(',', ':'))
print("JSON序列化结果:", json_result)

# 提取JSON中的URL
import re
url_match = re.search(r'"url":"([^"]+)"', json_result)
if url_match:
    auto_escaped_url = url_match.group(1)
    print("自动转义的URL:", auto_escaped_url)

# 对比1.txt中的格式
expected = "https:\/\/img.alicdn.com\/imgextra\/i4\/test.jpg"
print("期望格式:", expected)
print("格式匹配:", auto_escaped_url == expected)
