import requests,json,time,re,hashlib,uuid,os
from urllib.parse import quote, quote_plus
from requests_toolbelt.multipart.encoder import MultipartEncoder

def extract_upload_params_from_cookie(cookie):
    """
    从cookie中提取上传所需的参数

    Args:
        cookie: 完整的cookie字符串

    Returns:
        dict: 包含_tb_token_等参数的字典
    """
    params = {}

    # 提取_tb_token_
    if '_tb_token_=' in cookie:
        token_part = cookie.split('_tb_token_=')[1]
        if ';' in token_part:
            params['_tb_token_'] = token_part.split(';')[0]
        else:
            params['_tb_token_'] = token_part

    return params


def upload_image_to_platform(image_path, cookie, seller_order_info=None):
    """
    上传图片到淘宝平台并返回图片URL

    Args:
        image_path: 本地图片文件路径
        cookie: 完整的cookie字符串
        seller_order_info: 包含sellerNumId和bizOrderId的字典

    Returns:
        str: 上传后的图片URL，如果上传失败返回空字符串
    """
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"图片文件不存在: {image_path}")
        return ""

    # 提取上传参数
    upload_params = extract_upload_params_from_cookie(cookie)

    # 构建上传URL
    upload_url = "https://rate.taobao.com/upload_pic.htm?_input_charset=utf-8&at_iframe=1"

    # 准备文件数据
    filename = os.path.basename(image_path)

    # 检测文件类型
    file_ext = os.path.splitext(filename)[1].lower()
    if file_ext == '.png':
        content_type = 'image/png'
    elif file_ext in ['.jpg', '.jpeg']:
        content_type = 'image/jpeg'
    else:
        content_type = 'image/png'  # 默认使用png

    # 使用 MultipartEncoder 构建 multipart/form-data
    with open(image_path, 'rb') as f:
        multipart_data = MultipartEncoder(
            fields={
                'sellerNumId': seller_order_info.get('sellerNumId', ''),
                'bizOrderId': seller_order_info.get('bizOrderId', ''),
                '_tb_token_': upload_params['_tb_token_'],
                'picture': (filename, f, content_type),
                'type':'ajax'
            },
            boundary='----WebKitFormBoundaryV7vnLuJj2A8x1Wqz'
        )

        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': '*/*',
            'Origin': 'https://rate.taobao.com',
            'Referer': 'https://rate.taobao.com/remarkSeller.jhtml?spm=a1z09.2.0.0.1a9e2e8dBEuqBp&tradeID=2584823881098950055&returnURL=https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm',
            'Cookie': cookie,
            'Content-Type': multipart_data.content_type
        }

        print(f"开始上传图片: {image_path}")
        print(f"上传参数: sellerNumId={seller_order_info.get('sellerNumId', '')}, bizOrderId={seller_order_info.get('bizOrderId', '')}")

        # 发送上传请求
        response = requests.post(
            upload_url,
            data=multipart_data,
            headers=headers,
            timeout=30,proxies=proxies,
            verify=False
        )

        imgurl = 'https://img.alicdn.com/imgextra/i3/' + str(response.json()['thumbnail']).replace('_40x40.jpg','').split('/')[-1]
        print(imgurl)

        return imgurl



def scan_image_directory(image_directory):
    """
    扫描图片目录，按商品ID组织图片文件

    Args:
        image_directory: 图片目录路径

    Returns:
        dict: 商品ID到图片文件列表的映射
              格式: {商品ID: [图片文件路径1, 图片文件路径2, ...]}
    """
    if not image_directory or not os.path.exists(image_directory):
        return {}

    image_map = {}

    # 支持的图片格式
    supported_formats = ('.png', '.jpg', '.jpeg')

    # 遍历目录中的所有文件
    for filename in os.listdir(image_directory):
        if not filename.lower().endswith(supported_formats):
            continue

        # 提取文件名（不含扩展名）
        name_without_ext = os.path.splitext(filename)[0]

        # 解析商品ID
        if '-' in name_without_ext:
            # 多张图片格式：商品ID-序号
            goods_id = name_without_ext.split('-')[0]
        else:
            # 单张图片格式：商品ID
            goods_id = name_without_ext

        # 构建完整文件路径
        file_path = os.path.join(image_directory, filename)

        # 添加到映射中
        if goods_id not in image_map:
            image_map[goods_id] = []
        image_map[goods_id].append(file_path)

    # 对每个商品的图片列表按文件名排序
    for goods_id in image_map:
        image_map[goods_id].sort()

    return image_map

def extract_goods_id_from_api_response(json_data):
    """
    从淘宝API响应中提取商品ID

    Args:
        json_data: 淘宝API返回的JSON数据

    Returns:
        str: 商品ID，如果提取失败返回空字符串
    """
    for key, value in json_data.items():
        if key.startswith("recordOnionShow_"):

            # 尝试从fields中提取商品信息
            fields = value.get("fields", {})
            if not fields:
                continue

            # 从extraGoodsParams中获取goods_id
            extra_goods_params = fields.get("extraGoodsParams", {})
            if not extra_goods_params:
                continue

            if "goods_id" in extra_goods_params:
                goods_id = str(extra_goods_params["goods_id"])
                return goods_id
    return ""

def create_image_data(image_url, goods_id, pub_session=None):
    """创建完整的图片数据结构，与淘宝评价系统完全兼容 - 精确匹配实际POST数据格式"""

    # 如果没有提供pub_session，生成一个新的（每张图片不同）
    if not pub_session:
        pub_session = str(uuid.uuid4())

    # 确保URL格式正确，移除多余空格，让JSON序列化自动处理转义
    image_url = image_url.replace(" ", "")
    print(image_url)

    # 按照实际POST数据的精确格式构建statInfo
    # 注意：这里的转义层级必须与实际数据完全匹配

    # 第一层：构建最内层的配置JSON字符串
    music_frame_config_str = json.dumps({
        "type": "music",
        "frameResolutionSize": 256,
        "frameCount": 2,
        "frameInterval": 0.5,
        "frameQuality": 50,
        "frameDecodeTimeout": 1000,
        "frameUploadTimeout": 1500
    }, separators=(',', ':'))

    topic_frame_config_str = json.dumps({
        "type": "topic",
        "frameResolutionSize": 256,
        "frameCount": 5,
        "frameInterval": 0.2,
        "frameQuality": 70,
        "frameDecodeTimeout": 5000,
        "frameUploadTimeout": 4000
    }, separators=(',', ':'))

    # 第二层：构建ab_test_info JSON字符串
    ab_test_info_str = json.dumps({
        "musicFrameConfig": music_frame_config_str,
        "topicFrameConfig": topic_frame_config_str
    }, separators=(',', ':'))

    # 第三层：构建完整的statInfo结构（按实际数据字段顺序）
    stat_info = {
        "ab_test_info": ab_test_info_str,
        "camera_rotation": 0,
        "filter": [{}],
        "fun_id": {},
        "goods_id": str(goods_id),
        "is_hq_record": False,
        "itemsticker_items": [],
        "pub_session": pub_session,
        "source": "user_record",
        "additionalInfo": {
            "imageSource": "1",
            "containExif": False,
            "isScreenshot": False,
            "OS": "Android"
        }
    }

    # 返回完整的图片数据结构
    return {
        "fileSourceTag": "taobao_camera",
        "height": 1440,
        "statInfo": json.dumps(stat_info, separators=(',', ':')),  # 单次序列化
        "url": image_url,
        "width": 1080
    }
def goToken(cookie):
    """获取淘宝API所需的token"""
    t = str(int(time.time()))
    date = '{}'
    xapi = 'mtop.tmall.kangaroo.core.service.route.aldlampservicefixedresv2'
    xv = '1.0'
    token = ''
    str1 = token + '&' + t + '&12574478&' + date
    str2 = bytes(str1, encoding='utf-8')  # md5
    sign = hashlib.md5(str2).hexdigest()
    data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=2019@weex_h5_0.12.14&data=' + str(
        quote(date, 'utf-8'))
    url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
    head = {
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
        'cookie': cookie,
        'url': url2
    }
    proxies = {"http": "http://127.0.0.1:8887",
               "https": "http://127.0.0.1:8887"}
    r = requests.get(url2, timeout=20, headers=head, proxies=proxies,stream=False, verify=False)
    if r.text.find('为空') != -1 or r.text.find('过期') != -1:
        set_cookie = str(r.headers.get('Set-Cookie'))
        mk = '_m_h5_tk=' + set_cookie.split('_m_h5_tk=')[1].split(';')[0] + ';'
        enc = '_m_h5_tk_enc=' + set_cookie.split('_m_h5_tk_enc=')[1].split(';')[0] + ';'
        return mk + enc
    else:
        return ''
def to_comment(final_params, cookie):
    """提交评价到淘宝服务器"""
    # 使用json模块正确序列化数据，避免编码问题
    date = json.dumps(final_params)
    xapi = 'mtop.taobao.rate.component.publish'
    xv = '1.0'
    t = str(int(time.time()))
    token = cookie.split('_m_h5_tk=')[1].split('_')[0]
    str1 = token + '&' + t + '&12574478&' + date
    str2 = bytes(str1, encoding='utf-8')  # md5
    sign = hashlib.md5(str2).hexdigest()
    data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=700170@taobao_android_10.27.10'
    url2 = 'https://guide-acs.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
    head = {
        'user-agent': 'MTOPSDK/3.1.1.7+(Android;11;Google;Pixel+4a)',
        'cookie': cookie,
        'Content-type': 'application/x-www-form-urlencoded; charset=utf-8'
    }
    proxies = {"http": "http://127.0.0.1:8887",
               "https": "http://127.0.0.1:8887"}
    # 使用json参数而不是data参数，让requests自动处理JSON序列化和编码
    result = requests.post(url2, timeout=20, data={'data': date}, headers=head, proxies=proxies,verify=False)
    print(result.text)
    return result.json()

cookie = 'thw=cn; t=abd73d967394735f92bb87a256e4ca5a; _tb_token_=ee183e33981e5; cookie2=1c7281608be2f2147c1e1bb2776285fb; wk_cookie2=1048ac522e2b01ddba69ef35a8e87d81; wk_unb=UUpgRKryugbCESNt4g%3D%3D; aui=2212740955500; useNativeIM=false; wwUserTip=false; sca=a5e96ba9; _uab_collina=174834077345555795110278; _umdata=GE35D1E4F1E0C30602659A2F327157074BD568D; _samesite_flag_=true; cna=eD73HFthMiICARsrz+Sezxu3; 3PcFlag=1748382834945; lgc=t_1635766629972_054; cancelledSubSites=empty; dnk=t_1635766629972_054; tracknick=t_1635766629972_054; sn=; _hvn_lgc_=0; havana_lgc2_0=eyJoaWQiOjIyMTI3NDA5NTU1MDAsInNnIjoiNDgyM2E5MzRkYzQwOTgwOGI0MTFlNjRjMTlhMDlmNmMiLCJzaXRlIjowLCJ0b2tlbiI6IjEwN3FTR0QwYVowZlpGREZZMmJwM01RIn0; ariaDefaultTheme=undefined; xlly_s=1; unb=2212740955500; uc3=id2=UUpgRKryugbCESNt4g%3D%3D&nk2=F6k3Hs%2Fm6MYjoXCLvkVLxyX2Kw%3D%3D&vt3=F8dD2f5q82TMDuE7Lqc%3D&lg2=Vq8l%2BKCLz3%2F65A%3D%3D; csg=57426ecb; cookie17=UUpgRKryugbCESNt4g%3D%3D; skt=893c5e9d019935b4; existShop=MTc0OTAzMzQwNQ%3D%3D; uc4=id4=0%40U2gqy1txckoZ3LAdDde%2B9g0%2BeUZNaQUw&nk4=0%40FbMocGfzwBQ8aSqA70BxCLtIiZjtiSY6En1NTvxj; _cc_=VT5L2FSpdA%3D%3D; _l_g_=Ug%3D%3D; sg=403; _nk_=t_1635766629972_054; cookie1=VAFf%2BE%2B1dYinyy6Z9nX%2F6YcrsZkBJ09fmkBj%2FjHSq08%3D; sgcookie=E100qBC%2F2KaooJrN%2BE21WTu7%2BasoqOY1nE1pwQVgTsHZhP3zvA8cepjIGAllxX5WR9C%2BqsxN6SmH5ARdUzpVBNr4tlymaLp94dwn%2BVWsDBNjvI4BwWlo%2FNEmuTIIhOqPfvny; havana_lgc_exp=1749064509074; sdkSilent=1749119809768; havana_sdkSilent=1749119809768; uc1=cookie14=UoYagkBXjwoNLw%3D%3D&cookie16=UtASsssmPlP%2Ff1IHDsDaPRu%2BPw%3D%3D&pas=0&cookie21=URm48syIYB3rzvI4Dim4&existShop=false&cookie15=URm48syIIVrSKA%3D%3D; mtop_partitioned_detect=1; _m_h5_tk=ff3b1454f4810d7b22415ff6d96da5fc_1749185221405; _m_h5_tk_enc=bebd50c5a1188974e2e5b85bc78ce195; bxuab=0; isg=BNnZ8MNq1Qpor4YxCP9dp6GZ6MWzZs0YL9p_xPuOzYB_AvuUQ7Jw6EgRAMZ0umVQ; tfstk=gMknlubViXPC1ATT6AwBgYgnTnROdJwSU4B8y8Uy_Pz6V6CpA7rrq4D-90z8E40oryFyRJHoSmiJAYKQLLqrkV2-vyU-S30KVTLBNJBPr2i2RDUJAY4zS2uuNHa8zz0-4XK9HKnIA8wPrEpvHm1O-g0uYzWP73rzmu-TTh9MUh2ykEpO6GPBJ8uJ4Jiqs1rb7krPLu-MQkZ8UkrU4h4aqo1P84uyjPruV_zzzk5N7ozaz8yrzc-gVPrUU8lCkTzmUAMwWXUSswNa-Aq3troMGTWlCk7YoDzGUTz30WfqYPXPUAGO87408IsTPJUIck0Wh9wUZXu3b8ARVycZDvUKmiXij2nZM7DeVBiiEyVqL5jAEJm_44Po1EXYxJhgivcBVNoEkyc4d0IDJcVmsSZa_gfUpjnxFSkeKZaTGu00m2bh4B1Ng4xRFl--Q_17TlZgkrYmrvseSfSXjhfXOWr_vEKMj_15lTWDkhxGGaNUfkLA.'

h5 = goToken(cookie)
cookie = h5 + cookie
comment_text = '挺好的'
# 设置图片目录
image_directory = r"C:\Users\<USER>\Desktop\1"
date = '{"dinamicOpen": "True","pageType": "taobaoTotalPublishRate","orderId":"2598804843366950055","platformType": "wireless","asac": "2A23A16I9YC7QUVMJ8EH2I"}'
xapi = 'mtop.taobao.rate.component.render'
xv = '1.0'
t = str(int(time.time()))
token = cookie.split('_m_h5_tk=')[1].split('_')[0]
str1 = token + '&' + t + '&12574478&' + date
str2 = bytes(str1, encoding='utf-8')  # md5
sign = hashlib.md5(str2).hexdigest()
data2 = 'jsv=2.4.9&appKey=12574478&t=' + t + '&sign=' + sign + '&api=' + xapi + '&v=' + xv + '&jsonpIncPrefix=weexcb&ttid=10022339@ltao_android_10.39.17&data=' + str(quote(date, 'utf-8'))
url2 = 'https://h5api.m.taobao.com/h5/' + xapi + '/' + xv + '/?' + data2
head = {
    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1',
    'cookie': cookie}
proxies = {"http": "http://127.0.0.1:8887",
           "https": "http://127.0.0.1:8887"}
result = requests.get(url2, timeout=20, headers=head, stream=False, proxies=proxies,verify=False).json()
print(result)
submit_params = {
    "data": {},
    "hierarchy": result["data"]["hierarchy"],
    "linkage": result["data"]["linkage"]
}
json_data = result["data"]["data"]
tem_list = {}
order_length = 0
order = ""
# 遍历 json_data 的键值对
for k, v in json_data.items():
    if k.startswith("itemRateContainer"):
        submit_params["data"][k] = v
        order_length += 1
    elif k.startswith("rateRoot_"):
        # 修复rateRoot字段以匹配1.txt格式
        if "fields" in v:
            v["fields"]["publicShareExperiOpen"] = "false"  # 修改为false以匹配1.txt
            # 移除新增字段以匹配1.txt格式
            fields_to_remove = ["enablePublicAndShareV2", "isAnonymousOrder"]
            for field in fields_to_remove:
                if field in v["fields"]:
                    del v["fields"][field]
        submit_params["data"][k] = v
    elif k.startswith("mainOrderScore"):
        if not order:
            match = re.search(r'mainOrderScore_(\d+)_\d+_\d+', k)
            if match:
                order = match.group(1)
        tem_list[k] = v
    elif (k.startswith("text_") or k.startswith("tmallSatisficationScore_") or
          k.startswith("recordOnionShow_") or k.startswith("structRate_") or
          k.startswith("tbGnbRate_") or k.startswith("ugcPublicAndPublishSKS_")):
        tem_list[k] = v

# 遍历临时列表 tem_list 的键值对
for k, v in tem_list.items():
    if k.startswith("mainOrderScore_") or k.startswith("tmallSatisficationScore_"):
        v["fields"]["darkMode"] = '0'
        v["fields"]["darkModel"] = '0'
        v["fields"]["orderId"] = order
        v["fields"]["rateStatus"] = 0
        v["fields"]["starValue"] = 5
        if order_length == 1:
            v["fields"]["groupIndex"] = k[-1]
            v["fields"]["groupsEnable"] = '1'
        v["fields"]["rateType"] = '0' if order_length == 1 else '1'
        submit_params["data"][k] = v
    elif k.startswith("recordOnionShow_"):
        v["fields"]["trackInfo"]["exposure"]["args"]["trackId"] = order
        if order_length == 1:
            v["fields"]["groupIndex"] = '0'
            v["fields"]["groupsEnable"] = '1'

        # 图片处理逻辑
        if image_directory:
            # 扫描图片目录
            image_map = scan_image_directory(image_directory)

            # 从API响应中提取商品ID
            goods_id = extract_goods_id_from_api_response(json_data)

            if goods_id and goods_id in image_map:
                # 找到匹配的图片
                image_files = image_map[goods_id]

                # 提取卖家和订单信息用于图片上传
                seller_order_info = {'bizOrderId': order,'sellerNumId': str(json_data).split('uploaded/i4/')[1].split('/')[0]}
                # 创建图片数据列表

                images_data = []
                for image_file in image_files:
                    # 上传图片并获取URL（使用真实的上传函数）
                    image_url = upload_image_to_platform(image_file, cookie, seller_order_info)
                    if image_url:
                        # 为每张图片生成唯一的pub_session
                        pub_session = str(uuid.uuid4())
                        images_data.append(create_image_data(image_url, goods_id, pub_session))
                    else:
                        print(f"图片 {image_file} 上传失败，跳过此图片")
                print(images_data)
                input(111111111111111)
                if images_data:
                    v["fields"]['images'] = images_data

        submit_params["data"][k] = v
    elif k.startswith("structRate_"):
        v["fields"]["content"] = comment_text
        tags = v["fields"].get("tags")
        if tags:
            v["fields"]["trackInfo"]["exposure"]["args"]["struct_tag_title"] = tags[0]["title"]
            v["fields"]["trackInfo"]["exposure"]["args"]["struct_tag_id"] = tags[0]["id"]
        if order_length == 1:
            v["fields"]["groupIndex"] = '0'
            v["fields"]["groupsEnable"] = '1'
        v["fields"]["tags"] = []
        submit_params["data"][k] = v
    elif k.startswith("tbGnbRate_"):
        v["fields"]["darkMode"] = '0'
        v["fields"]["darkModel"] = '0'
        v["fields"]["trackInfo"]["exposure"]["args"]["trackId"] = order
        v["fields"]["orderId"] = order
        v["fields"]["rateStatus"] = 0
        v["fields"]["selectedValue"] = "1"
        v["fields"]["rateType"] = '0' if order_length == 1 else '1'
        if order_length == 1:
            v["fields"]["groupIndex"] = '1'
            v["fields"]["groupsEnable"] = '1'
        submit_params["data"][k] = v
    elif k.startswith("ugcPublicAndPublishSKS_"):
        v["fields"]["isAnonymous"] = "true"  # 修改为字符串格式以匹配1.txt
        if order_length == 1:
            v["fields"]["groupIndex"] = '0'
            v["fields"]["groupsEnable"] = '1'
        v["fields"]["darkMode"] = '0'
        v["fields"]["darkModel"] = '0'
        v["fields"]["orderId"] = order
        v["fields"]["rateStatus"] = 0
        v["fields"]["rateType"] = '0' if order_length == 1 else '1'
        # 移除新增的权限相关字段以匹配1.txt格式
        fields_to_remove = ["enablePublicAndShareV2", "isAnonymousOrder", "permissionDetail",
                           "permissionDetailUrl", "permissionIndex", "permissionStatus", "permissionTipMap"]
        for field in fields_to_remove:
            if field in v["fields"]:
                del v["fields"][field]
        submit_params["data"][k] = v
    elif k.startswith("text_"):
        v["fields"]["data"]["text"] = comment_text
        submit_params["data"][k] = v
    else:
        submit_params["data"][k] = v

# 将submit_params序列化为字符串以匹配1.txt格式
submit_params_str = json.dumps(submit_params, separators=(',', ':'))
final_params = {"submit": submit_params_str, "asac": "2A23A16I9YC7QUVMJ8EH2I"}
get_rate = to_comment(final_params, cookie)

