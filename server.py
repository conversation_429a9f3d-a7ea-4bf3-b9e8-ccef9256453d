import os
import json
import threading
from flask import Flask, jsonify, request, render_template, send_from_directory
from werkzeug.utils import secure_filename
from config_manager import config_manager

class ApiServer:
    """API服务器，处理前端请求"""

    def __init__(self, chrome_manager, order_manager, app=None):
        if app:
            self.app = app
        else:
            self.app = Flask(__name__,
                          static_folder=os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static'),
                          template_folder=os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates'))
            self.app.config['JSON_AS_ASCII'] = False

        self.chrome_manager = chrome_manager
        self.order_manager = order_manager

        # 注册路由
        self._register_routes()

        # 启动线程
        self.thread = None
        self.running = False

    def _register_routes(self):
        """注册API路由"""

        # 主页
        @self.app.route('/')
        def index():
            return render_template('index.html')

        # 下单页面
        @self.app.route('/order')
        def order_page():
            return render_template('order.html')

        # 确认收货页面
        @self.app.route('/confirm')
        def confirm_page():
            return render_template('confirm.html')

        # Cookie获取页面
        @self.app.route('/cookie')
        def cookie_page():
            return render_template('cookie.html')



        # 下载路由
        @self.app.route('/downloads/<path:filename>')
        def download_file(filename):
            # 获取downloads目录的绝对路径
            downloads_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'downloads')
            return send_from_directory(downloads_dir, filename, as_attachment=True)

        # 获取Chrome用户列表
        @self.app.route('/api/profiles', methods=['GET'])
        def get_profiles():
            profiles = self.chrome_manager.get_profiles()
            return jsonify({'success': True, 'profiles': profiles})

        @self.app.route('/api/profiles/refresh', methods=['POST'])
        def refresh_profiles():
            self.chrome_manager.refresh_profiles()
            profiles = self.chrome_manager.get_profiles()
            return jsonify({'success': True, 'profiles': profiles})

        # 更新用户状态
        @self.app.route('/api/profile/status', methods=['POST'])
        def update_profile_status_server():
            data = request.json
            profile_id = data.get('profile_id')
            status = data.get('status')

            if not profile_id or not status:
                return jsonify({'success': False, 'message': '缺少必要参数'})

            success = self.chrome_manager.update_profile_status(profile_id, status)
            return jsonify({'success': success})

        # 获取配置信息
        @self.app.route('/api/config', methods=['GET'])
        def get_config():
            try:
                # 使用集中配置管理器获取配置
                config = config_manager.get_config()

                # 如果配置为空，使用订单管理器的默认值
                if not config:
                    config = {
                        "max_threads": self.order_manager.max_threads,
                        "retry_count": self.order_manager.retry_count,
                        "timeout": self.order_manager.timeout
                    }
                return jsonify({'success': True, 'config': config})
            except Exception as e:
                print(f"获取配置失败: {e}")
                import traceback
                traceback.print_exc()
                # 使用订单管理器的默认值
                config = {
                    "max_threads": self.order_manager.max_threads,
                    "retry_count": self.order_manager.retry_count,
                    "timeout": self.order_manager.timeout
                }
                return jsonify({'success': True, 'config': config})

        @self.app.route('/api/config', methods=['POST'])
        def save_config():
            try:
                data = request.get_json()

                # 使用集中配置管理器获取当前配置
                config = config_manager.get_config()

                # 更新系统配置
                if 'max_threads' in data:
                    self.order_manager.max_threads = int(data['max_threads'])
                    config['max_concurrent_browsers'] = int(data['max_threads'])
                if 'retry_count' in data:
                    self.order_manager.retry_count = int(data['retry_count'])
                    config['retry_count'] = int(data['retry_count'])
                if 'timeout' in data:
                    self.order_manager.timeout = int(data['timeout'])
                    config['timeout'] = int(data['timeout'])

                # 更新确认收货相关配置
                if 'start_date' in data:
                    config['start_date'] = data['start_date']
                if 'end_date' in data:
                    config['end_date'] = data['end_date']
                if 'interval' in data:
                    config['interval'] = data['interval']
                if 'auto_review_enabled' in data:
                    config['auto_review_enabled'] = data['auto_review_enabled']
                if 'review_text' in data:
                    config['review_text'] = data['review_text']

                # 更新用户支付密码
                if 'profiles' in data and isinstance(data['profiles'], dict):
                    # 确保config中有profiles字段
                    if 'profiles' not in config:
                        config['profiles'] = {}

                    # 更新每个用户的支付密码
                    for profile_id, profile_data in data['profiles'].items():
                        # 获取现有的profile配置
                        profile = config_manager.get_profile(profile_id)

                        if 'pay_password' in profile_data:
                            profile['pay_password'] = profile_data['pay_password']

                        # 更新到配置管理器
                        config_manager.update_profile(profile_id, profile)

                # 更新配置
                config_manager.update_config(config)

                # 保存配置
                success = config_manager.save_config()
                if success:
                    return jsonify({'success': True, 'message': '配置保存成功'})
                else:
                    return jsonify({'success': False, 'message': '配置保存失败'})
            except Exception as e:
                import traceback
                print(f"保存配置失败: {str(e)}")
                print(traceback.format_exc())
                return jsonify({'success': False, 'message': str(e)})

        # 启动下单任务
        @self.app.route('/api/order/start', methods=['POST'])
        def start_order():
            try:
                data = request.get_json()
                print("收到下单请求数据:", data)  # 调试信息

                profile_ids = data.get('profile_ids', [])
                order_urls = data.get('order_urls', [])
                shuffle = data.get('shuffle', False)
                multi_thread = data.get('multi_thread', False)  # 获取多线程模式标志

                # 确保profile_ids是列表
                if not isinstance(profile_ids, list):
                    profile_ids = [profile_ids]

                # 确保列表中的ID是字符串或整数类型
                profile_ids = [str(pid) for pid in profile_ids]

                # 校验参数
                if not profile_ids:
                    return jsonify({'success': False, 'message': '请选择至少一个Chrome用户'})
                if not order_urls:
                    return jsonify({'success': False, 'message': '请输入至少一个订单链接'})

                print(f"准备启动下单任务，用户ID: {profile_ids}, 链接数: {len(order_urls)}, 随机排序: {shuffle}, 多线程: {multi_thread}")

                # 如果需要随机顺序，则打乱订单链接
                if shuffle:
                    import random
                    random.shuffle(order_urls)

                # 启动下单任务
                result = self.order_manager.start_order_task(profile_ids, order_urls, multi_thread)
                return jsonify(result)
            except Exception as e:
                import traceback
                print("启动下单任务出错:", str(e))
                print(traceback.format_exc())
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/order/stop', methods=['POST'])
        def stop_order():
            try:
                result = self.order_manager.stop_all_tasks()
                return jsonify(result)
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/order/status', methods=['GET'])
        def get_order_status():
            try:
                task_status = self.order_manager.get_task_status()

                # task_status现在已经包含了完整的任务状态信息，直接返回
                return jsonify({'success': True, 'data': task_status})
            except Exception as e:
                import traceback
                print(f"获取任务状态出错: {str(e)}")
                print(traceback.format_exc())
                return jsonify({'success': False, 'message': str(e)})

        # 获取特定用户的任务状态
        @self.app.route('/api/profile/<profile_id>/status', methods=['GET'])
        def get_profile_status(profile_id):
            try:
                profile = self.chrome_manager.get_profile_by_id(profile_id)
                if not profile:
                    return jsonify({'success': False, 'message': '用户不存在'})

                profile_status = {
                    'id': profile.get('id'),
                    'name': profile.get('name'),
                    'status': profile.get('status', '就绪'),
                    'status_data': profile.get('status_data', {}),
                    'last_updated': profile.get('last_updated', '')
                }

                return jsonify({'success': True, 'data': profile_status})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        # 启动确认收货任务
        @self.app.route('/api/receipt/start', methods=['POST'])
        def start_receipt():
            try:
                data = request.get_json()
                profile_ids = data.get('profile_ids', [])
                order_ids = data.get('order_ids', [])

                # 校验参数
                if not profile_ids:
                    return jsonify({'success': False, 'message': '请选择至少一个Chrome用户'})
                if not order_ids:
                    return jsonify({'success': False, 'message': '请选择至少一个订单'})

                # 启动确认收货任务
                result = self.order_manager.start_receipt_task(profile_ids, order_ids)
                return jsonify(result)
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/receipt/auto/start', methods=['POST'])
        def start_auto_receipt():
            try:
                data = request.get_json()
                profile_ids = data.get('profile_ids', [])

                # 校验参数
                if not profile_ids:
                    return jsonify({'success': False, 'message': '请选择至少一个Chrome用户'})

                # 启动自动确认收货任务
                result = self.order_manager.start_auto_receipt_task(profile_ids)
                return jsonify(result)
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/start_confirm', methods=['POST'])
        def start_confirm_api():
            try:
                data = request.get_json()
                profile_ids = data.get('profiles', [])
                profile_passwords = data.get('profile_passwords', {})

                # 打印请求数据，帮助调试
                print(f"收到确认收货请求: profiles={profile_ids}, 密码数量={len(profile_passwords)}")

                # 校验参数
                if not profile_ids:
                    print("错误: 未选择Chrome用户")
                    return jsonify({'success': False, 'message': '请选择至少一个Chrome用户'})

                # 启动自动确认收货任务
                print("调用order_manager.start_auto_receipt_task...")
                result = self.order_manager.start_auto_receipt_task(profile_ids, profile_passwords)
                print(f"任务启动结果: {result}")
                return jsonify(result)
            except Exception as e:
                import traceback
                print(f"启动确认收货任务失败: {str(e)}")
                print(traceback.format_exc())
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/stop_confirm', methods=['POST'])
        def stop_confirm_api():
            try:
                result = self.order_manager.stop_receipt_task()
                return jsonify(result)
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        # 订单管理API
        @self.app.route('/api/orders', methods=['GET'])
        def get_orders():
            try:
                orders = self.order_manager.get_all_orders()
                return jsonify({'success': True, 'orders': orders})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/orders/<order_id>', methods=['GET'])
        def get_order_detail(order_id):
            try:
                order = self.order_manager.get_order_detail(order_id)
                if order:
                    return jsonify({'success': True, 'order': order})
                else:
                    return jsonify({'success': False, 'message': '订单不存在'})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/orders/<order_id>', methods=['DELETE'])
        def delete_order(order_id):
            try:
                success = self.order_manager.delete_order(order_id)
                if success:
                    return jsonify({'success': True})
                else:
                    return jsonify({'success': False, 'message': '删除订单失败'})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/orders/clear', methods=['POST'])
        def clear_orders():
            try:
                success = self.order_manager.clear_orders()
                if success:
                    return jsonify({'success': True})
                else:
                    return jsonify({'success': False, 'message': '清空订单失败'})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})



    def start(self, host='127.0.0.1', port=5000, debug=False):
        """启动Flask服务器"""
        if self.running:
            return False

        self.running = True

        # 非调试模式下使用线程运行
        if not debug:
            self.thread = threading.Thread(target=self._run_server, args=(host, port))
            self.thread.daemon = True
            self.thread.start()
            return True
        else:
            # 调试模式下直接运行（阻塞）
            self.app.run(host=host, port=port, debug=True)
            return True

    def _run_server(self, host, port):
        """在线程中运行Flask服务器"""
        self.app.run(host=host, port=port)

    def stop(self):
        """停止Flask服务器"""
        self.running = False
        return True

def create_server(chrome_manager, order_manager, app=None):
    """
    创建API服务器实例

    Args:
        chrome_manager: Chrome用户管理器实例
        order_manager: 订单管理器实例
        app: 可选的Flask应用实例，如果提供则使用该实例，否则创建新的Flask实例

    Returns:
        ApiServer: API服务器实例
    """
    return ApiServer(chrome_manager, order_manager, app)