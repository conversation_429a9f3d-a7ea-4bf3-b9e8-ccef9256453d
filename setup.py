#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
打包脚本，使用PyInstaller将应用程序打包为可执行文件
"""

import os
import sys
import shutil
import subprocess

def main():
    """主函数"""
    print("开始打包应用程序...")
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 创建dist目录（如果不存在）
    dist_dir = os.path.join(current_dir, "dist")
    if not os.path.exists(dist_dir):
        os.makedirs(dist_dir)
    
    # 清空dist目录
    for item in os.listdir(dist_dir):
        item_path = os.path.join(dist_dir, item)
        if os.path.isfile(item_path):
            os.remove(item_path)
        elif os.path.isdir(item_path):
            shutil.rmtree(item_path)
    
    # 构建PyInstaller命令
    pyinstaller_cmd = [
        "pyinstaller",
        "--noconfirm",
        "--clean",
        "--name=taobao_order_system",
        "--icon=static/favicon.ico",
        "--add-data=static;static",
        "--add-data=templates;templates",
        "--add-data=confirm_receipt.py;.",  # 添加确认收货脚本
        "--hidden-import=DrissionPage",
        "--hidden-import=webview",
        "--hidden-import=flask",
        "--hidden-import=psutil",
        "app.py"
    ]
    
    # 执行PyInstaller命令
    print(f"执行命令: {' '.join(pyinstaller_cmd)}")
    subprocess.run(pyinstaller_cmd, cwd=current_dir)
    
    # 复制config.json到dist目录（如果存在）
    config_path = os.path.join(current_dir, "config.json")
    if os.path.exists(config_path):
        shutil.copy(config_path, os.path.join(dist_dir, "taobao_order_system", "config.json"))
        print(f"已复制config.json到dist目录")
    
    print("打包完成！")

if __name__ == "__main__":
    main()
