#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Cookie提取测试
"""

import os
import sys
import subprocess
import time

# 添加当前目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

def kill_chrome():
    """关闭Chrome进程"""
    try:
        print("正在关闭Chrome进程...")
        subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'], 
                      capture_output=True, check=False)
        time.sleep(2)
        print("Chrome进程已关闭")
        return True
    except Exception as e:
        print(f"关闭Chrome失败: {e}")
        return False

def test_cookie_extraction():
    """测试Cookie提取"""
    try:
        print("导入cookie_extractor...")
        from cookie_extractor import get_chrome_cookies
        print("✓ 成功导入")
        
        print("正在提取淘宝Cookie...")
        cookies = get_chrome_cookies("taobao.com")
        print(f"✓ 成功提取到 {len(cookies)} 个Cookie")
        
        if cookies:
            print("Cookie示例:")
            for i, cookie in enumerate(cookies[:2]):
                print(f"  {i+1}. {cookie['name']}: {cookie['value'][:50]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ Cookie提取失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chrome_manager():
    """测试ChromeManager"""
    try:
        print("导入ChromeManager...")
        from chrome_manager import ChromeManager
        print("✓ 成功导入")
        
        print("创建ChromeManager实例...")
        manager = ChromeManager()
        print("✓ 成功创建")
        
        print("测试自动Cookie提取...")
        success = manager._auto_extract_cookies()
        
        if success:
            print("✓ 自动Cookie提取成功")
        else:
            print("✗ 自动Cookie提取失败")
            
        return success
        
    except Exception as e:
        print(f"✗ ChromeManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 50)
    print("简单Cookie提取测试")
    print("=" * 50)
    
    # 1. 关闭Chrome
    print("\n1. 关闭Chrome进程")
    kill_chrome()
    
    # 2. 测试Cookie提取
    print("\n2. 测试Cookie提取")
    cookie_result = test_cookie_extraction()
    
    # 3. 测试ChromeManager
    print("\n3. 测试ChromeManager")
    manager_result = test_chrome_manager()
    
    # 结果
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"Cookie提取: {'✓ 成功' if cookie_result else '✗ 失败'}")
    print(f"ChromeManager: {'✓ 成功' if manager_result else '✗ 失败'}")
    
    if cookie_result and manager_result:
        print("\n🎉 所有测试通过！")
        return True
    else:
        print("\n⚠️ 部分测试失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试出错: {e}")
        sys.exit(1)
