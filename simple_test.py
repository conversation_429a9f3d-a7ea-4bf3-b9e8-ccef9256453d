import json
import uuid

# 测试修复后的函数
def test_create_image_data():
    # 模拟函数
    def create_image_data(image_url, goods_id, pub_session=None):
        if not pub_session:
            pub_session = str(uuid.uuid4())

        # URL转义修复 - 让JSON序列化自动处理转义
        image_url = image_url.replace(" ", "")

        # 构建配置
        music_frame_config_str = json.dumps({
            "type": "music",
            "frameResolutionSize": 256,
            "frameCount": 2,
            "frameInterval": 0.5,
            "frameQuality": 50,
            "frameDecodeTimeout": 1000,
            "frameUploadTimeout": 1500
        }, separators=(',', ':'))

        topic_frame_config_str = json.dumps({
            "type": "topic",
            "frameResolutionSize": 256,
            "frameCount": 5,
            "frameInterval": 0.2,
            "frameQuality": 70,
            "frameDecodeTimeout": 5000,
            "frameUploadTimeout": 4000
        }, separators=(',', ':'))

        ab_test_info_str = json.dumps({
            "musicFrameConfig": music_frame_config_str,
            "topicFrameConfig": topic_frame_config_str
        }, separators=(',', ':'))

        stat_info = {
            "ab_test_info": ab_test_info_str,
            "camera_rotation": 0,
            "filter": [{}],
            "fun_id": {},
            "goods_id": str(goods_id),
            "is_hq_record": False,
            "itemsticker_items": [],
            "pub_session": pub_session,
            "source": "user_record",
            "additionalInfo": {
                "imageSource": "1",
                "containExif": False,
                "isScreenshot": False,
                "OS": "Android"
            }
        }

        # 双重序列化修复转义层级
        stat_info_json = json.dumps(stat_info, separators=(',', ':'))
        final_stat_info = json.dumps(stat_info_json, separators=(',', ':'))

        return {
            "fileSourceTag": "taobao_camera",
            "height": 1440,
            "statInfo": final_stat_info,
            "url": image_url,
            "width": 1080
        }

    # 测试
    test_url = "https://img.alicdn.com/imgextra/i4/O1CN01vxUpjm1qV3QLI59Ql_!!4611686018427387244-0-rate.jpg"
    goods_id = "821648750509"
    
    result = create_image_data(test_url, goods_id)
    
    print("修复后的结果:")
    print(f"URL: {result['url']}")
    print(f"statInfo前100字符: {result['statInfo'][:100]}...")
    
    # 检查转义层级
    stat_info_content = result['statInfo']
    print(f"\n转义层级检查:")
    print(f"包含\\\\\\\\\\\\\\\"type\\\\\\\\\\\\\\\"模式: {'\\\\\\\\\\\\\\\"type\\\\\\\\\\\\\\\"' in stat_info_content}")
    
    return result

if __name__ == "__main__":
    test_create_image_data()
