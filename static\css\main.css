/* 全局样式 */
body {
    background-color: #f5f5f5;
    font-family: 'Microsoft YaHei', sans-serif;
}

/* 侧边栏样式 */
.sidebar {
    min-height: 100vh;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-sticky {
    position: sticky;
    top: 0;
    padding-top: 1rem;
}

.nav-link {
    color: #f8f9fa;
    padding: 0.75rem 1rem;
    transition: all 0.3s;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.nav-link.active {
    background-color: #007bff;
    color: #fff;
}

.nav-link i {
    margin-right: 0.5rem;
}

/* 卡片样式 */
.card {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: none;
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 0.75rem 1.25rem;
    font-weight: 500;
}

.card-body {
    padding: 1.25rem;
}

/* 表格样式 */
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.table td, .table th {
    padding: 0.75rem;
    vertical-align: middle;
}

/* 状态样式 */
.status-normal {
    color: #28a745;
}

.status-error {
    color: #dc3545;
}

.status-processing {
    color: #007bff;
    font-weight: bold;
}

.status-info {
    font-size: 1.1rem;
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    transition: all 0.3s;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 进度条样式 */
.progress {
    height: 25px;
    border-radius: 4px;
}

/* 消息通知样式 */
#notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
}

/* 表单元素样式 */
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    border-color: #80bdff;
} 