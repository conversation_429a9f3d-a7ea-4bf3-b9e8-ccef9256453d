/**
 * 智能聊天页面JavaScript
 * 处理聊天交互、任务生成和管理
 */

// 全局变量
let profiles = [];
let selectedProfiles = [];
let chatTasks = [];
let isProcessing = false;

// DOM加载完成后执行
$(document).ready(function() {
    console.log('聊天页面初始化');

    // 初始化页面
    initChatPage();

    // 加载Chrome用户列表
    loadProfiles();

    // 加载现有任务
    loadChatTasks();

    // 绑定事件
    bindEvents();
});

/**
 * 初始化聊天页面
 */
function initChatPage() {
    // 设置消息输入框自动调整高度
    $('#messageInput').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // 回车发送消息（Shift+Enter换行）
    $('#messageInput').keydown(function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 发送消息按钮
    $('#sendBtn').click(sendMessage);
    
    // 清空对话按钮
    $('#clearChatBtn').click(clearChat);
    
    // 刷新用户列表按钮
    $('#refreshProfilesBtn').click(function() {
        loadProfiles(true);
    });
    
    // 全选用户复选框
    $('#checkAllProfiles').change(function() {
        const isChecked = $(this).is(':checked');
        $('.profile-checkbox').prop('checked', isChecked);
        updateSelectedProfiles();
    });
    
    // 快速选择按钮
    $('.quick-select-btn').click(function() {
        const type = $(this).data('type');
        quickSelectProfiles(type);
    });
    
    // 快速选择输入框
    $('#quickSelectBtn').click(function() {
        const input = $('#quickSelectInput').val().trim();
        if (input) {
            quickSelectByRange(input);
        }
    });
    
    $('#quickSelectInput').keypress(function(e) {
        if (e.which === 13) {
            $('#quickSelectBtn').click();
        }
    });
    
    // 执行任务按钮
    $('#executeTasksBtn').click(executeTasks);
}

/**
 * 发送消息
 */
function sendMessage() {
    const message = $('#messageInput').val().trim();
    if (!message || isProcessing) return;
    
    // 添加用户消息到聊天界面
    addMessage('user', message);
    
    // 清空输入框
    $('#messageInput').val('').css('height', 'auto');
    
    // 处理消息
    processMessage(message);
}

/**
 * 添加消息到聊天界面
 */
function addMessage(sender, content) {
    const messageHtml = `
        <div class="message ${sender}">
            <div class="message-bubble">
                ${content.replace(/\n/g, '<br>')}
            </div>
        </div>
    `;
    
    $('#chatContainer').append(messageHtml);
    
    // 滚动到底部
    const container = document.getElementById('chatContainer');
    container.scrollTop = container.scrollHeight;
}

/**
 * 处理用户消息
 */
function processMessage(message) {
    isProcessing = true;
    
    // 显示处理中状态
    addMessage('assistant', '<i class="ri-loading-line animate-spin"></i> 正在处理您的请求...');
    
    setTimeout(() => {
        // 移除处理中消息
        $('#chatContainer .message:last').remove();
        
        // 检测是否包含商品链接
        const urls = extractUrls(message);
        
        if (urls.length > 0) {
            handleProductUrls(urls, message);
        } else {
            handleGeneralMessage(message);
        }
        
        isProcessing = false;
    }, 1000);
}

/**
 * 提取消息中的URL
 */
function extractUrls(text) {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const urls = text.match(urlRegex) || [];
    
    // 过滤淘宝相关链接
    return urls.filter(url => 
        url.includes('taobao.com') || 
        url.includes('tmall.com') || 
        url.includes('item.htm') ||
        url.includes('detail.tmall.com')
    );
}

/**
 * 处理商品链接
 */
function handleProductUrls(urls, originalMessage) {
    if (selectedProfiles.length === 0) {
        addMessage('assistant', '❌ 请先选择Chrome用户，然后再创建任务。');
        return;
    }

    // 显示处理中状态
    addMessage('assistant', '<i class="ri-loading-line animate-spin"></i> 正在创建下单任务...');

    // 调用后端API创建任务
    $.ajax({
        url: '/api/chat/create_task',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            type: 'order',
            urls: urls,
            profile_ids: selectedProfiles.map(p => p.id),
            message: originalMessage
        }),
        success: function(response) {
            // 移除处理中消息
            $('#chatContainer .message:last').remove();

            if (response.success) {
                const task = response.task;

                // 添加到本地任务列表
                chatTasks.push(task);

                // 更新任务显示
                updateTasksList();

                // 回复用户
                const responseMsg = `
                    ✅ 已识别到 ${urls.length} 个商品链接，已生成下单任务！<br><br>
                    📋 <strong>任务详情：</strong><br>
                    • 任务ID: ${task.id}<br>
                    • 商品数量: ${urls.length} 个<br>
                    • 选中用户: ${selectedProfiles.length} 个<br><br>
                    ${urls.map((url, index) => `${index + 1}. ${url.substring(0, 80)}...`).join('<br>')}<br><br>
                    💡 您可以在右侧任务管理区域查看和执行此任务。
                `;

                addMessage('assistant', responseMsg);
            } else {
                addMessage('assistant', `❌ 创建任务失败: ${response.message}`);
            }
        },
        error: function(xhr, status, error) {
            // 移除处理中消息
            $('#chatContainer .message:last').remove();
            addMessage('assistant', `❌ 创建任务失败: ${error}`);
        }
    });
}

/**
 * 处理一般消息
 */
function handleGeneralMessage(message) {
    let response = '';
    
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('帮助') || lowerMessage.includes('help')) {
        response = `
            📚 <strong>系统功能说明：</strong><br><br>
            🛒 <strong>商品下单：</strong><br>
            • 粘贴淘宝/天猫商品链接，我会自动生成下单任务<br>
            • 支持批量链接处理<br><br>
            ✅ <strong>确认收货：</strong><br>
            • 批量确认订单收货<br>
            • 支持自动评价功能<br><br>
            📊 <strong>订单导出：</strong><br>
            • 导出订单数据为CSV格式<br>
            • 支持时间范围筛选<br><br>
            🔑 <strong>Cookie管理：</strong><br>
            • 自动获取Chrome用户Cookie<br>
            • 管理多账号登录状态
        `;
    } else if (lowerMessage.includes('用户') || lowerMessage.includes('账号')) {
        response = `
            👥 <strong>Chrome用户管理：</strong><br><br>
            当前系统检测到 ${profiles.length} 个Chrome用户配置<br>
            已选择 ${selectedProfiles.length} 个用户<br><br>
            💡 <strong>快速选择技巧：</strong><br>
            • 点击"正常"按钮选择状态正常的用户<br>
            • 使用"1-10"格式快速选择范围<br>
            • 支持"前5个"、"后5个"快速选择
        `;
    } else if (lowerMessage.includes('任务') || lowerMessage.includes('task')) {
        response = `
            📋 <strong>任务管理：</strong><br><br>
            当前任务数量: ${chatTasks.length} 个<br><br>
            🎯 <strong>任务类型：</strong><br>
            • 下单任务：自动执行商品下单<br>
            • 确认收货任务：批量确认订单<br><br>
            ⚡ <strong>执行任务：</strong><br>
            选择任务后点击"执行选中任务"按钮即可开始执行
        `;
    } else {
        response = `
            🤖 我是您的AI助手，专门帮助您管理淘宝订单。<br><br>
            💡 <strong>您可以：</strong><br>
            • 直接粘贴商品链接，我会生成下单任务<br>
            • 询问"帮助"了解系统功能<br>
            • 询问"用户"了解账号管理<br>
            • 询问"任务"了解任务执行<br><br>
            还有什么我可以帮助您的吗？
        `;
    }
    
    addMessage('assistant', response);
}

/**
 * 清空对话
 */
function clearChat() {
    $('#chatContainer').empty();
    
    // 添加欢迎消息
    addMessage('assistant', `
        👋 对话已清空！我是您的AI助手。<br><br>
        您可以：<br>
        • 粘贴商品链接，我会帮您生成下单任务<br>
        • 询问系统功能和使用方法<br>
        • 查看和管理您的任务<br><br>
        请告诉我您需要什么帮助？
    `);
}

/**
 * 加载Chrome用户列表
 */
function loadProfiles(forceRefresh = false) {
    console.log('开始加载Chrome用户列表...');

    // 显示加载状态
    $('#profilesList').html(`
        <div class="text-center text-gray-500 py-4">
            <i class="ri-loading-line animate-spin text-xl mb-2"></i>
            <div>正在加载用户列表...</div>
        </div>
    `);

    // 使用HTTP API加载用户列表
    $.ajax({
        url: '/api/profiles',
        method: 'GET',
        timeout: 10000,
        success: function(data) {
            console.log('用户列表API响应:', data);
            console.log('响应数据类型:', typeof data);
            console.log('data.profiles存在:', !!data.profiles);
            console.log('data.success:', data.success);

            if (data.profiles && Array.isArray(data.profiles)) {
                profiles = data.profiles;
                console.log('成功加载', profiles.length, '个Chrome用户');
                console.log('第一个用户示例:', profiles[0]);
                renderProfiles();
                updateProfileCount();
            } else {
                console.error('加载用户列表失败 - 数据格式错误:', data);
                showNotification('加载用户列表失败: 数据格式错误', 'error');
                renderProfilesError('数据格式错误');
            }
        },
        error: function(xhr, status, error) {
            console.error('加载用户列表失败:', status, error);
            showNotification('加载用户列表失败: ' + error, 'error');
            renderProfilesError('网络错误: ' + error);
        }
    });
}

/**
 * 渲染用户列表错误状态
 */
function renderProfilesError(errorMessage) {
    $('#profilesList').html(`
        <div class="text-center text-red-500 py-4">
            <i class="ri-error-warning-line text-xl mb-2"></i>
            <div class="text-sm">${errorMessage}</div>
            <button onclick="loadProfiles(true)" class="mt-2 text-xs bg-red-100 hover:bg-red-200 px-2 py-1 rounded">
                重试
            </button>
        </div>
    `);
}

/**
 * 渲染用户列表
 */
function renderProfiles() {
    const container = $('#profilesList');

    console.log('开始渲染用户列表，用户数量:', profiles.length);

    if (profiles.length === 0) {
        container.html(`
            <div class="text-center text-gray-500 py-4">
                <i class="ri-user-line text-xl mb-2"></i>
                <div class="text-sm">未找到Chrome用户</div>
            </div>
        `);
        return;
    }

    const html = profiles.map((profile, index) => {
        const statusClass = getStatusClass(profile.status);
        const statusText = getStatusText(profile.status);

        // 获取用户显示名称
        const displayName = profile.name || profile.chrome_user_name || `用户${index + 1}`;
        const profileDir = profile.profile_dir || 'Default';

        // 安全地序列化profile对象
        const profileData = {
            id: profile.id,
            name: displayName,
            chrome_user_name: profile.chrome_user_name,
            profile_dir: profileDir,
            status: profile.status || '未知'
        };

        return `
            <div class="flex items-center p-2 border border-gray-200 rounded hover:border-primary/50 transition-colors">
                <input type="checkbox" class="profile-checkbox mr-2" value="${profile.id}" data-profile='${JSON.stringify(profileData)}'>
                <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900 truncate">${displayName}</div>
                    <div class="text-xs text-gray-500">${profileDir}</div>
                </div>
                <span class="text-xs px-2 py-1 rounded ${statusClass}">${statusText}</span>
            </div>
        `;
    }).join('');

    container.html(html);

    console.log('用户列表渲染完成');

    // 绑定复选框事件
    $('.profile-checkbox').change(updateSelectedProfiles);
}

/**
 * 更新选中的用户
 */
function updateSelectedProfiles() {
    selectedProfiles = [];
    $('.profile-checkbox:checked').each(function() {
        const profileData = $(this).data('profile');
        selectedProfiles.push(profileData);
    });
    
    $('#selectedCount').text(`已选择: ${selectedProfiles.length}`);
    
    // 更新全选复选框状态
    const totalCheckboxes = $('.profile-checkbox').length;
    const checkedCheckboxes = $('.profile-checkbox:checked').length;
    
    if (checkedCheckboxes === 0) {
        $('#checkAllProfiles').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#checkAllProfiles').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#checkAllProfiles').prop('indeterminate', true);
    }
}

/**
 * 快速选择用户
 */
function quickSelectProfiles(type) {
    $('.profile-checkbox').prop('checked', false);
    
    switch (type) {
        case 'normal':
            $('.profile-checkbox').each(function() {
                const profile = $(this).data('profile');
                if (profile.status === '正常') {
                    $(this).prop('checked', true);
                }
            });
            break;
        case 'first5':
            $('.profile-checkbox').slice(0, 5).prop('checked', true);
            break;
        case 'last5':
            $('.profile-checkbox').slice(-5).prop('checked', true);
            break;
    }
    
    updateSelectedProfiles();
}

/**
 * 按范围快速选择
 */
function quickSelectByRange(input) {
    const match = input.match(/^(\d+)-(\d+)$/);
    if (!match) {
        showNotification('请输入正确的范围格式，如: 1-10', 'warning');
        return;
    }
    
    const start = parseInt(match[1]) - 1; // 转为0基索引
    const end = parseInt(match[2]);
    
    $('.profile-checkbox').prop('checked', false);
    $('.profile-checkbox').slice(start, end).prop('checked', true);
    
    updateSelectedProfiles();
}

/**
 * 更新任务列表显示
 */
function updateTasksList() {
    const container = $('#tasksList');

    if (chatTasks.length === 0) {
        container.html(`
            <div class="text-center text-gray-500 py-4">
                <i class="ri-task-line text-xl mb-2"></i>
                <div class="text-sm">暂无任务</div>
            </div>
        `);
        $('#executeTasksBtn').prop('disabled', true);
        return;
    }

    const html = chatTasks.map(task => {
        const statusClass = getTaskStatusClass(task.status);
        const statusText = getTaskStatusText(task.status);

        return `
            <div class="task-card" data-task-id="${task.id}">
                <div class="flex items-center mb-2">
                    <input type="checkbox" class="task-checkbox mr-2" value="${task.id}" ${task.status === 'running' ? 'disabled' : ''}>
                    <div class="flex-1">
                        <div class="text-sm font-medium">${task.type === 'order' ? '下单任务' : '其他任务'}</div>
                        <div class="text-xs text-gray-500">${task.id.substring(0, 20)}...</div>
                    </div>
                    <div class="flex items-center space-x-1">
                        <span class="text-xs px-2 py-1 rounded ${statusClass}">${statusText}</span>
                        <button class="delete-task-btn text-xs text-red-500 hover:text-red-700 p-1" data-task-id="${task.id}" title="删除任务">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </div>
                </div>
                <div class="text-xs text-gray-600">
                    商品数量: ${task.urls ? task.urls.length : 0} 个<br>
                    用户数量: ${task.profile_ids ? task.profile_ids.length : (task.profiles ? task.profiles.length : 0)} 个
                </div>
            </div>
        `;
    }).join('');

    container.html(html);

    // 绑定任务复选框事件
    $('.task-checkbox').change(function() {
        const checkedTasks = $('.task-checkbox:checked').length;
        $('#executeTasksBtn').prop('disabled', checkedTasks === 0);
    });

    // 绑定删除任务事件
    $('.delete-task-btn').click(function() {
        const taskId = $(this).data('task-id');
        deleteTask(taskId);
    });
}

/**
 * 获取任务状态样式类
 */
function getTaskStatusClass(status) {
    switch (status) {
        case 'pending': return 'bg-gray-100 text-gray-800';
        case 'running': return 'bg-blue-100 text-blue-800';
        case 'completed': return 'bg-green-100 text-green-800';
        case 'failed': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

/**
 * 获取任务状态文本
 */
function getTaskStatusText(status) {
    switch (status) {
        case 'pending': return '待执行';
        case 'running': return '执行中';
        case 'completed': return '已完成';
        case 'failed': return '失败';
        default: return '未知';
    }
}

/**
 * 删除任务
 */
function deleteTask(taskId) {
    if (!confirm('确定要删除这个任务吗？')) {
        return;
    }

    // 调用后端API删除任务
    $.ajax({
        url: '/api/chat/delete_task',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            task_id: taskId
        }),
        success: function(response) {
            if (response.success) {
                // 从本地任务列表中移除
                chatTasks = chatTasks.filter(task => task.id !== taskId);

                // 更新任务显示
                updateTasksList();

                showNotification('任务删除成功', 'success');
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            showNotification(`删除任务失败: ${error}`, 'error');
        }
    });
}

/**
 * 执行选中的任务
 */
function executeTasks() {
    const selectedTaskIds = $('.task-checkbox:checked').map(function() {
        return $(this).val();
    }).get();

    if (selectedTaskIds.length === 0) {
        showNotification('请选择要执行的任务', 'warning');
        return;
    }

    // 禁用执行按钮
    $('#executeTasksBtn').prop('disabled', true).html('<i class="ri-loading-line animate-spin mr-1"></i>执行中...');

    // 调用后端API执行任务
    $.ajax({
        url: '/api/chat/execute_task',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            task_ids: selectedTaskIds
        }),
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');

                // 更新本地任务状态
                selectedTaskIds.forEach(taskId => {
                    const task = chatTasks.find(t => t.id === taskId);
                    if (task && response.executed_tasks.includes(taskId)) {
                        task.status = 'running';
                    }
                });

                // 更新任务显示
                updateTasksList();

                // 添加AI回复
                addMessage('assistant', `🚀 已成功启动 ${response.executed_tasks.length} 个任务！任务正在后台执行中...`);
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            showNotification(`执行任务失败: ${error}`, 'error');
        },
        complete: function() {
            // 恢复执行按钮
            $('#executeTasksBtn').prop('disabled', false).html('<i class="ri-play-line mr-1"></i>执行选中任务');
        }
    });
}

/**
 * 获取状态样式类
 */
function getStatusClass(status) {
    switch (status) {
        case '正常': return 'status-success';
        case '处理中': return 'status-processing';
        case '账号失效': return 'status-inactive';
        case '密码错误': return 'status-password-error';
        default: return 'status-pending';
    }
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
    return status || '未知';
}

/**
 * 更新用户数量显示
 */
function updateProfileCount() {
    $('#profile-count span.font-medium').text(profiles.length);
}

/**
 * 加载聊天任务列表
 */
function loadChatTasks() {
    $.ajax({
        url: '/api/chat/tasks',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                chatTasks = response.tasks || [];
                updateTasksList();
            } else {
                console.error('加载任务列表失败:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('加载任务列表失败:', error);
        }
    });
}

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);

    // 获取通知元素
    const notification = $('#notification');
    const icon = $('#notificationIcon');
    const title = $('#notificationTitle');
    const msg = $('#notificationMessage');

    // 设置图标和标题
    let iconClass = '';
    let titleText = '';

    switch (type) {
        case 'success':
            iconClass = 'ri-check-circle-line text-green-500';
            titleText = '成功';
            break;
        case 'error':
            iconClass = 'ri-error-warning-line text-red-500';
            titleText = '错误';
            break;
        case 'warning':
            iconClass = 'ri-alert-line text-yellow-500';
            titleText = '警告';
            break;
        default:
            iconClass = 'ri-information-line text-blue-500';
            titleText = '信息';
    }

    // 设置内容
    icon.html(`<i class="${iconClass}"></i>`);
    title.text(titleText);
    msg.text(message);

    // 显示通知
    notification.removeClass('hidden').addClass('animate-pulse');

    // 3秒后自动隐藏
    setTimeout(() => {
        notification.addClass('hidden').removeClass('animate-pulse');
    }, 3000);
}
