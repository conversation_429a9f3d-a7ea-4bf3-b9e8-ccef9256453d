/**
 * Chrome用户列表映射模块
 * 处理用户列表的排序、映射和显示
 */
const ChromeUsersMapping = {
    // 原始用户列表
    originalProfiles: [],

    // 排序后的用户列表
    sortedProfiles: [],

    // 映射关系
    mapping: {
        // 显示索引到原始ID的映射
        indexToId: {},

        // 原始ID到显示索引的映射
        idToIndex: {}
    },

    // 选中的用户ID
    selectedIds: [],

    // 初始化
    init: function() {
        // 从本地存储加载选择状态
        this.loadSelection();
        // 从本地存储加载用户状态信息
        this.loadProfilesStatus();
        console.log('Chrome用户映射模块已初始化');
    },

    // 加载用户列表
    loadProfiles: function(callback) {
        console.log('开始加载Chrome用户列表');
        // 使用PyWebView API调用
        if (window.pywebview && window.pywebview.api) {
            window.pywebview.api.get_profiles()
                .then(result => {
                    if (result.success) {
                        console.log('成功加载Chrome用户列表:', result.profiles.length, '个用户');
                        this.setProfiles(result.profiles);
                        if (callback) callback(this.sortedProfiles);
                    } else {
                        console.error('加载Chrome用户列表失败:', result.message);
                        if (callback) callback([]);
                    }
                })
                .catch(error => {
                    console.error('加载Chrome用户列表API调用失败:', error);
                    // 回退到AJAX请求
                    this.loadProfilesViaAjax(callback);
                });
        } else {
            // 回退到AJAX请求
            this.loadProfilesViaAjax(callback);
        }
    },

    // 通过AJAX加载用户列表
    loadProfilesViaAjax: function(callback) {
        console.log('通过AJAX加载Chrome用户列表');
        fetch('/api/profiles')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('AJAX成功加载Chrome用户列表:', data.profiles.length, '个用户');
                    this.setProfiles(data.profiles);
                    if (callback) callback(this.sortedProfiles);
                } else {
                    console.error('AJAX加载Chrome用户列表失败:', data.message);
                    if (callback) callback([]);
                }
            })
            .catch(error => {
                console.error('AJAX加载Chrome用户列表请求失败:', error);
                if (callback) callback([]);
            });
    },

    // 设置用户列表并创建映射
    setProfiles: function(profiles) {
        // 保存原始用户列表
        this.originalProfiles = profiles;

        // 提取每个用户的淘宝账号名称
        this.extractTaobaoUsernames();

        // 按数字大小排序
        this.sortProfiles();

        // 创建映射
        this.createMapping();

        // 恢复选择状态
        this.restoreSelection();

        console.log('用户列表设置完成，共', this.sortedProfiles.length, '个用户');
    },

    // 从cookie中提取淘宝账号名称
    extractTaobaoUsernames: function() {
        this.originalProfiles.forEach(profile => {
            if (profile.cookie) {
                // 尝试从cookie中提取lgc参数
                const lgcMatch = profile.cookie.match(/lgc=([^;]+)/);
                if (lgcMatch && lgcMatch[1]) {
                    // 解码URL编码的用户名
                    try {
                        profile.taobao_username = decodeURIComponent(lgcMatch[1]);
                        console.log(`提取到用户 ${profile.id} 的淘宝账号: ${profile.taobao_username}`);
                    } catch (e) {
                        console.error(`解码用户名失败: ${lgcMatch[1]}`, e);
                        profile.taobao_username = lgcMatch[1];
                    }
                } else {
                    console.log(`未能从用户 ${profile.id} 的cookie中提取到淘宝账号`);
                    profile.taobao_username = '';
                }
            } else {
                profile.taobao_username = '';
            }
        });
    },

    // 排序用户列表
    sortProfiles: function() {
        // 复制原始列表
        this.sortedProfiles = [...this.originalProfiles];

        // 自然排序函数 - 模拟Python的natural_sort_key
        function naturalSortKey(str) {
            // 将字符串按数字和非数字部分分割
            // 类似于Python中的re.split(r'(\d+)', s)
            const parts = str.split(/(\d+)/);

            // 将数字部分转换为整数，非数字部分保持不变
            return parts.map(part => {
                // 检查是否为纯数字
                if (/^\d+$/.test(part)) {
                    return parseInt(part, 10);
                }
                // 非数字部分转为小写
                return part.toLowerCase();
            });
        }

        // 比较两个自然排序键
        function compareNaturalSortKeys(keysA, keysB) {
            for (let i = 0; i < Math.min(keysA.length, keysB.length); i++) {
                // 如果当前部分类型不同（一个是数字，一个是字符串）
                if (typeof keysA[i] !== typeof keysB[i]) {
                    // 数字总是排在字符串前面
                    return typeof keysA[i] === 'number' ? -1 : 1;
                }

                // 如果当前部分不相等
                if (keysA[i] !== keysB[i]) {
                    return keysA[i] < keysB[i] ? -1 : 1;
                }
            }

            // 如果前面部分都相同，则长度短的排在前面
            return keysA.length - keysB.length;
        }

        // 按Chrome用户名进行自然排序
        this.sortedProfiles.sort((a, b) => {
            // 获取用户名，如果不存在则使用ID
            const nameA = a.name || a.chrome_user_name || '';
            const nameB = b.name || b.chrome_user_name || '';

            try {
                // 获取自然排序键
                const keysA = naturalSortKey(nameA);
                const keysB = naturalSortKey(nameB);

                // 比较自然排序键
                return compareNaturalSortKeys(keysA, keysB);
            } catch (e) {
                // 如果自然排序出错，回退到简单比较
                console.error('自然排序出错，使用简单比较:', e);

                // 如果用户名相同，则按ID排序
                if (nameA.toLowerCase() === nameB.toLowerCase()) {
                    return a.id.localeCompare(b.id);
                }

                return nameA.toLowerCase() < nameB.toLowerCase() ? -1 : 1;
            }
        });

        console.log('用户列表按自然排序完成');
    },

    // 创建映射关系
    createMapping: function() {
        this.mapping.indexToId = {};
        this.mapping.idToIndex = {};

        // 创建双向映射
        this.sortedProfiles.forEach((profile, index) => {
            this.mapping.indexToId[index] = profile.id;
            this.mapping.idToIndex[profile.id] = index;
        });

        console.log('创建映射关系完成');
    },

    // 根据显示索引获取原始ID
    getIdByIndex: function(index) {
        return this.mapping.indexToId[index];
    },

    // 根据原始ID获取显示索引
    getIndexById: function(id) {
        return this.mapping.idToIndex[id];
    },

    // 获取选中的用户ID列表
    getSelectedIds: function() {
        return this.selectedIds;
    },

    // 更新选择状态
    updateSelection: function() {
        this.selectedIds = [];
        $('.profile-checkbox:checked').each(function() {
            ChromeUsersMapping.selectedIds.push($(this).val());
        });

        // 保存选择状态
        this.saveSelection();

        // 触发选择变更事件
        $(document).trigger('chromeUsers:selectionChanged', [this.selectedIds]);

        console.log('选择状态已更新，选中', this.selectedIds.length, '个用户');
    },

    // 保存选择状态到本地存储
    saveSelection: function() {
        localStorage.setItem('selectedProfiles', JSON.stringify(this.selectedIds));
    },

    // 从本地存储加载选择状态
    loadSelection: function() {
        const savedSelection = localStorage.getItem('selectedProfiles');
        if (savedSelection) {
            try {
                this.selectedIds = JSON.parse(savedSelection);
                console.log('从本地存储加载选择状态:', this.selectedIds.length, '个用户');
            } catch (e) {
                console.error('解析保存的选择状态失败:', e);
                this.selectedIds = [];
            }
        } else {
            this.selectedIds = [];
            console.log('本地存储中没有保存的选择状态');
        }
    },

    // 保存用户状态信息到本地存储
    saveProfilesStatus: function() {
        // 提取需要保存的信息
        const profilesStatus = {};
        this.sortedProfiles.forEach(profile => {
            profilesStatus[profile.id] = {
                status: profile.status || '正常',
                cookie: profile.cookie ? true : false,
                last_check: profile.last_check || '',
                taobao_username: profile.taobao_username || ''
            };
        });

        // 保存到localStorage
        localStorage.setItem('profilesStatus', JSON.stringify(profilesStatus));
        console.log('用户状态信息已保存到本地存储');
    },

    // 从本地存储加载用户状态信息
    loadProfilesStatus: function() {
        const savedStatus = localStorage.getItem('profilesStatus');
        if (!savedStatus) {
            console.log('本地存储中没有用户状态信息');
            return;
        }

        try {
            const profilesStatus = JSON.parse(savedStatus);
            console.log('从本地存储加载用户状态信息');

            // 将加载的状态信息应用到用户列表
            this.originalProfiles.forEach(profile => {
                const savedProfile = profilesStatus[profile.id];
                if (savedProfile) {
                    profile.status = savedProfile.status;
                    profile.cookie = savedProfile.cookie ? 'cookie-exists' : '';
                    profile.last_check = savedProfile.last_check;
                    profile.taobao_username = savedProfile.taobao_username;
                }
            });

            // 同步到排序后的列表
            this.sortedProfiles.forEach(profile => {
                const savedProfile = profilesStatus[profile.id];
                if (savedProfile) {
                    profile.status = savedProfile.status;
                    profile.cookie = savedProfile.cookie ? 'cookie-exists' : '';
                    profile.last_check = savedProfile.last_check;
                    profile.taobao_username = savedProfile.taobao_username;
                }
            });

        } catch (e) {
            console.error('解析保存的用户状态信息失败:', e);
        }
    },

    // 恢复选择状态
    restoreSelection: function() {
        // 清除所有选择
        $('.profile-checkbox').prop('checked', false);

        // 恢复保存的选择
        this.selectedIds.forEach(id => {
            const index = this.getIndexById(id);
            if (index !== undefined) {
                $(`#profile-checkbox-${index}`).prop('checked', true);
            }
        });

        console.log('选择状态已恢复');
    },

    // 渲染用户列表
    renderProfilesTable: function(containerId) {
        const container = $(containerId);
        if (!container.length) {
            console.error(`找不到容器: ${containerId}`);
            return;
        }

        // 清空容器
        container.empty();

        // 如果没有用户，显示提示信息
        if (this.sortedProfiles.length === 0) {
            container.html(`
                <tr>
                    <td colspan="9" class="py-4 text-center text-gray-500">
                        <div class="flex flex-col items-center">
                            <i class="ri-user-search-line text-3xl mb-2"></i>
                            <span>未找到Chrome用户</span>
                        </div>
                    </td>
                </tr>
            `);
            return;
        }

        // 渲染用户列表
        this.sortedProfiles.forEach((profile, index) => {
            // 获取状态样式
            let statusClass = '';
            let statusText = profile.status || '正常';

            if (statusText === '账号失效') {
                statusClass = 'text-red-500 bg-red-50';
            } else if (statusText === '正常') {
                statusClass = 'text-green-500 bg-green-50';
            }

            // 创建行
            const row = $(`
                <tr class="border-b hover:bg-gray-50">
                    <td class="py-3 px-4">
                        <input
                            type="checkbox"
                            id="profile-checkbox-${index}"
                            value="${profile.id}"
                            class="profile-checkbox rounded border-gray-300 text-primary focus:ring-primary/20"
                            data-original-id="${profile.id}"
                        />
                    </td>
                    <td class="py-3 px-4 text-sm">
                        <span class="text-gray-500">${index + 1}</span>
                        <span class="text-xs text-gray-400 ml-1">(${profile.id})</span>
                    </td>
                    <td class="py-3 px-4">
                        <div class="flex flex-col">
                            <span>${profile.name || profile.chrome_user_name || '未命名'}</span>
                            ${profile.taobao_username ? `<span class="text-xs text-blue-500">${profile.taobao_username}</span>` : ''}
                        </div>
                    </td>
                    <td class="py-3 px-4 text-sm text-gray-500 truncate max-w-xs">${profile.profile_dir || profile.path || ''}</td>
                    <td class="py-3 px-4">
                        <span class="inline-block px-2 py-1 rounded-full text-xs ${statusClass}">
                            ${statusText}
                        </span>
                    </td>
                    <td class="py-3 px-4 text-center" id="waitPay-${profile.id}">
                        <div class="inline-flex items-center justify-center w-6 h-6 bg-gray-100 rounded-full">
                            <span class="text-xs text-gray-500">-</span>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center" id="waitSend-${profile.id}">
                        <div class="inline-flex items-center justify-center w-6 h-6 bg-gray-100 rounded-full">
                            <span class="text-xs text-gray-500">-</span>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center" id="waitConfirm-${profile.id}">
                        <div class="inline-flex items-center justify-center w-6 h-6 bg-gray-100 rounded-full">
                            <span class="text-xs text-gray-500">-</span>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center" id="waitRate-${profile.id}">
                        <div class="inline-flex items-center justify-center w-6 h-6 bg-gray-100 rounded-full">
                            <span class="text-xs text-gray-500">-</span>
                        </div>
                    </td>
                </tr>
            `);

            // 添加到容器
            container.append(row);

            // 获取订单数量信息
            this.fetchOrderCounts(profile.id);
        });

        // 绑定复选框事件
        $('.profile-checkbox').change(function() {
            ChromeUsersMapping.updateSelection();
        });

        // 恢复选择状态
        this.restoreSelection();

        console.log('用户列表渲染完成');
    },

    // 获取订单数量信息
    fetchOrderCounts: function(profileId) {
        // 发送请求获取订单数量
        fetch('/api/order_counts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                profile_id: profileId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新UI
                this.updateOrderCountUI(profileId, 'waitPay', data.data.waitPay);
                this.updateOrderCountUI(profileId, 'waitSend', data.data.waitSend);
                this.updateOrderCountUI(profileId, 'waitConfirm', data.data.waitConfirm);
                this.updateOrderCountUI(profileId, 'waitRate', data.data.waitRate);

                // 更新用户状态为正常
                this.updateProfileStatus(profileId, '正常');
            } else {
                console.error('获取订单数量失败:', data.message);

                // 检查是否是登录过期
                if (data.session_expired) {
                    // 更新用户状态为账号失效
                    this.updateProfileStatus(profileId, '账号失效');

                    // 清空订单数量显示
                    this.updateOrderCountUI(profileId, 'waitPay', '0');
                    this.updateOrderCountUI(profileId, 'waitSend', '0');
                    this.updateOrderCountUI(profileId, 'waitConfirm', '0');
                    this.updateOrderCountUI(profileId, 'waitRate', '0');
                }
            }
        })
        .catch(error => {
            console.error('获取订单数量请求失败:', error);
        });
    },

    // 更新用户状态
    updateProfileStatus: function(profileId, status) {
        // 查找用户行
        const row = $(`.profile-checkbox[value="${profileId}"]`).closest('tr');
        if (!row.length) return;

        // 查找状态单元格
        const statusCell = row.find('td:nth-child(5)');
        if (!statusCell.length) return;

        // 设置状态样式
        let statusClass = '';
        if (status === '账号失效') {
            statusClass = 'text-red-500 bg-red-50';
        } else if (status === '正常') {
            statusClass = 'text-green-500 bg-green-50';
        }

        // 更新状态显示
        statusCell.html(`
            <span class="inline-block px-2 py-1 rounded-full text-xs ${statusClass}">
                ${status}
            </span>
        `);

        // 更新原始数据
        for (let i = 0; i < this.sortedProfiles.length; i++) {
            if (this.sortedProfiles[i].id === profileId) {
                this.sortedProfiles[i].status = status;
                break;
            }
        }

        // 保存用户状态信息到本地存储
        this.saveProfilesStatus();
    },

    // 更新订单数量UI
    updateOrderCountUI: function(profileId, type, count) {
        const cell = $(`#${type}-${profileId}`);
        if (!cell.length) return;

        // 根据数量设置不同的样式
        let bgColor = 'bg-gray-100';
        let textColor = 'text-gray-500';

        if (count > 0) {
            switch(type) {
                case 'waitPay':
                    bgColor = 'bg-red-100';
                    textColor = 'text-red-500';
                    break;
                case 'waitSend':
                    bgColor = 'bg-blue-100';
                    textColor = 'text-blue-500';
                    break;
                case 'waitConfirm':
                    bgColor = 'bg-orange-100';  // 改为橙色
                    textColor = 'text-orange-500';  // 改为橙色
                    break;
                case 'waitRate':
                    bgColor = 'bg-green-100';
                    textColor = 'text-green-500';
                    break;
            }
        }

        // 更新UI
        cell.html(`
            <div class="inline-flex items-center justify-center w-6 h-6 ${bgColor} rounded-full">
                <span class="text-xs ${textColor} font-medium">${count}</span>
            </div>
        `);
    },

    // 选择所有未登录用户
    selectAllNotLoggedIn: function() {
        // 先清除所有选择
        $('.profile-checkbox').prop('checked', false);

        // 计数器
        let selectedCount = 0;

        // 遍历所有用户
        this.sortedProfiles.forEach((profile) => {
            // 检查是否未登录
            const isLoggedIn = profile.cookie && profile.cookie.length > 0;

            if (!isLoggedIn) {
                // 获取显示索引
                const index = this.getIndexById(profile.id);
                if (index !== undefined) {
                    // 选中对应的复选框
                    $(`#profile-checkbox-${index}`).prop('checked', true);
                    selectedCount++;
                }
            }
        });

        // 更新选择状态
        this.updateSelection();

        return selectedCount;
    }
};
