/**
 * 确认收货页面的JavaScript
 */

// 全局变量
let profiles = [];
let autoRefreshEnabled = true;
let refreshInterval = null;
let isTaskRunning = false;

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('确认收货页面加载完成');

    // 显示当前日期
    const dateOptions = {
        weekday: 'long',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    };
    const now = new Date();
    document.getElementById('current-date').textContent = now.toLocaleDateString('zh-CN', dateOptions);

    // 加载Chrome用户列表
    loadProfiles();

    // 加载配置
    loadConfig();

    // 绑定事件
    bindEvents();

    // 启动状态刷新
    startStatusRefresh();
});

// 加载Chrome用户列表
function loadProfiles() {
    // 如果映射模块已加载，使用映射模块加载用户列表
    if (typeof ChromeUsersMapping !== 'undefined') {
        console.log('使用ChromeUsersMapping加载用户列表');
        ChromeUsersMapping.loadProfiles(function(sortedProfiles) {
            // 保存到全局变量
            profiles = sortedProfiles;

            // 渲染用户列表
            renderProfiles();
        });
    } else {
        console.log('ChromeUsersMapping未加载，使用原始方法加载用户列表');
        // 使用PyWebView API调用
        if (window.pywebview && window.pywebview.api) {
            window.pywebview.api.get_profiles()
                .then(result => {
                    if (result.success) {
                        profiles = result.profiles;
                        renderProfiles();
                    } else {
                        showNotification('加载Chrome用户列表失败: ' + result.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('加载Chrome用户列表API调用失败:', error);

                    // 回退到AJAX请求
                    loadProfilesViaAjax();
                });
        } else {
            loadProfilesViaAjax();
        }
    }
}

// 通过AJAX加载Chrome用户列表(作为备用方法)
function loadProfilesViaAjax() {
    fetch('/api/profiles')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                profiles = data.profiles;
                renderProfiles();
            } else {
                showNotification('加载Chrome用户列表失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('加载Chrome用户列表失败:', error);
            showNotification('加载Chrome用户列表失败', 'error');
        });
}

// 渲染Chrome用户列表
function renderProfiles() {
    // 使用正确的容器ID
    const tableBody = document.getElementById('profilesTableBody');

    // 清空容器
    if (tableBody) {
        tableBody.innerHTML = '';
    } else {
        console.error('找不到profilesTableBody元素');
        return;
    }

    if (profiles.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="py-4 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <i class="ri-user-search-line text-3xl mb-2"></i>
                        <span>没有找到Chrome用户</span>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // 创建用户列表
    profiles.forEach((profile, index) => {
        const row = document.createElement('tr');
        row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
        row.className += ' transition duration-300 ease-in-out hover:bg-yellow-50';

        // 获取状态样式
        const statusClass = getStatusClass(profile.status || '正常');

        // 使用映射模块获取显示索引
        let displayIndex = index;
        if (typeof ChromeUsersMapping !== 'undefined') {
            displayIndex = ChromeUsersMapping.getIndexById(profile.id) || index;
        }

        row.innerHTML = `
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 text-center">
                <input type="checkbox" id="profile-checkbox-${displayIndex}" class="profile-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" value="${profile.id}" data-original-id="${profile.id}">
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-500">
                <span>${displayIndex + 1}</span>
                <span class="text-xs text-gray-400 ml-1">(${profile.id})</span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                <div class="flex flex-col">
                    <span>${profile.name || profile.id}</span>
                    ${profile.taobao_username ? `<span class="text-xs text-blue-500">${profile.taobao_username}</span>` : ''}
                </div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                <div class="relative">
                    <input type="text" class="pay-password-input w-full border border-gray-200 rounded-lg p-1 text-sm focus:border-primary focus:ring-1 focus:ring-primary"
                           placeholder="支付密码"
                           data-profile-id="${profile.id}"
                           value="${profile.pay_password || ''}">
                    <button type="button" class="toggle-password absolute right-2 top-1 text-gray-500 hover:text-primary" data-profile-id="${profile.id}">
                        <i class="ri-eye-line text-xs"></i>
                    </button>
                </div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full status-badge ${statusClass}" id="status_${profile.id}">
                    ${profile.status || '正常'}
                </span>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // 绑定密码显示/隐藏切换按钮
    document.querySelectorAll('.toggle-password').forEach(button => {
        button.addEventListener('click', function() {
            const profileId = this.getAttribute('data-profile-id');
            const passwordInput = document.querySelector(`.pay-password-input[data-profile-id="${profileId}"]`);
            const icon = this.querySelector('i');

            if (passwordInput.type === 'text') {
                passwordInput.type = 'password';
                icon.classList.remove('ri-eye-line');
                icon.classList.add('ri-eye-off-line');
            } else {
                passwordInput.type = 'text';
                icon.classList.remove('ri-eye-off-line');
                icon.classList.add('ri-eye-line');
            }
        });
    });

    // 绑定全选复选框事件
    const selectAllCheckbox = document.getElementById('selectAllProfiles');
    const checkAllCheckbox = document.getElementById('checkAllProfiles');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            // 同步另一个全选复选框
            if (checkAllCheckbox) {
                checkAllCheckbox.checked = isChecked;
            }
            // 同步所有用户复选框
            document.querySelectorAll('.profile-checkbox').forEach(checkbox => {
                checkbox.checked = isChecked;
            });

            // 如果映射模块已加载，更新选择状态
            if (typeof ChromeUsersMapping !== 'undefined') {
                ChromeUsersMapping.updateSelection();
            }
        });
    }

    if (checkAllCheckbox) {
        checkAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            // 同步另一个全选复选框
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = isChecked;
            }
            // 同步所有用户复选框
            document.querySelectorAll('.profile-checkbox').forEach(checkbox => {
                checkbox.checked = isChecked;
            });

            // 如果映射模块已加载，更新选择状态
            if (typeof ChromeUsersMapping !== 'undefined') {
                ChromeUsersMapping.updateSelection();
            }
        });
    }

    // 绑定复选框变更事件
    document.querySelectorAll('.profile-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // 如果映射模块已加载，更新选择状态
            if (typeof ChromeUsersMapping !== 'undefined') {
                ChromeUsersMapping.updateSelection();
            }
        });
    });

    // 绑定刷新按钮事件
    document.getElementById('btnRefreshProfiles').addEventListener('click', loadProfiles);

    // 如果映射模块已加载，恢复选择状态
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.restoreSelection();
    }
}

// 获取状态对应的CSS类
function getStatusClass(status) {
    switch (status) {
        case '正常':
            return 'status-normal';
        case '账号失效':
        case '处理出错':
        case '密码错误':
            return 'status-error';
        case '没有待确认订单':
        case '日期范围外':
            return 'status-warning';
        case '处理中':
        case '自动评价中':
            return 'status-processing';
        default:
            return 'status-waiting';
    }
}

// 加载配置
function loadConfig() {
    // 使用PyWebView API调用
    if (window.pywebview && window.pywebview.api) {
        window.pywebview.api.get_config()
            .then(result => {
                if (result.success) {
                    const config = result.config || result.data; // 兼容两种返回格式
                    applyConfig(config);
                }
            })
            .catch(error => {
                console.error('加载配置API调用失败:', error);

                // 回退到AJAX请求
                loadConfigViaAjax();
            });
    } else {
        loadConfigViaAjax();
    }
}

// 通过AJAX加载配置(作为备用方法)
function loadConfigViaAjax() {
    fetch('/api/config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const config = data.config || data.data; // 兼容两种返回格式
                applyConfig(config);
            }
        })
        .catch(error => {
            console.error('加载配置失败:', error);
        });
}

// 应用配置到界面
function applyConfig(config) {
    // 设置表单值
    if (config.start_date) {
        document.getElementById('startDate').value = config.start_date;
    }
    if (config.end_date) {
        document.getElementById('endDate').value = config.end_date;
    }
    if (config.interval) {
        document.getElementById('interval').value = config.interval;
    }
    if (config.max_threads) {
        document.getElementById('maxThreads').value = config.max_threads;
    }
    if (config.auto_review_enabled !== undefined) {
        document.getElementById('autoReviewEnabled').checked = config.auto_review_enabled;
    }
    if (config.review_text) {
        document.getElementById('reviewText').value = config.review_text;
    }
    if (config.image_directory) {
        document.getElementById('imageDirectory').value = config.image_directory;
    }

    // 加载每个用户的支付密码
    if (config.profiles && typeof config.profiles === 'object') {
        // 等待用户列表渲染完成后再设置密码
        setTimeout(() => {
            for (const [profileId, profileData] of Object.entries(config.profiles)) {
                if (profileData.pay_password) {
                    const passwordInput = document.querySelector(`.pay-password-input[data-profile-id="${profileId}"]`);
                    if (passwordInput) {
                        passwordInput.value = profileData.pay_password;
                    }
                }
            }
        }, 500); // 给用户列表渲染一些时间
    }

    // 更新评价内容输入框显示状态
    toggleReviewTextContainer();
}

// 保存配置
function saveConfig() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const interval = document.getElementById('interval').value;

    // 确保maxThreads是整数
    let maxThreads = 3; // 默认值
    if (document.getElementById('maxThreads')) {
        const maxThreadsValue = document.getElementById('maxThreads').value;
        maxThreads = parseInt(maxThreadsValue) || 3; // 如果解析失败，使用默认值3
    }

    const autoReviewEnabled = document.getElementById('autoReviewEnabled').checked;
    const reviewText = document.getElementById('reviewText').value;
    const imageDirectory = document.getElementById('imageDirectory').value;

    // 收集每个用户的支付密码
    const profilePasswords = {};
    document.querySelectorAll('.pay-password-input').forEach(input => {
        const profileId = input.getAttribute('data-profile-id');
        const password = input.value;
        if (password) {
            profilePasswords[profileId] = password;
        }
    });

    // 构建配置对象
    const config = {
        start_date: startDate,
        end_date: endDate,
        interval: interval,
        max_threads: maxThreads,
        auto_review_enabled: autoReviewEnabled,
        review_text: reviewText,
        image_directory: imageDirectory,
        profiles: {}
    };

    // 将每个用户的支付密码和其他信息添加到配置中
    for (const [profileId, password] of Object.entries(profilePasswords)) {
        if (!config.profiles[profileId]) {
            config.profiles[profileId] = {};
        }
        config.profiles[profileId].pay_password = password;

        // 查找对应的profile对象，获取用户名和配置目录信息
        const profile = profiles.find(p => p.id === profileId);
        if (profile) {
            config.profiles[profileId].chrome_user_name = profile.name || '';
            config.profiles[profileId].profile_dir = profile.profile_dir || '';
            // 保存状态信息
            const statusElement = document.getElementById(`status_${profileId}`);
            if (statusElement) {
                config.profiles[profileId].status = statusElement.textContent.trim();
            }
        }
    }

    console.log('保存配置:', {
        start_date: config.start_date,
        end_date: config.end_date,
        interval: config.interval,
        max_threads: config.max_threads,
        auto_review_enabled: config.auto_review_enabled,
        profiles_count: Object.keys(config.profiles).length
    });

    // 使用PyWebView API调用
    if (window.pywebview && window.pywebview.api) {
        window.pywebview.api.save_config(config)
            .then(result => {
                if (result.success) {
                    showNotification('配置保存成功', 'success');
                } else {
                    showNotification('配置保存失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存配置API调用失败:', error);
                showNotification('保存配置失败: ' + error, 'error');

                // 回退到AJAX请求
                saveConfigViaAjax(config);
            });
    } else {
        console.warn('PyWebView API不可用，回退到AJAX请求');
        saveConfigViaAjax(config);
    }
}

// 通过AJAX保存配置(作为备用方法)
function saveConfigViaAjax(config) {
    fetch('/api/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('配置保存成功', 'success');
        } else {
            showNotification('配置保存失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('保存配置失败:', error);
        showNotification('保存配置失败', 'error');
    });
}

// 切换评价内容输入框的显示状态
function toggleReviewTextContainer() {
    const autoReviewEnabled = document.getElementById('autoReviewEnabled').checked;
    document.getElementById('reviewTextContainer').style.display = autoReviewEnabled ? 'block' : 'none';
    document.getElementById('imageDirectoryContainer').style.display = autoReviewEnabled ? 'block' : 'none';
}

// 选择图片目录
function selectImageDirectory() {
    // 使用PyWebView API选择目录
    if (window.pywebview && window.pywebview.api) {
        window.pywebview.api.select_directory()
            .then(result => {
                if (result.success && result.directory) {
                    document.getElementById('imageDirectory').value = result.directory;
                    showNotification('图片目录选择成功', 'success');
                } else if (result.cancelled) {
                    // 用户取消选择，不显示错误
                } else {
                    showNotification('选择目录失败: ' + (result.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('选择目录API调用失败:', error);
                showNotification('选择目录失败: ' + error, 'error');
            });
    } else {
        showNotification('目录选择功能不可用', 'error');
    }
}

// 绑定事件
function bindEvents() {
    // 保存配置按钮
    document.getElementById('saveConfigBtn').addEventListener('click', saveConfig);

    // 自动评价复选框
    document.getElementById('autoReviewEnabled').addEventListener('change', toggleReviewTextContainer);

    // 选择图片目录按钮
    document.getElementById('selectImageDirBtn').addEventListener('click', selectImageDirectory);

    // 开始确认收货按钮
    document.getElementById('startBtn').addEventListener('click', startConfirm);

    // 停止按钮
    document.getElementById('stopBtn').addEventListener('click', stopConfirm);

    // 自动刷新开关
    document.getElementById('autoRefreshToggle').addEventListener('change', function() {
        autoRefreshEnabled = this.checked;
        if (autoRefreshEnabled && !refreshInterval) {
            startStatusRefresh();
        }
    });

    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', function() {
        refreshStatus();
    });
}

// 开始确认收货
function startConfirm() {
    // 获取选中的用户
    let selectedProfiles = [];
    const profilePasswords = {};

    // 如果映射模块已加载，使用映射模块获取选中的用户
    if (typeof ChromeUsersMapping !== 'undefined') {
        selectedProfiles = ChromeUsersMapping.getSelectedIds();
    } else {
        // 回退到原始方法
        document.querySelectorAll('.profile-checkbox:checked').forEach(checkbox => {
            const profileId = checkbox.getAttribute('value') || checkbox.getAttribute('data-id');
            selectedProfiles.push(profileId);
        });
    }

    // 获取每个选中用户的支付密码
    selectedProfiles.forEach(profileId => {
        // 获取该用户的支付密码
        const passwordInput = document.querySelector(`.pay-password-input[data-profile-id="${profileId}"]`);
        if (passwordInput && passwordInput.value) {
            profilePasswords[profileId] = passwordInput.value;
        }
    });

    if (selectedProfiles.length === 0) {
        showNotification('请至少选择一个Chrome用户', 'error');
        return;
    }

    // 更新任务状态
    document.getElementById('taskStatus').textContent = '准备中';
    document.getElementById('taskStatus').className = 'text-2xl font-semibold mt-1 text-blue-500';

    // 重置进度条
    const taskProgress = document.getElementById('taskProgress');
    taskProgress.style.width = '0%';
    taskProgress.textContent = '0%';

    // 更新处理状态文本
    document.getElementById('processingText').textContent = '正在启动任务...';

    // 打印请求数据，帮助调试
    console.log('发送确认收货请求:', {
        profiles: selectedProfiles,
        profile_passwords: Object.keys(profilePasswords).length > 0 ? '已设置' : '未设置'
    });

    // 获取线程数设置
    let maxThreads = 3; // 默认值
    if (document.getElementById('maxThreads')) {
        const maxThreadsValue = document.getElementById('maxThreads').value;
        maxThreads = parseInt(maxThreadsValue) || 3; // 如果解析失败，使用默认值3
    }
    const useMultiThread = maxThreads > 1;

    console.log(`使用${useMultiThread ? '多' : '单'}线程模式，最大线程数: ${maxThreads}`);

    // 使用PyWebView API调用
    if (window.pywebview && window.pywebview.api) {
        window.pywebview.api.start_confirm(selectedProfiles, profilePasswords, useMultiThread)
            .then(result => {
                console.log('API调用结果:', result);
                if (result.success) {
                    showNotification('确认收货任务已启动', 'success');

                    // 设置任务运行状态
                    isTaskRunning = true;
                } else {
                    showNotification(result.message || '启动任务失败', 'error');
                }
            })
            .catch(error => {
                console.error('API调用失败:', error);
                showNotification('启动确认收货任务失败: ' + error, 'error');

                // 回退到AJAX请求
                startConfirmViaAjax(selectedProfiles, profilePasswords);
            });
    } else {
        console.warn('PyWebView API不可用，回退到AJAX请求');
        startConfirmViaAjax(selectedProfiles, profilePasswords);
    }
}

// 通过AJAX启动确认收货任务(作为备用方法)
function startConfirmViaAjax(selectedProfiles, profilePasswords) {
    fetch('/api/start_confirm', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            profiles: selectedProfiles,
            profile_passwords: profilePasswords
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('确认收货任务已启动', 'success');

            // 设置任务运行状态
            isTaskRunning = true;
        } else {
            showNotification(data.message || '启动任务失败', 'error');
        }
    })
    .catch(error => {
        console.error('启动确认收货任务失败:', error);
        showNotification('启动确认收货任务失败', 'error');
    });
}

// 停止确认收货
function stopConfirm() {
    console.log('停止确认收货任务');

    // 使用PyWebView API调用
    if (window.pywebview && window.pywebview.api) {
        window.pywebview.api.stop_confirm()
            .then(result => {
                console.log('停止任务API调用结果:', result);
                if (result.success) {
                    showNotification('确认收货任务已停止', 'success');

                    // 设置任务运行状态
                    isTaskRunning = false;
                } else {
                    showNotification(result.message || '停止任务失败', 'error');
                }
            })
            .catch(error => {
                console.error('停止任务API调用失败:', error);
                showNotification('停止确认收货任务失败: ' + error, 'error');

                // 回退到AJAX请求
                stopConfirmViaAjax();
            });
    } else {
        console.warn('PyWebView API不可用，回退到AJAX请求');
        stopConfirmViaAjax();
    }
}

// 通过AJAX停止确认收货任务(作为备用方法)
function stopConfirmViaAjax() {
    fetch('/api/stop_confirm', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('确认收货任务已停止', 'success');

            // 设置任务运行状态
            isTaskRunning = false;
        } else {
            showNotification('停止失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('停止失败:', error);
        showNotification('停止失败', 'error');
    });
}

// 启动状态刷新
function startStatusRefresh() {
    // 先执行一次刷新
    refreshStatus();

    // 设置定时刷新
    refreshInterval = setInterval(refreshStatus, 3000);
}

// 刷新状态
function refreshStatus() {
    if (!autoRefreshEnabled) {
        return;
    }

    // 使用PyWebView API调用
    if (window.pywebview && window.pywebview.api) {
        window.pywebview.api.get_receipt_status()
            .then(result => {
                if (result.success) {
                    updateStatus(result.data);
                }
            })
            .catch(error => {
                console.error('刷新状态API调用失败:', error);

                // 回退到AJAX请求
                refreshStatusViaAjax();
            });
    } else {
        refreshStatusViaAjax();
    }
}

// 通过AJAX刷新状态(作为备用方法)
function refreshStatusViaAjax() {
    fetch('/api/get_receipt_status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatus(data.data);
            }
        })
        .catch(error => {
            console.error('刷新状态失败:', error);
        });
}

// 更新状态
function updateStatus(status) {
    // 更新计数器 - 兼容两种字段名
    document.getElementById('completedCounter').textContent = status.completed_tasks || status.completed || '0';
    document.getElementById('skippedCounter').textContent = status.skipped_tasks || status.skipped || '0';

    // 更新待处理订单数 - 兼容两种字段名
    const totalTasks = status.total_tasks || status.total || 0;
    const completedTasks = status.completed_tasks || status.completed || 0;
    const skippedTasks = status.skipped_tasks || status.skipped || 0;
    const pendingTasks = totalTasks - completedTasks - skippedTasks;
    document.getElementById('pendingCounter').textContent = pendingTasks > 0 ? pendingTasks : '0';

    // 更新任务运行状态
    isTaskRunning = status.running || status.is_running; // 兼容两种字段名

    // 更新任务状态文本
    const taskStatusElement = document.getElementById('taskStatus');
    if (isTaskRunning) {
        if (status.waiting) {
            taskStatusElement.textContent = '等待中';
            taskStatusElement.className = 'text-2xl font-semibold mt-1 text-yellow-500';
        } else {
            taskStatusElement.textContent = '运行中';
            taskStatusElement.className = 'text-2xl font-semibold mt-1 text-green-500';
        }
    } else {
        taskStatusElement.textContent = '未运行';
        taskStatusElement.className = 'text-2xl font-semibold mt-1';
    }

    // 更新进度条 - 使用前面已经计算好的变量
    let progressPercent = 0;

    if (totalTasks > 0) {
        progressPercent = Math.round(((completedTasks + skippedTasks) / totalTasks) * 100);
    }

    const taskProgress = document.getElementById('taskProgress');
    taskProgress.style.width = `${progressPercent}%`;
    taskProgress.textContent = `${progressPercent}%`;

    // 更新按钮状态
    if (isTaskRunning) {
        document.getElementById('startBtn').disabled = true;
        document.getElementById('startBtn').classList.add('opacity-50', 'cursor-not-allowed');

        document.getElementById('stopBtn').disabled = false;
        document.getElementById('stopBtn').classList.remove('opacity-50', 'cursor-not-allowed');
    } else {
        document.getElementById('startBtn').disabled = false;
        document.getElementById('startBtn').classList.remove('opacity-50', 'cursor-not-allowed');

        document.getElementById('stopBtn').disabled = true;
        document.getElementById('stopBtn').classList.add('opacity-50', 'cursor-not-allowed');
    }

    // 更新处理状态
    if (isTaskRunning) {
        // 兼容两种字段名
        const currentProfile = status.current_profile || (status.active_tasks && status.active_tasks[0] ? status.active_tasks[0].profile_name : null);

        if (currentProfile) {
            document.getElementById('processingText').textContent = `正在处理用户: ${currentProfile}`;
        } else {
            document.getElementById('processingText').textContent = '正在处理...';
        }

        // 兼容两种字段名
        const isWaiting = status.waiting || (status.active_tasks && status.active_tasks.length === 0 && isTaskRunning);
        const waitingTime = status.waiting_time || '60';

        if (isWaiting) {
            document.getElementById('waitingStatus').style.display = 'block';
            document.getElementById('waitingTime').textContent = waitingTime;
        } else {
            document.getElementById('waitingStatus').style.display = 'none';
        }

        // 更新评价状态 - 兼容两种字段名
        const isReviewing = status.reviewing || (status.active_tasks && status.active_tasks.some(task => task.action === 'review'));
        const currentOrderId = status.current_order_id || '-';
        const currentOrderTitle = status.current_order_title || '-';
        const currentOrderStatus = status.current_order_status || '-';
        const totalOrders = status.total_orders || '0';

        if (isReviewing) {
            document.getElementById('reviewStatus').style.display = 'block';
            document.getElementById('currentOrderId').textContent = currentOrderId;
            document.getElementById('currentOrderTitle').textContent = currentOrderTitle;
            document.getElementById('currentOrderStatus').textContent = currentOrderStatus;
            document.getElementById('totalOrderCount').textContent = totalOrders;
        } else {
            document.getElementById('reviewStatus').style.display = 'none';
        }
    } else {
        document.getElementById('waitingStatus').style.display = 'none';
        document.getElementById('reviewStatus').style.display = 'none';
    }

    // 更新用户状态
    if (status.profiles) {
        for (const [profileId, profileStatus] of Object.entries(status.profiles)) {
            const statusElement = document.getElementById(`status_${profileId}`);
            if (statusElement) {
                statusElement.textContent = profileStatus.status || '正常';
                statusElement.className = `px-2 inline-flex text-xs leading-5 font-semibold rounded-full status-badge ${getStatusClass(profileStatus.status || '正常')}`;
            }
        }
    }
}

// 更新用户状态为密码错误
function updatePasswordError(profileId) {
    if (!profileId) {
        console.error('未提供profileId');
        showNotification('更新状态失败：未提供用户ID', 'error');
        return;
    }

    console.log(`更新用户 ${profileId} 状态为密码错误`);

    // 发送请求到后端API
    fetch('/api/update_password_error', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            profile_id: profileId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(`用户 ${profileId} 状态已更新为密码错误`);
            showNotification(`用户状态已更新为密码错误`, 'success');

            // 更新UI上的状态显示
            const statusElement = document.getElementById(`status_${profileId}`);
            if (statusElement) {
                statusElement.textContent = '密码错误';
                statusElement.className = `px-2 inline-flex text-xs leading-5 font-semibold rounded-full status-badge ${getStatusClass('密码错误')}`;
            }
        } else {
            console.error(`更新用户状态失败: ${data.message}`);
            showNotification(`更新状态失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        console.error(`更新用户状态请求失败: ${error}`);
        showNotification('更新状态请求失败', 'error');
    });
}

// 显示通知
function showNotification(message, type = 'success') {
    if (window.showNotification) {
        window.showNotification(message, type);
    } else {
        alert(message);
    }
}
