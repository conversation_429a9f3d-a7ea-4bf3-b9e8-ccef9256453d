/**
 * Cookie获取页面的JavaScript
 */

// 全局变量
let profiles = [];
let isTaskRunning = false;

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('Cookie获取页面加载完成');

    // 显示当前日期
    const dateOptions = {
        weekday: 'long',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    };
    const now = new Date();
    document.getElementById('current-date').textContent = now.toLocaleDateString('zh-CN', dateOptions);

    // 加载Chrome用户列表
    loadProfiles();

    // 绑定事件
    bindEvents();
});

// 加载Chrome用户列表
function loadProfiles() {
    // 如果映射模块已加载，使用映射模块加载用户列表
    if (typeof ChromeUsersMapping !== 'undefined') {
        console.log('使用ChromeUsersMapping加载用户列表');
        ChromeUsersMapping.loadProfiles(function(sortedProfiles) {
            // 保存到全局变量
            profiles = sortedProfiles;

            // 渲染用户列表
            renderProfiles();
        });
    } else {
        console.log('ChromeUsersMapping未加载，使用原始方法加载用户列表');
        // 使用PyWebView API调用
        if (window.pywebview && window.pywebview.api) {
            window.pywebview.api.get_profiles()
                .then(result => {
                    if (result.success) {
                        profiles = result.profiles;
                        renderProfiles();
                    } else {
                        showNotification('加载Chrome用户列表失败: ' + result.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('加载Chrome用户列表API调用失败:', error);

                    // 回退到AJAX请求
                    loadProfilesViaAjax();
                });
        } else {
            loadProfilesViaAjax();
        }
    }
}

// 通过AJAX加载Chrome用户列表(作为备用方法)
function loadProfilesViaAjax() {
    fetch('/api/profiles')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                profiles = data.profiles;
                renderProfiles();
            } else {
                showNotification('加载Chrome用户列表失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('加载Chrome用户列表失败:', error);
            showNotification('加载Chrome用户列表失败', 'error');
        });
}

// 从本地存储加载用户状态信息
function loadProfilesStatusFromLocalStorage() {
    // 如果映射模块已加载，使用映射模块的方法
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.loadProfilesStatus();
        return;
    }

    // 否则使用简单的方法加载
    const savedStatus = localStorage.getItem('profilesStatus');
    if (!savedStatus) {
        console.log('本地存储中没有用户状态信息');
        return;
    }

    try {
        const profilesStatus = JSON.parse(savedStatus);
        console.log('从本地存储加载用户状态信息');

        // 将加载的状态信息应用到用户列表
        profiles.forEach(profile => {
            const savedProfile = profilesStatus[profile.id];
            if (savedProfile) {
                profile.status = savedProfile.status;
                profile.cookie = savedProfile.cookie ? 'cookie-exists' : '';
                profile.last_check = savedProfile.last_check;
                profile.taobao_username = savedProfile.taobao_username;
            }
        });

    } catch (e) {
        console.error('解析保存的用户状态信息失败:', e);
    }
}

// 渲染Chrome用户列表
function renderProfiles() {
    // 使用正确的容器ID
    const tableBody = document.getElementById('profilesTableBody');

    // 清空容器
    if (tableBody) {
        tableBody.innerHTML = '';
    } else {
        console.error('找不到profilesTableBody元素');
        return;
    }

    if (profiles.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="py-4 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <i class="ri-user-search-line text-3xl mb-2"></i>
                        <span>没有找到Chrome用户</span>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // 从本地存储加载用户状态信息
    loadProfilesStatusFromLocalStorage();

    // 创建用户列表
    profiles.forEach((profile, index) => {
        const row = document.createElement('tr');
        row.id = `row_${profile.id}`;
        row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
        row.className += ' transition duration-300 ease-in-out hover:bg-yellow-50';

        // 获取登录状态
        const isLoggedIn = profile.cookie && profile.cookie.length > 0;
        const statusClass = isLoggedIn ? 'status-normal' : 'status-warning';
        const statusText = isLoggedIn ? '已登录' : '未登录';

        // 获取最后检查时间
        const lastCheck = profile.last_check || '未检查';

        // 使用映射模块获取显示索引
        let displayIndex = index;
        if (typeof ChromeUsersMapping !== 'undefined') {
            displayIndex = ChromeUsersMapping.getIndexById(profile.id) || index;
        }

        // 创建表格行，确保列的顺序与表头一致：复选框、ID、用户名、登录状态、最后检查时间
        row.innerHTML = `
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 text-center">
                <input type="checkbox" id="profile-checkbox-${displayIndex}" class="profile-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" value="${profile.id}" data-original-id="${profile.id}">
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-500">
                <span>${displayIndex + 1}</span>
                <span class="text-xs text-gray-400 ml-1">(${profile.id})</span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                <div class="flex flex-col">
                    <span>${profile.name || profile.id}</span>
                    ${profile.taobao_username ? `<span class="text-xs text-blue-500">${profile.taobao_username}</span>` : ''}
                </div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full status-badge ${statusClass}" id="status_${profile.id}">
                    ${statusText}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500" id="lastCheck_${profile.id}">
                ${lastCheck}
            </td>
        `;

        tableBody.appendChild(row);

        // 添加隐藏的配置目录信息
        const profileDirInput = document.createElement('input');
        profileDirInput.type = 'hidden';
        profileDirInput.id = 'profile_dir_' + profile.id;
        profileDirInput.value = profile.profile_dir || '';
        row.appendChild(profileDirInput);
    });

    // 绑定全选复选框事件
    const selectAllCheckbox = document.getElementById('selectAllProfiles');
    const checkAllCheckbox = document.getElementById('checkAllProfiles');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            // 同步另一个全选复选框
            if (checkAllCheckbox) {
                checkAllCheckbox.checked = isChecked;
            }
            // 同步所有用户复选框
            document.querySelectorAll('.profile-checkbox').forEach(checkbox => {
                checkbox.checked = isChecked;
            });

            // 如果映射模块已加载，更新选择状态
            if (typeof ChromeUsersMapping !== 'undefined') {
                ChromeUsersMapping.updateSelection();
            }
        });
    }

    if (checkAllCheckbox) {
        checkAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            // 同步另一个全选复选框
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = isChecked;
            }
            // 同步所有用户复选框
            document.querySelectorAll('.profile-checkbox').forEach(checkbox => {
                checkbox.checked = isChecked;
            });

            // 如果映射模块已加载，更新选择状态
            if (typeof ChromeUsersMapping !== 'undefined') {
                ChromeUsersMapping.updateSelection();
            }
        });
    }

    // 绑定复选框变更事件
    document.querySelectorAll('.profile-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // 如果映射模块已加载，更新选择状态
            if (typeof ChromeUsersMapping !== 'undefined') {
                ChromeUsersMapping.updateSelection();
            }
        });
    });

    // 更新计数器
    updateCounters();

    // 如果映射模块已加载，恢复选择状态
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.restoreSelection();
    }
}

// 更新计数器
function updateCounters() {
    let loggedInCount = 0;
    let notLoggedInCount = 0;

    profiles.forEach(profile => {
        if (profile.cookie && profile.cookie.length > 0) {
            loggedInCount++;
        } else {
            notLoggedInCount++;
        }
    });

    document.getElementById('loggedInCounter').textContent = loggedInCount;
    document.getElementById('notLoggedInCounter').textContent = notLoggedInCount;
}

// 绑定事件
function bindEvents() {
    // 获取Cookie按钮
    document.getElementById('getCookieBtn').addEventListener('click', getCookie);

    // 停止按钮
    document.getElementById('stopBtn').addEventListener('click', stopCookie);

    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', function() {
        loadProfiles();
    });

    // 刷新用户列表按钮
    document.getElementById('btnRefreshProfiles').addEventListener('click', function() {
        loadProfiles();
    });

    // 选择所有未登录用户按钮
    document.getElementById('selectNotLoggedInBtn').addEventListener('click', selectAllNotLoggedInProfiles);
}

// 获取Cookie
function getCookie() {
    // 获取选中的用户
    let selectedProfiles = [];

    // 如果映射模块已加载，使用映射模块获取选中的用户
    if (typeof ChromeUsersMapping !== 'undefined') {
        selectedProfiles = ChromeUsersMapping.getSelectedIds();
    } else {
        // 回退到原始方法
        document.querySelectorAll('.profile-checkbox:checked').forEach(checkbox => {
            const profileId = checkbox.getAttribute('value') || checkbox.getAttribute('data-id');
            selectedProfiles.push(profileId);
        });
    }

    if (selectedProfiles.length === 0) {
        showNotification('请至少选择一个Chrome用户', 'error');
        return;
    }

    // 更新任务状态
    document.getElementById('taskStatus').textContent = '准备中';
    document.getElementById('taskStatus').className = 'text-2xl font-semibold mt-1 text-blue-500';

    // 重置进度条
    const taskProgress = document.getElementById('taskProgress');
    taskProgress.style.width = '0%';
    taskProgress.textContent = '0%';

    // 更新处理状态文本
    document.getElementById('processingText').textContent = '正在启动任务...';

    // 禁用获取Cookie按钮，启用停止按钮
    document.getElementById('getCookieBtn').disabled = true;
    document.getElementById('stopBtn').disabled = false;

    // 使用PyWebView API调用
    if (window.pywebview && window.pywebview.api) {
        window.pywebview.api.get_cookies(selectedProfiles)
            .then(result => {
                console.log('API调用结果:', result);
                if (result.success) {
                    showNotification('Cookie获取任务已启动', 'success');

                    // 设置任务运行状态
                    isTaskRunning = true;

                    // 确保停止按钮可用
                    document.getElementById('stopBtn').disabled = false;
                } else {
                    showNotification(result.message || '启动任务失败', 'error');
                    resetTaskStatus();
                }
            })
            .catch(error => {
                console.error('API调用失败:', error);
                showNotification('启动Cookie获取任务失败: ' + error, 'error');
                resetTaskStatus();
            });
    } else {
        showNotification('PyWebView API不可用', 'error');
        resetTaskStatus();
    }
}

// 停止Cookie获取
function stopCookie() {
    if (!isTaskRunning) {
        return;
    }

    // 使用PyWebView API调用
    if (window.pywebview && window.pywebview.api) {
        window.pywebview.api.stop_cookies()
            .then(result => {
                console.log('停止任务结果:', result);
                if (result.success) {
                    showNotification('Cookie获取任务已停止', 'info');
                } else {
                    showNotification(result.message || '停止任务失败', 'error');
                }
                resetTaskStatus();
            })
            .catch(error => {
                console.error('停止任务API调用失败:', error);
                showNotification('停止Cookie获取任务失败: ' + error, 'error');
                resetTaskStatus();
            });
    } else {
        showNotification('PyWebView API不可用', 'error');
        resetTaskStatus();
    }
}

// 重置任务状态
function resetTaskStatus() {
    isTaskRunning = false;
    document.getElementById('taskStatus').textContent = '未运行';
    document.getElementById('taskStatus').className = 'text-2xl font-semibold mt-1 text-gray-500';
    document.getElementById('getCookieBtn').disabled = false;
    document.getElementById('stopBtn').disabled = true;
}

// 更新任务进度
function updateTaskProgress(current, total) {
    if (total <= 0) return;

    const percent = Math.round((current / total) * 100);
    const taskProgress = document.getElementById('taskProgress');
    taskProgress.style.width = `${percent}%`;
    taskProgress.textContent = `${percent}%`;

    document.getElementById('completedCounter').textContent = current;
    document.getElementById('processingText').textContent = `已处理 ${current}/${total} 个用户`;

    if (current >= total) {
        document.getElementById('taskStatus').textContent = '已完成';
        document.getElementById('taskStatus').className = 'text-2xl font-semibold mt-1 text-green-500';
        resetTaskStatus();
    }
}

// 选择所有未登录用户
function selectAllNotLoggedInProfiles() {
    // 全选复选框取消选择
    const selectAllCheckbox = document.getElementById('selectAllProfiles');
    const checkAllCheckbox = document.getElementById('checkAllProfiles');
    if (selectAllCheckbox) selectAllCheckbox.checked = false;
    if (checkAllCheckbox) checkAllCheckbox.checked = false;

    let selectedCount = 0;

    // 如果映射模块已加载，使用映射模块的方法
    if (typeof ChromeUsersMapping !== 'undefined') {
        selectedCount = ChromeUsersMapping.selectAllNotLoggedIn();
    } else {
        // 先取消所有选择
        document.querySelectorAll('.profile-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });

        // 选择所有未登录用户
        profiles.forEach((profile, index) => {
            // 检查是否未登录
            const isLoggedIn = profile.cookie && profile.cookie.length > 0;

            if (!isLoggedIn) {
                // 选中对应的复选框
                const checkbox = document.getElementById(`profile-checkbox-${index}`);
                if (checkbox) {
                    checkbox.checked = true;
                    selectedCount++;
                }
            }
        });
    }

    // 显示通知
    if (selectedCount > 0) {
        showNotification(`已选择 ${selectedCount} 个未登录用户`, 'success');
    } else {
        showNotification('没有找到未登录用户', 'info');
    }
}

// 更新用户状态
function updateProfileStatus(profileId, isLoggedIn, lastCheck) {
    const statusElement = document.getElementById(`status_${profileId}`);
    const lastCheckElement = document.getElementById(`lastCheck_${profileId}`);

    if (statusElement) {
        statusElement.textContent = isLoggedIn ? '已登录' : '未登录';
        statusElement.className = `px-2 inline-flex text-xs leading-5 font-semibold rounded-full status-badge ${isLoggedIn ? 'status-normal' : 'status-warning'}`;
    }

    if (lastCheckElement && lastCheck) {
        lastCheckElement.textContent = lastCheck;
    }

    // 更新该用户在profiles数组中的状态
    for (let i = 0; i < profiles.length; i++) {
        if (profiles[i].id === profileId) {
            profiles[i].cookie = isLoggedIn ? 'cookie-exists' : '';
            profiles[i].last_check = lastCheck;
            break;
        }
    }

    // 更新计数器
    updateCounters();

    // 保存用户状态信息到本地存储
    saveProfilesStatusToLocalStorage();
}

// 保存用户状态信息到本地存储
function saveProfilesStatusToLocalStorage() {
    // 如果映射模块已加载，使用映射模块的方法
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.saveProfilesStatus();
        return;
    }

    // 否则使用简单的方法保存
    const profilesStatus = {};
    profiles.forEach(profile => {
        profilesStatus[profile.id] = {
            status: profile.status || '正常',
            cookie: profile.cookie ? true : false,
            last_check: profile.last_check || '',
            taobao_username: profile.taobao_username || ''
        };
    });

    // 保存到localStorage
    localStorage.setItem('profilesStatus', JSON.stringify(profilesStatus));
    console.log('用户状态信息已保存到本地存储');
}
