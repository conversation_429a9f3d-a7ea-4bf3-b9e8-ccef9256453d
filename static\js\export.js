/**
 * 订单导出页面的JavaScript代码
 */

$(document).ready(function() {
    // 初始化页面
    initExportPage();

    // 绑定事件
    bindEvents();
});

/**
 * 初始化订单导出页面
 */
function initExportPage() {
    console.log('初始化订单导出页面');

    // 设置默认日期
    const today = new Date();
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    $('#startDate').val(formatDate(lastMonth));
    $('#endDate').val(formatDate(today));

    // 显示加载中的提示
    $('#profilesTableBody').html(`
        <tr>
            <td colspan="4" class="py-4 text-center text-gray-500">
                <div class="flex flex-col items-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
                    <span>正在准备加载用户列表...</span>
                </div>
            </td>
        </tr>
    `);

    // 延迟加载Chrome用户列表，让页面先渲染完成
    setTimeout(function() {
        loadProfiles();
    }, 500);
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 全选/取消全选按钮
    $('#selectAllBtn').click(function() {
        $('.profile-checkbox').prop('checked', true);
        updateSelectionStatus();
    });

    $('#unselectAllBtn').click(function() {
        $('.profile-checkbox').prop('checked', false);
        updateSelectionStatus();
    });

    // 全选复选框
    $('#selectAllProfiles, #checkAllProfiles').change(function() {
        const isChecked = $(this).prop('checked');
        $('.profile-checkbox').prop('checked', isChecked);
        updateSelectionStatus();
    });

    // 选择所有正常用户按钮
    $('#selectNormalBtn').click(function() {
        $('.profile-checkbox').prop('checked', false);

        // 选择所有状态为"正常"或"active"的用户
        $('.profile-checkbox').each(function() {
            const row = $(this).closest('tr');
            const statusCell = row.find('td:nth-child(4)');
            const statusText = statusCell.text().trim();

            if (statusText.includes('正常') || statusText.includes('active')) {
                $(this).prop('checked', true);
            }
        });

        updateSelectionStatus();
    });

    // 选择所有登录失效用户按钮
    $('#selectLoginFailedBtn').click(function() {
        $('.profile-checkbox').prop('checked', false);

        // 选择所有状态为"登录失效"或"login_failed"的用户
        $('.profile-checkbox').each(function() {
            const row = $(this).closest('tr');
            const statusCell = row.find('td:nth-child(4)');
            const statusText = statusCell.text().trim();

            if (statusText.includes('登录失效') || statusText.includes('login_failed')) {
                $(this).prop('checked', true);
            }
        });

        updateSelectionStatus();
    });

    // 刷新用户列表按钮
    $('#btnRefreshProfiles').click(function() {
        loadProfiles();
    });

    // 预览按钮点击事件
    $('#previewBtn').click(function() {
        previewOrders();
    });

    // 导出按钮点击事件
    $('#exportBtn').click(function() {
        exportOrders();
    });

    // 停止导出按钮点击事件
    $('#stopExportBtn').click(function() {
        stopExport();
    });

    // 绑定快速选择按钮事件
    $('#quickSelectBtn').click(function() {
        const input = $('#quickSelectInput').val().trim();
        if (!input) {
            showNotification('请输入选择条件', 'info');
            return;
        }

        // 使用QuickSelect模块进行选择
        if (typeof QuickSelect !== 'undefined') {
            QuickSelect.selectByInput(input);
        } else {
            showNotification('快速选择模块未加载', 'error');
        }
    });

    // 绑定快速选择输入框回车事件
    $('#quickSelectInput').keypress(function(e) {
        if (e.which === 13) { // Enter键
            $('#quickSelectBtn').click();
            e.preventDefault();
        }
    });

    // 绑定预设快速选择按钮事件
    $('.quick-select-btn').click(function() {
        const type = $(this).data('type');

        if (typeof QuickSelect !== 'undefined') {
            switch (type) {
                case 'even':
                    QuickSelect.selectEven();
                    break;
                case 'odd':
                    QuickSelect.selectOdd();
                    break;
                case 'first10':
                    QuickSelect.selectFirst(10);
                    break;
                case 'last10':
                    QuickSelect.selectLast(10);
                    break;
            }
        } else {
            showNotification('快速选择模块未加载', 'error');
        }
    });
}

/**
 * 格式化日期为YYYY-MM-DD
 */
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 加载Chrome用户列表
 */
function loadProfiles() {
    // 如果映射模块已加载，使用映射模块加载用户列表
    if (typeof ChromeUsersMapping !== 'undefined') {
        console.log('使用ChromeUsersMapping加载用户列表');
        ChromeUsersMapping.loadProfiles(function(sortedProfiles) {
            // 使用排序后的用户列表更新表格
            updateProfilesTable(sortedProfiles);
        });
    } else {
        console.log('ChromeUsersMapping未加载，使用原始方法加载用户列表');
        // 回退到原始AJAX请求
        $.ajax({
            url: '/api/profiles',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    updateProfilesTable(response.profiles);
                } else {
                    $('#profilesTableBody').html(`<tr><td colspan="4" class="py-4 text-center text-red-500">${response.message || '加载用户列表失败'}</td></tr>`);
                }
            },
            error: function() {
                $('#profilesTableBody').html('<tr><td colspan="4" class="py-4 text-center text-red-500">网络错误，请刷新重试</td></tr>');
            }
        });
    }
}

/**
 * 更新用户列表表格
 */
function updateProfilesTable(profiles) {
    if (!profiles || profiles.length === 0) {
        $('#profilesTableBody').html('<tr><td colspan="4" class="py-4 text-center text-gray-500">没有找到Chrome用户</td></tr>');
        return;
    }

    let html = '';
    profiles.forEach(profile => {
        html += `
        <tr class="border-b border-gray-100 hover:bg-gray-50">
            <td class="py-3 px-4">
                <input type="checkbox" class="profile-checkbox rounded border-gray-300 text-primary focus:ring-primary/20"
                       value="${profile.id}" data-name="${profile.name}" />
            </td>
            <td class="py-3 px-4 text-sm">${profile.id}</td>
            <td class="py-3 px-4 text-sm">${profile.name}</td>
            <td class="py-3 px-4 text-sm">
                <span class="px-2 py-1 rounded-full text-xs ${getStatusClass(profile.status)}">
                    ${profile.status || '未知'}
                </span>
            </td>
        </tr>
        `;
    });

    $('#profilesTableBody').html(html);

    // 绑定复选框事件
    $('.profile-checkbox').change(function() {
        updateSelectionStatus();
    });
}

/**
 * 获取状态对应的样式类
 */
function getStatusClass(status) {
    switch(status) {
        case '正常':
        case 'active':
            return 'bg-green-100 text-green-800';
        case '登录失效':
        case 'login_failed':
            return 'bg-red-100 text-red-800';
        case '处理中':
        case 'processing':
            return 'bg-blue-100 text-blue-800';
        case '密码错误':
        case 'password_error':
            return 'bg-yellow-100 text-yellow-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

/**
 * 更新选择状态
 */
function updateSelectionStatus() {
    // 获取选中的用户数量
    const selectedCount = $('.profile-checkbox:checked').length;
    const totalCount = $('.profile-checkbox').length;

    // 更新全选复选框状态
    $('#selectAllProfiles, #checkAllProfiles').prop('checked', selectedCount > 0 && selectedCount === totalCount);
    $('#selectAllProfiles, #checkAllProfiles').prop('indeterminate', selectedCount > 0 && selectedCount < totalCount);

    // 显示选择结果
    showNotification(`已选择 ${selectedCount}/${totalCount} 个用户`, 'info');

    // 如果存在ChromeUsersMapping模块，调用其更新方法
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.updateSelection();
    }
}

/**
 * 预览订单数据
 */
function previewOrders() {
    const formData = getFormData();
    if (!formData.profile_ids || formData.profile_ids.length === 0) {
        alert('请至少选择一个Chrome用户');
        return;
    }

    $('#previewArea').removeClass('hidden');
    $('#previewTableBody').html(`
        <tr>
            <td colspan="6" class="py-4 text-center text-gray-500">
                <div class="flex flex-col items-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
                    <span>正在加载数据...</span>
                </div>
            </td>
        </tr>
    `);

    $.ajax({
        url: '/api/export_orders/preview',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                updatePreviewTable(response.orders);
            } else {
                $('#previewTableBody').html(`<tr><td colspan="6" class="py-4 text-center text-red-500">${response.message || '加载预览数据失败'}</td></tr>`);
            }
        },
        error: function() {
            $('#previewTableBody').html('<tr><td colspan="6" class="py-4 text-center text-red-500">网络错误，请刷新重试</td></tr>');
        }
    });
}

/**
 * 更新预览表格
 */
function updatePreviewTable(orders) {
    if (!orders || orders.length === 0) {
        $('#previewTableBody').html('<tr><td colspan="6" class="py-4 text-center text-gray-500">没有找到符合条件的订单</td></tr>');
        return;
    }

    let html = '';
    orders.forEach(order => {
        html += `
        <tr class="border-b border-gray-100 hover:bg-gray-50">
            <td class="py-3 px-4 text-sm">${order.order_id || '-'}</td>
            <td class="py-3 px-4 text-sm">${order.title || '-'}</td>
            <td class="py-3 px-4 text-sm">${order.user_name || '-'}</td>
            <td class="py-3 px-4 text-sm">¥${order.amount || '0.00'}</td>
            <td class="py-3 px-4 text-sm">
                <span class="px-2 py-1 rounded-full text-xs ${getOrderStatusClass(order.status)}">
                    ${order.status || '未知'}
                </span>
            </td>
            <td class="py-3 px-4 text-sm">${order.create_time || '-'}</td>
        </tr>
        `;
    });

    $('#previewTableBody').html(html);
}

/**
 * 获取订单状态对应的样式类
 */
function getOrderStatusClass(status) {
    if (!status) return 'bg-gray-100 text-gray-800';

    if (status.includes('待付款')) return 'bg-yellow-100 text-yellow-800';
    if (status.includes('待发货')) return 'bg-blue-100 text-blue-800';
    if (status.includes('待收货')) return 'bg-purple-100 text-purple-800';
    if (status.includes('待评价')) return 'bg-indigo-100 text-indigo-800';
    if (status.includes('已完成')) return 'bg-green-100 text-green-800';
    if (status.includes('已取消')) return 'bg-red-100 text-red-800';

    return 'bg-gray-100 text-gray-800';
}

// 全局变量，用于存储轮询定时器ID
let exportProgressTimer = null;
let lastProgressTimestamp = 0;

/**
 * 导出订单数据
 */
function exportOrders() {
    const formData = getFormData();
    if (!formData.profile_ids || formData.profile_ids.length === 0) {
        alert('请至少选择一个Chrome用户');
        return;
    }

    // 显示停止导出按钮
    $('#stopExportBtn').removeClass('hidden');
    $('#exportBtn').addClass('disabled').prop('disabled', true);

    // 显示导出中的提示
    showNotification('正在导出订单数据，请稍候...', 'info');

    // 显示进度区域
    $('#currentExportStatus').html(`
        <div class="mt-4 p-4 bg-blue-50 text-blue-800 rounded-lg">
            <p class="font-medium">正在导出订单数据...</p>
            <p id="exportProgressText">准备中...</p>
        </div>
    `);

    // 创建FormData对象
    const formDataObj = new FormData();

    // 添加表单字段
    formDataObj.append('start_date', formData.start_date);
    formDataObj.append('end_date', formData.end_date);
    formDataObj.append('order_type', formData.order_type);
    formDataObj.append('export_format', formData.export_format);

    // 添加选中的用户ID
    formData.profile_ids.forEach(id => {
        formDataObj.append('profile_ids', id);
    });

    // 开始轮询导出进度
    startPollingExportProgress();

    // 使用AJAX发送请求
    $.ajax({
        url: '/api/export_orders',
        method: 'POST',
        data: formDataObj,
        processData: false,
        contentType: false,
        success: function(response) {
            // 停止轮询导出进度
            stopPollingExportProgress();

            // 隐藏停止导出按钮
            $('#stopExportBtn').addClass('hidden');
            $('#exportBtn').removeClass('disabled').prop('disabled', false);

            if (response.success) {
                // 显示成功消息
                showNotification(response.message, 'success');

                // 在结果区域显示文件路径
                $('#exportResults').html(`
                    <div class="mt-4 p-4 bg-green-50 text-green-800 rounded-lg">
                        <p class="font-medium">导出成功！</p>
                        <p>文件已保存到: ${response.file_path}</p>
                    </div>
                `);

                // 清空进度区域
                $('#currentExportStatus').html('');
            } else {
                // 显示错误消息
                showNotification(response.message, 'error');

                // 在结果区域显示错误信息
                $('#exportResults').html(`
                    <div class="mt-4 p-4 bg-red-50 text-red-800 rounded-lg">
                        <p class="font-medium">导出失败</p>
                        <p>${response.message}</p>
                    </div>
                `);

                // 清空进度区域
                $('#currentExportStatus').html('');
            }
        },
        error: function(xhr, status, error) {
            // 停止轮询导出进度
            stopPollingExportProgress();

            // 隐藏停止导出按钮
            $('#stopExportBtn').addClass('hidden');
            $('#exportBtn').removeClass('disabled').prop('disabled', false);

            // 显示错误消息
            console.error("导出失败:", xhr.responseText);
            showNotification('导出订单数据失败，请稍后重试', 'error');

            // 清空进度区域
            $('#currentExportStatus').html('');
        }
    });
}

/**
 * 开始轮询导出进度
 */
function startPollingExportProgress() {
    // 先清除可能存在的定时器
    stopPollingExportProgress();

    // 重置时间戳
    lastProgressTimestamp = 0;

    // 立即获取一次进度
    getExportProgress();

    // 设置定时器，每秒获取一次进度
    exportProgressTimer = setInterval(getExportProgress, 1000);
}

/**
 * 停止轮询导出进度
 */
function stopPollingExportProgress() {
    if (exportProgressTimer) {
        clearInterval(exportProgressTimer);
        exportProgressTimer = null;
    }
}

/**
 * 获取导出进度
 */
function getExportProgress() {
    $.ajax({
        url: '/api/export_progress',
        method: 'GET',
        success: function(response) {
            if (response.success && response.message) {
                // 只有当时间戳更新时才更新进度显示
                if (response.timestamp > lastProgressTimestamp) {
                    lastProgressTimestamp = response.timestamp;
                    $('#exportProgressText').text(response.message);
                }
            }
        },
        error: function(xhr, status, error) {
            console.error("获取导出进度失败:", error);
        }
    });
}

/**
 * 停止导出
 */
function stopExport() {
    $.ajax({
        url: '/api/stop_export',
        method: 'POST',
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                // 隐藏停止导出按钮
                $('#stopExportBtn').addClass('hidden');
                $('#exportBtn').removeClass('disabled').prop('disabled', false);

                // 停止轮询导出进度
                stopPollingExportProgress();

                // 更新进度区域
                $('#currentExportStatus').html(`
                    <div class="mt-4 p-4 bg-yellow-50 text-yellow-800 rounded-lg">
                        <p class="font-medium">导出已停止</p>
                        <p>导出过程已被用户手动停止</p>
                    </div>
                `);
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('停止导出失败，请刷新页面重试', 'error');
        }
    });
}

/**
 * 获取表单数据
 */
function getFormData() {
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();
    const orderType = $('input[name="order_type"]:checked').val();
    const exportFormat = $('input[name="export_format"]:checked').val();

    // 获取选中的用户ID
    const profileIds = [];
    $('.profile-checkbox:checked').each(function() {
        profileIds.push($(this).val());
    });

    return {
        start_date: startDate,
        end_date: endDate,
        order_type: orderType,
        export_format: exportFormat,
        profile_ids: profileIds
    };
}
