/**
 * 淘宝商品下单管理系统 - 主JavaScript文件
 * 处理公共功能和通用交互
 */

// 全局变量
let profiles = [];
let selectedProfiles = [];
let config = {};
let currentTask = null;
let taskPollingTimer = null;
let passwordErrors = {}; // 存储密码错误状态

// DOM加载完成后执行
$(document).ready(function() {
    // 初始化操作
    initUI();

    // 初始化Chrome用户映射模块
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.init();
        console.log('Chrome用户映射模块已初始化');
    } else {
        console.warn('Chrome用户映射模块未加载');
    }

    // 加载Chrome用户列表（使用缓存）
    loadProfiles(false);

    // 加载配置
    loadConfig();

    // 绑定事件
    bindEvents();

    // 检查是否有未完成的任务
    checkPendingTasks();

    // 加载密码错误状态
    loadPasswordErrorStatus();

    // 启动密码错误状态监听
    startPasswordErrorListener();
});

/**
 * 初始化UI
 */
function initUI() {
    // 设置活动菜单（已由HTML自行处理）
}

/**
 * 绑定事件处理
 */
function bindEvents() {
    // 刷新用户列表按钮
    $('#refreshProfilesBtn').on('click', function() {
        loadProfiles(true); // 强制刷新
    });

    // 全选/取消全选
    $('#selectAllProfiles').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.profile-checkbox').prop('checked', isChecked);
        updateSelectedProfiles();
    });

    // 保存配置按钮
    $('#saveConfigBtn').on('click', function() {
        saveConfig();
    });

    // 取消待付款订单按钮
    $('#cancelOrdersBtn').on('click', function() {
        cancelWaitPayOrders();
    });
}

/**
 * 取消选中用户的待付款订单
 */
function cancelWaitPayOrders() {
    // 获取选中的用户ID
    const selectedIds = getSelectedProfileIds();

    if (selectedIds.length === 0) {
        showNotification('请选择至少一个Chrome用户', 'error');
        return;
    }

    // 显示确认对话框
    if (!confirm(`确定要取消选中的 ${selectedIds.length} 个用户的所有待付款订单吗？\n此操作不可恢复！`)) {
        return;
    }

    // 显示加载状态
    showNotification('正在处理取消订单请求...', 'info');

    // 发送请求
    $.ajax({
        url: '/api/cancel_orders',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            profile_ids: selectedIds
        }),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // 显示成功消息
                showNotification('取消订单任务已启动', 'success');

                // 保存任务ID到LocalStorage
                currentTask = {
                    id: response.task_id,
                    type: 'cancel_orders',
                    startTime: Date.now(),
                    selectedIds: selectedIds
                };

                // 保存到LocalStorage
                saveCurrentTask();

                // 开始轮询任务状态
                pollTaskStatus(response.task_id);

                // 创建任务状态容器
                createTaskStatusContainer();
            } else {
                showNotification('启动取消订单任务失败: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            showNotification('取消订单请求出错: ' + error, 'error');
        }
    });
}

/**
 * 获取选中的用户ID列表
 */
function getSelectedProfileIds() {
    // 如果映射模块已加载，使用映射模块获取选中的ID
    if (typeof ChromeUsersMapping !== 'undefined') {
        return ChromeUsersMapping.getSelectedIds();
    }

    // 回退到原始方法
    const selectedIds = [];
    $('.profile-checkbox:checked').each(function() {
        selectedIds.push($(this).val() || $(this).data('id'));
    });

    return selectedIds;
}

/**
 * 加载Chrome用户列表
 * @param {boolean} forceRefresh - 是否强制刷新，默认为false
 */
function loadProfiles(forceRefresh = false) {
    // 显示加载状态
    $('#profilesTableBody').html(`
        <tr>
            <td colspan="9" class="py-4 text-center text-gray-500">
                <div class="flex flex-col items-center">
                    <i class="ri-loader-4-line text-3xl mb-2 animate-spin"></i>
                    <span>正在加载Chrome用户列表...</span>
                </div>
            </td>
        </tr>
    `);

    if (forceRefresh) {
        // 强制刷新 - 启动异步刷新任务
        $.ajax({
            url: '/api/refresh_profiles',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({}),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // 显示成功消息
                    showNotification('刷新用户列表任务已启动', 'success');

                    // 保存任务ID到LocalStorage
                    currentTask = {
                        id: response.task_id,
                        type: 'refresh_profiles',
                        startTime: Date.now()
                    };

                    // 保存到LocalStorage
                    saveCurrentTask();

                    // 开始轮询任务状态
                    pollTaskStatus(response.task_id);

                    // 创建任务状态容器
                    createTaskStatusContainer();

                    // 同时加载当前用户列表（不等待刷新完成）
                    loadCurrentProfiles();
                } else {
                    showNotification('启动刷新用户列表任务失败: ' + response.message, 'error');
                    // 回退到直接加载当前用户列表
                    loadCurrentProfiles();
                }
            },
            error: function(xhr, status, error) {
                showNotification('刷新用户列表请求出错: ' + error, 'error');
                // 回退到直接加载当前用户列表
                loadCurrentProfiles();
            }
        });
    } else {
        // 不强制刷新 - 直接加载当前用户列表（使用缓存）
        loadCurrentProfiles();
    }
}

/**
 * 加载当前用户列表（不刷新）
 */
function loadCurrentProfiles() {
    // 如果映射模块已加载，使用映射模块加载用户列表
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.loadProfiles(function(sortedProfiles) {
            // 保存到全局变量
            profiles = sortedProfiles;

            // 渲染用户列表
            renderProfilesTable();

            // 更新头部用户数量
            $('#profile-count').html(`浏览器用户: <span class="font-medium">${profiles.length}</span>`);
            // 更新总数统计卡片
            $('#total-profiles').text(profiles.length);

            // 更新选中的用户列表
            selectedProfiles = ChromeUsersMapping.getSelectedIds();
        });
    } else {
        // 回退到原始方法
        $.ajax({
            url: '/api/profiles',
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    profiles = response.profiles;
                    renderProfilesTable();
                    // 更新头部用户数量
                    $('#profile-count').html(`浏览器用户: <span class="font-medium">${profiles.length}</span>`);
                    // 更新总数统计卡片
                    $('#total-profiles').text(profiles.length);
                } else {
                    showNotification('加载Chrome用户失败: ' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                showNotification('加载Chrome用户出错: ' + error, 'error');
            }
        });
    }
}

/**
 * 渲染Chrome用户表格
 */
function renderProfilesTable() {
    // 如果映射模块已加载，使用映射模块渲染用户列表
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.renderProfilesTable('#profilesTableBody');
        return;
    }

    // 回退到原始方法
    const tableBody = $('#profilesTableBody');
    tableBody.empty();

    if (profiles.length === 0) {
        tableBody.append(`
            <tr>
                <td colspan="5" class="py-4 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <i class="ri-user-search-line text-3xl mb-2"></i>
                        <span>未找到Chrome用户</span>
                    </div>
                </td>
            </tr>
        `);
        return;
    }

    profiles.forEach(function(profile, index) {
        // 获取状态样式
        let statusClass = '';
        let statusBadgeClass = '';

        if (profile.status === '正常' || profile.status === '成功') {
            statusClass = 'text-green-600';
            statusBadgeClass = 'status-success';
        } else if (profile.status === '失败' || profile.status === '错误') {
            statusClass = 'text-red-600';
            statusBadgeClass = 'status-error';
        } else if (profile.status === '处理中') {
            statusClass = 'text-primary';
            statusBadgeClass = 'status-processing';
        } else {
            statusClass = 'text-gray-600';
            statusBadgeClass = 'status-pending';
        }

        const row = $(`
            <tr class="border-t border-gray-100 hover:bg-blue-50/30 transition-colors duration-200">
                <td class="py-3 px-4">
                    <input type="checkbox"
                        id="profile-checkbox-${index}"
                        class="profile-checkbox rounded border-gray-300 text-primary focus:ring-primary/20"
                        value="${profile.id}"
                        data-original-id="${profile.id}"
                        onchange="updateSelectedProfiles()">
                </td>
                <td class="py-3 px-4 text-sm font-medium text-gray-700">
                    <span class="text-gray-500">${index + 1}</span>
                    <span class="text-xs text-gray-400 ml-1">(${profile.id})</span>
                </td>
                <td class="py-3 px-4 text-sm text-gray-700">
                    <div class="flex items-center">
                        <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-xs text-primary font-medium">
                            ${profile.name.charAt(0)}
                        </div>
                        <span class="ml-2">${profile.name}</span>
                    </div>
                </td>
                <td class="py-3 px-4 text-sm text-gray-600 truncate max-w-xs">${profile.path || profile.profile_dir || ''}</td>
                <td class="py-3 px-4">
                    <span class="px-2 py-1 text-xs rounded-full ${statusBadgeClass}">${profile.status}</span>
                </td>
            </tr>
        `);

        tableBody.append(row);
    });

    // 重新检查选中状态
    profiles.forEach(function(profile) {
        if (selectedProfiles.includes(profile.id)) {
            $(`.profile-checkbox[value="${profile.id}"]`).prop('checked', true);
        }
    });
}

/**
 * 更新选中的用户列表
 */
function updateSelectedProfiles() {
    // 如果映射模块已加载，使用映射模块更新选择状态
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.updateSelection();
        selectedProfiles = ChromeUsersMapping.getSelectedIds();
    } else {
        // 回退到原始方法
        selectedProfiles = [];

        $('.profile-checkbox:checked').each(function() {
            selectedProfiles.push($(this).val() || $(this).data('id'));
        });
    }

    // 更新相关UI元素状态
    updateUIState();

    console.log('选中的用户列表已更新:', selectedProfiles);
}

/**
 * 更新UI元素状态
 */
function updateUIState() {
    // 启用/禁用开始按钮
    if ($('#startOrderBtn').length) {
        $('#startOrderBtn').prop('disabled', selectedProfiles.length === 0);

        // 更新按钮样式
        if (selectedProfiles.length === 0) {
            $('#startOrderBtn').addClass('opacity-50 cursor-not-allowed');
        } else {
            $('#startOrderBtn').removeClass('opacity-50 cursor-not-allowed');
        }
    }
}

/**
 * 加载配置
 */
function loadConfig() {
    $.ajax({
        url: '/api/config',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                config = response.config;

                // 更新UI
                if ($('#maxThreads').length) {
                    $('#maxThreads').val(config.max_concurrent_browsers || 3);
                    $('#max-threads').text(config.max_concurrent_browsers || 3);
                }

                if ($('#orderInterval').length) {
                    $('#orderInterval').val(config.order_interval || 60);
                    $('#order-interval').text(config.order_interval || 60);
                }
            }
        }
    });
}

/**
 * 保存配置
 */
function saveConfig() {
    // 收集配置
    const newConfig = {
        max_concurrent_browsers: parseInt($('#maxThreads').val()) || 3,
        order_interval: parseInt($('#orderInterval').val()) || 60
    };

    // 发送请求
    $.ajax({
        url: '/api/config',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(newConfig),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // 更新UI中的显示
                $('#max-threads').text(newConfig.max_concurrent_browsers);
                $('#order-interval').text(newConfig.order_interval);

                showNotification('配置保存成功', 'success');
            } else {
                showNotification('配置保存失败: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            showNotification('配置保存出错: ' + error, 'error');
        }
    });
}

/**
 * 轮询任务状态
 * @param {string} taskId 任务ID
 */
function pollTaskStatus(taskId) {
    // 清除之前的轮询定时器
    if (taskPollingTimer) {
        clearInterval(taskPollingTimer);
    }

    // 设置轮询间隔（毫秒）
    const pollInterval = 2000;

    // 创建轮询函数
    function checkStatus() {
        $.ajax({
            url: `/api/task_status/${taskId}`,
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // 更新UI显示任务状态
                    updateTaskStatusUI(response.status);

                    // 如果任务完成或失败，停止轮询
                    if (response.status.status === 'completed' || response.status.status === 'failed') {
                        // 保存最终结果到LocalStorage
                        saveTaskResult(response.status);

                        // 清除当前任务
                        clearCurrentTask();

                        // 停止轮询
                        clearInterval(taskPollingTimer);
                        taskPollingTimer = null;

                        // 如果成功，刷新用户列表
                        if (response.status.status === 'completed') {
                            setTimeout(loadCurrentProfiles, 1000);
                        }
                    }
                } else {
                    console.error('获取任务状态失败:', response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('获取任务状态请求出错:', error);
            }
        });
    }

    // 立即检查一次
    checkStatus();

    // 开始轮询
    taskPollingTimer = setInterval(checkStatus, pollInterval);
}

/**
 * 更新任务状态UI
 * @param {Object} taskStatus 任务状态对象
 */
function updateTaskStatusUI(taskStatus) {
    // 获取任务状态容器
    const statusContainer = $('#taskStatusContainer');

    // 如果状态容器不存在，创建一个
    if (statusContainer.length === 0) {
        createTaskStatusContainer();
    }

    // 根据任务状态更新内容
    let statusHtml = '';
    let progressPercent = taskStatus.progress || 0;

    if (taskStatus.status === 'pending') {
        statusHtml = `
            <div class="text-blue-600 mb-2">
                <i class="ri-time-line mr-2"></i>任务准备中...
            </div>
        `;
    }
    else if (taskStatus.status === 'running') {
        statusHtml = `
            <div class="text-blue-600 mb-2">
                <i class="ri-loader-4-line animate-spin mr-2"></i>
                ${taskStatus.type === 'cancel_orders' ? '正在处理取消订单任务' : '正在刷新用户列表'} (${progressPercent}%)
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                <div class="bg-blue-600 h-2.5 rounded-full" style="width: ${progressPercent}%"></div>
            </div>
            <div class="text-sm text-gray-600">${taskStatus.message || ''}</div>
        `;
    }
    else if (taskStatus.status === 'completed') {
        const result = taskStatus.result || {};
        statusHtml = `
            <div class="text-green-600 mb-2">
                <i class="ri-check-line mr-2"></i>
                ${taskStatus.type === 'cancel_orders' ?
                    `取消订单任务已完成: ${result.success_count || 0}个成功, ${result.fail_count || 0}个失败` :
                    `刷新用户列表完成: 共${result.profile_count || 0}个用户`}
            </div>
        `;

        // 如果是取消订单任务，显示详细结果
        if (taskStatus.type === 'cancel_orders' && result.results) {
            statusHtml += `<div class="text-sm text-gray-600 mt-2">详细结果:</div>`;
            statusHtml += `<div class="max-h-40 overflow-y-auto text-sm text-gray-600 border border-gray-200 rounded p-2 mt-1">`;

            result.results.forEach(function(userResult) {
                statusHtml += `<div class="mb-1">- ${userResult.profile_name || userResult.profile_id}: `;
                if (userResult.message) {
                    statusHtml += userResult.message;
                } else if (userResult.orders) {
                    const successCount = userResult.orders.filter(o => o.success).length;
                    const totalCount = userResult.orders.length;
                    statusHtml += `成功取消 ${successCount}/${totalCount} 个订单`;
                }
                statusHtml += `</div>`;
            });

            statusHtml += `</div>`;
        }
    }
    else if (taskStatus.status === 'failed') {
        statusHtml = `
            <div class="text-red-600 mb-2">
                <i class="ri-error-warning-line mr-2"></i>
                ${taskStatus.type === 'cancel_orders' ? '取消订单任务失败' : '刷新用户列表失败'}: ${taskStatus.error || '未知错误'}
            </div>
        `;
    }

    // 更新状态容器内容
    $('#taskStatusContainer').html(statusHtml);
}

/**
 * 创建任务状态容器
 */
function createTaskStatusContainer() {
    // 如果容器已存在，不重复创建
    if ($('#taskStatusContainer').length > 0) {
        return;
    }

    // 创建容器
    const container = $(`
        <div id="taskStatusContainer" class="glass-effect hover-card rounded-lg shadow-sm mb-6 p-4">
            <div class="text-blue-600">
                <i class="ri-loader-4-line animate-spin mr-2"></i>
                正在初始化任务...
            </div>
        </div>
    `);

    // 插入到页面中
    $('main .glass-effect').first().after(container);
}

/**
 * 保存当前任务到LocalStorage
 */
function saveCurrentTask() {
    if (currentTask) {
        localStorage.setItem('currentTask', JSON.stringify(currentTask));
    }
}

/**
 * 清除当前任务
 */
function clearCurrentTask() {
    currentTask = null;
    localStorage.removeItem('currentTask');
}

/**
 * 保存任务结果到LocalStorage
 */
function saveTaskResult(result) {
    // 保存最近的任务结果
    localStorage.setItem('lastTaskResult', JSON.stringify(result));
    localStorage.setItem('lastTaskUpdateTime', Date.now());
}

/**
 * 检查是否有未完成的任务
 */
function checkPendingTasks() {
    // 检查LocalStorage中是否有未完成的任务
    const savedTask = localStorage.getItem('currentTask');

    if (savedTask) {
        try {
            currentTask = JSON.parse(savedTask);

            // 检查任务是否过期（24小时）
            const taskAge = Date.now() - currentTask.startTime;
            if (taskAge > 24 * 60 * 60 * 1000) {
                // 任务过期，清除
                clearCurrentTask();
                return;
            }

            // 检查任务状态
            $.ajax({
                url: `/api/task_status/${currentTask.id}`,
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // 如果任务仍在进行中，继续轮询
                        if (response.status.status === 'pending' || response.status.status === 'running') {
                            // 创建任务状态容器
                            createTaskStatusContainer();

                            // 更新UI
                            updateTaskStatusUI(response.status);

                            // 继续轮询
                            pollTaskStatus(currentTask.id);
                        }
                        // 如果任务已完成，显示结果
                        else if (response.status.status === 'completed' || response.status.status === 'failed') {
                            // 创建任务状态容器
                            createTaskStatusContainer();

                            // 更新UI
                            updateTaskStatusUI(response.status);

                            // 保存结果并清除当前任务
                            saveTaskResult(response.status);
                            clearCurrentTask();
                        }
                    } else {
                        // 任务不存在，清除
                        clearCurrentTask();
                    }
                },
                error: function() {
                    // 请求失败，检查是否有保存的结果
                    checkLastTaskResult();
                }
            });
        } catch (e) {
            console.error('解析保存的任务失败:', e);
            clearCurrentTask();
        }
    } else {
        // 检查是否有最近完成的任务结果
        checkLastTaskResult();
    }
}

/**
 * 检查是否有最近完成的任务结果
 */
function checkLastTaskResult() {
    const lastResult = localStorage.getItem('lastTaskResult');
    const lastUpdateTime = localStorage.getItem('lastTaskUpdateTime');

    // 如果有结果且更新时间在24小时内，显示结果
    if (lastResult && lastUpdateTime && (Date.now() - lastUpdateTime < 24 * 60 * 60 * 1000)) {
        try {
            const taskStatus = JSON.parse(lastResult);

            // 创建任务状态容器
            createTaskStatusContainer();

            // 更新UI
            updateTaskStatusUI(taskStatus);
        } catch (e) {
            console.error('解析保存的任务结果失败:', e);
            localStorage.removeItem('lastTaskResult');
            localStorage.removeItem('lastTaskUpdateTime');
        }
    }
}

/**
 * 加载密码错误状态
 */
function loadPasswordErrorStatus() {
    // 从LocalStorage加载密码错误状态
    const savedErrors = localStorage.getItem('passwordErrors');
    if (savedErrors) {
        try {
            passwordErrors = JSON.parse(savedErrors);

            // 检查是否有过期的错误状态（24小时）
            const currentTime = Date.now();
            let hasChanges = false;

            Object.keys(passwordErrors).forEach(profileId => {
                const error = passwordErrors[profileId];
                if (currentTime - error.timestamp > 24 * 60 * 60 * 1000) {
                    // 删除过期的错误状态
                    delete passwordErrors[profileId];
                    hasChanges = true;
                }
            });

            // 如果有变更，保存回LocalStorage
            if (hasChanges) {
                savePasswordErrorStatus();
            }

            // 更新UI显示
            updatePasswordErrorUI();
        } catch (e) {
            console.error('解析密码错误状态失败:', e);
            passwordErrors = {};
            localStorage.removeItem('passwordErrors');
        }
    }
}

/**
 * 保存密码错误状态到LocalStorage
 */
function savePasswordErrorStatus() {
    localStorage.setItem('passwordErrors', JSON.stringify(passwordErrors));
}

/**
 * 更新密码错误UI显示
 */
function updatePasswordErrorUI() {
    // 遍历所有用户，更新状态显示
    Object.keys(passwordErrors).forEach(profileId => {
        const error = passwordErrors[profileId];

        // 查找用户行
        const checkbox = $(`.profile-checkbox[value="${profileId}"]`);
        if (checkbox.length > 0) {
            const row = checkbox.closest('tr');
            const statusCell = row.find('td:nth-child(5)');

            // 更新状态显示
            statusCell.html(`
                <span class="px-2 py-1 text-xs rounded-full status-error">密码错误</span>
            `);
        }
    });
}

/**
 * 启动密码错误状态监听
 */
function startPasswordErrorListener() {
    // 每10秒检查一次是否有新的密码错误状态
    setInterval(function() {
        $.ajax({
            url: '/api/password_error_status',
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success && response.errors) {
                    // 检查是否有新的错误
                    let hasNewErrors = false;

                    response.errors.forEach(error => {
                        if (!passwordErrors[error.profile_id] ||
                            passwordErrors[error.profile_id].timestamp < error.timestamp) {
                            // 添加或更新错误状态
                            passwordErrors[error.profile_id] = {
                                timestamp: error.timestamp,
                                message: error.message || '密码错误'
                            };
                            hasNewErrors = true;
                        }
                    });

                    // 如果有新的错误，更新UI和保存状态
                    if (hasNewErrors) {
                        savePasswordErrorStatus();
                        updatePasswordErrorUI();
                    }
                }
            }
        });
    }, 10000);
}

/**
 * 处理密码错误通知
 * @param {string} profileId 用户ID
 * @param {string} message 错误消息
 */
function handlePasswordError(profileId, message) {
    // 添加或更新错误状态
    passwordErrors[profileId] = {
        timestamp: Date.now(),
        message: message || '密码错误'
    };

    // 保存状态
    savePasswordErrorStatus();

    // 更新UI
    updatePasswordErrorUI();

    // 显示通知
    showNotification(`用户 ${profileId} 支付密码错误`, 'error');
}

/**
 * 显示通知消息 - 仅当showNotification未在HTML中定义时使用
 */
if (typeof window.showNotification !== 'function') {
    window.showNotification = function(message, type) {
        const notification = $('#notification');

        // 设置消息
        $('#notificationMessage').text(message);

        // 设置样式
        notification.removeClass('border-green-500 border-red-500 border-blue-500');
        const iconContainer = notification.find('div:first-child');
        iconContainer.removeClass('text-green-500 text-red-500 text-blue-500');

        // 更新 icon
        const icon = iconContainer.find('i');

        if (type === 'error') {
            notification.addClass('border-red-500');
            iconContainer.addClass('text-red-500');
            icon.attr('class', 'ri-error-warning-line ri-lg');
        } else if (type === 'info') {
            notification.addClass('border-blue-500');
            iconContainer.addClass('text-blue-500');
            icon.attr('class', 'ri-information-line ri-lg');
        } else { // success
            notification.addClass('border-green-500');
            iconContainer.addClass('text-green-500');
            icon.attr('class', 'ri-check-line ri-lg');
        }

        // 显示通知
        notification.css({
            'opacity': '1',
            'transform': 'translateY(0)'
        });

        // 自动隐藏
        setTimeout(() => {
            notification.css({
                'opacity': '0',
                'transform': 'translateY(-20px)'
            });
        }, 3000);
    };
}