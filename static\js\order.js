/**
 * 淘宝商品下单管理系统 - 下单页面JavaScript
 * 处理下单功能的交互
 */

// 全局变量
let isTaskRunning = false;
let totalTasks = 0;
let completedTasks = 0;
let pendingTasks = 0;
let refreshInterval;

// 页面加载完成后执行
$(document).ready(function() {
    console.log('order.js: document ready');

    // 初始化页面
    initPage();

    // 绑定事件
    bindEvents();

    // 定期刷新任务状态
    refreshInterval = setInterval(updateTaskStatus, 2000);

    // 确保所有控件始终可用
    $(document).on('DOMNodeInserted', function(e) {
        // 当有新元素插入DOM时，确保所有控件都是可用的
        $('.order-links-input').prop('disabled', false).removeClass('bg-gray-100');
        $('.profile-checkbox').prop('disabled', false);
        $('#randomOrderCheckTop').prop('disabled', false);
        $('#maxThreads').prop('disabled', false);
        $('#startMultiTaskBtn').prop('disabled', false);
    });

    // 将函数暴露到全局作用域，供HTML中的函数调用
    window.startMultiThreadTask = startMultiThreadTask;
    window.stopAllProcesses = stopAllProcesses;
    window.clearAllOrderLinks = clearAllOrderLinks;
    window.clearAllTasks = clearAllTasks;
    console.log('已将startMultiThreadTask、stopAllProcesses、clearAllOrderLinks和clearAllTasks函数暴露到全局作用域');
});

// 初始化页面
function initPage() {
    // 加载Chrome用户配置
    loadProfiles();

    // 初始化UI状态
    updateUIState();

    // 添加快速选择功能
    setTimeout(addQuickSelectButton, 2000);
}

// 绑定事件
function bindEvents() {
    // 底部停止任务按钮已移除

    // 停止所有进程按钮点击事件
    $('#stopTaskBtn').click(function() {
        stopAllProcesses();
    });

    // 注意：启动多线程任务按钮的事件绑定已移至HTML中的onclick属性
    // 这样可以避免多次绑定导致的多次触发问题

    // 随机顺序复选框变化事件 - 保留功能但不显示
    $('#randomOrderCheckTop').change(function() {
        // 随机顺序复选框变化事件
        console.log('随机顺序设置:', $(this).prop('checked'));
    });

    // 刷新用户按钮点击事件
    $('#refreshProfilesBtn').click(function() {
        refreshProfiles();
    });

    // 全选/取消全选
    $('#selectAllProfiles').change(function() {
        const isChecked = $(this).prop('checked');
        $('.profile-checkbox').prop('checked', isChecked);
    });
}

// 加载Chrome用户配置
function loadProfiles() {
    // 显示加载状态
    $('#profilesTableBody').html(`
        <tr>
            <td colspan="5" class="py-4 text-center text-gray-500">
                <div class="flex flex-col items-center">
                    <i class="ri-loader-4-line text-3xl mb-2 animate-spin"></i>
                    <span>正在加载Chrome用户列表...</span>
                </div>
            </td>
        </tr>
    `);

    // 如果映射模块已加载，使用映射模块加载用户列表
    if (typeof ChromeUsersMapping !== 'undefined') {
        console.log('使用ChromeUsersMapping加载用户列表');
        ChromeUsersMapping.loadProfiles(function(sortedProfiles) {
            renderProfilesTable(sortedProfiles);
            // 更新头部用户数量
            if ($('#profile-count').length) {
                $('#profile-count').html(`浏览器用户: <span class="font-medium">${sortedProfiles.length}</span>`);
            }
            showNotification('success', '加载成功', `成功加载 ${sortedProfiles.length} 个Chrome用户`);
        });
    } else {
        console.log('ChromeUsersMapping未加载，使用原始方法加载用户列表');
        // 使用与main.js相同的API端点
        $.ajax({
            url: '/api/profiles',
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    renderProfilesTable(response.profiles);
                    // 更新头部用户数量
                    if ($('#profile-count').length) {
                        $('#profile-count').html(`浏览器用户: <span class="font-medium">${response.profiles.length}</span>`);
                    }
                    showNotification('success', '加载成功', `成功加载 ${response.profiles.length} 个Chrome用户`);
                } else {
                    showNotification('error', '加载失败', response.message || '未知错误');
                    // 回退到静态数据
                    loadProfilesViaAjax();
                }
            },
            error: function(xhr, status, error) {
                console.error('加载Chrome用户出错:', error);
                showNotification('error', '加载失败', error);
                // 回退到静态数据
                loadProfilesViaAjax();
            }
        });
    }
}

// 通过AJAX加载用户配置(作为备用方法)
function loadProfilesViaAjax() {
    // 由于没有对应的API端点，使用静态数据作为备用
    console.log('使用静态数据作为备用');
    const mockProfiles = [
        {
            id: 'test-user-1',
            name: '测试用户1',
            path: 'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 1',
            status: '可用'
        },
        {
            id: 'test-user-2',
            name: '测试用户2',
            path: 'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 2',
            status: '可用'
        }
    ];
    renderProfilesTable(mockProfiles);
}

// 刷新用户配置
function refreshProfiles() {
    // 显示加载状态
    $('#profilesTableBody').html(`
        <tr>
            <td colspan="5" class="py-4 text-center text-gray-500">
                <div class="flex flex-col items-center">
                    <i class="ri-loader-4-line text-3xl mb-2 animate-spin"></i>
                    <span>正在刷新Chrome用户列表...</span>
                </div>
            </td>
        </tr>
    `);

    // 使用与main.js相同的API端点
    $.ajax({
        url: '/api/profiles',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                renderProfilesTable(response.profiles);
                showNotification('success', '刷新成功', '已更新Chrome用户配置列表');
            } else {
                showNotification('error', '刷新失败', response.message || '未知错误');
                // 回退到静态数据
                refreshProfilesViaAjax();
            }
        },
        error: function(xhr, status, error) {
            console.error('刷新Chrome用户出错:', error);
            showNotification('error', '刷新失败', error);
            // 回退到静态数据
            refreshProfilesViaAjax();
        }
    });
}

// 通过AJAX刷新用户配置(作为备用方法)
function refreshProfilesViaAjax() {
    // 由于没有对应的API端点，使用静态数据作为备用
    console.log('使用静态数据作为备用');
    const mockProfiles = [
        {
            id: 'test-user-1',
            name: '测试用户1',
            path: 'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 1',
            status: '可用'
        },
        {
            id: 'test-user-2',
            name: '测试用户2',
            path: 'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 2',
            status: '可用'
        },
        {
            id: 'test-user-3',
            name: '测试用户3 (新)',
            path: 'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 3',
            status: '可用'
        }
    ];
    renderProfilesTable(mockProfiles);
    showNotification('success', '刷新成功', '已更新Chrome用户配置列表');
}

// 渲染用户配置表格
function renderProfilesTable(profiles) {
    if (!profiles || profiles.length === 0) {
        $('#profilesTableBody').html(`
            <tr>
                <td colspan="6" class="py-4 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <i class="ri-user-search-line text-3xl mb-2"></i>
                        <span>未找到Chrome用户配置</span>
                    </div>
                </td>
            </tr>
        `);
        return;
    }

    // 修改表头，添加下单链接列
    const tableHead = $('thead tr');
    if (tableHead.find('th').length === 5) {
        tableHead.append('<th class="py-3 px-4 font-medium text-gray-600 text-sm">下单链接</th>');
    }

    let html = '';
    profiles.forEach(function(profile, index) {
        // 获取状态样式
        let statusClass = '';
        let statusBadgeClass = '';
        let statusText = profile.status;
        let statusTooltip = '';

        if (profile.status === '正常' || profile.status === '成功' || profile.status === '可用') {
            statusClass = 'text-green-600';
            statusBadgeClass = 'bg-green-100 text-green-800';
        } else if (profile.status === '失败' || profile.status === '错误') {
            statusClass = 'text-red-600';
            statusBadgeClass = 'bg-red-100 text-red-800';
            // 如果有错误信息，添加到tooltip
            if (profile.status_data && profile.status_data.error_message) {
                statusTooltip = profile.status_data.error_message;
            }
        } else if (profile.status === '处理中') {
            statusClass = 'text-primary';
            statusBadgeClass = 'bg-blue-100 text-blue-800';
        } else if (profile.status === '登录失效') {
            statusClass = 'text-orange-600';
            statusBadgeClass = 'bg-orange-100 text-orange-800';
            // 如果有错误信息，添加到tooltip
            if (profile.status_data && profile.status_data.error_message) {
                statusTooltip = profile.status_data.error_message;
            }
        } else {
            statusClass = 'text-gray-600';
            statusBadgeClass = 'bg-gray-100 text-gray-800';
        }

        // 使用映射模块获取显示索引
        let displayIndex = index;
        if (typeof ChromeUsersMapping !== 'undefined') {
            displayIndex = ChromeUsersMapping.getIndexById(profile.id) || index;
        }

        html += `
        <tr class="${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} transition duration-300 ease-in-out hover:bg-yellow-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                <div class="flex items-center">
                    <input type="checkbox" id="profile-checkbox-${displayIndex}" class="profile-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" value="${profile.id}" data-original-id="${profile.id}">
                    <label for="profile-checkbox-${displayIndex}" class="ml-2 text-sm font-medium text-gray-900">
                        <span class="text-gray-500">${displayIndex + 1}.</span>
                        <div class="flex flex-col">
                            <span>${profile.name || profile.id}</span>
                            ${profile.taobao_username ? `<span class="text-xs text-blue-500">${profile.taobao_username}</span>` : ''}
                        </div>
                    </label>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${profile.id}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div class="flex items-center">
                    <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-xs text-primary font-medium">
                        ${(profile.name || profile.id).charAt(0)}
                    </div>
                    <div class="ml-2 flex flex-col">
                        <span>${profile.name || profile.id}</span>
                        ${profile.taobao_username ? `<span class="text-xs text-blue-500">${profile.taobao_username}</span>` : ''}
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${profile.profile_dir || profile.path || '未知'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusBadgeClass}" ${statusTooltip ? `title="${statusTooltip}"` : ''}>
                    ${statusText}
                </span>
                ${statusTooltip ? `<span class="ml-1 text-xs text-gray-400">(${statusTooltip})</span>` : ''}
            </td>
            <td class="px-6 py-4 text-sm text-gray-500">
                <textarea id="order-links-${profile.id}" class="order-links-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" rows="2" placeholder="输入下单链接，多个链接请用回车分隔"></textarea>
            </td>
        </tr>
        `;
    });

    $('#profilesTableBody').html(html);

    // 绑定复选框变更事件
    $('.profile-checkbox').change(function() {
        // 如果映射模块已加载，更新选择状态
        if (typeof ChromeUsersMapping !== 'undefined') {
            ChromeUsersMapping.updateSelection();
        }
    });

    // 如果映射模块已加载，恢复选择状态
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.restoreSelection();
    }
}

// 清空所有用户的下单链接
function clearAllOrderLinks() {
    console.log('清空所有用户的下单链接');
    // 查找所有下单链接输入框并清空
    $('.order-links-input').val('');
}

// 清空所有任务（清空下单链接和取消用户选择）
function clearAllTasks() {
    console.log('清空所有任务');

    // 清空所有下单链接
    clearAllOrderLinks();

    // 取消所有用户选择
    $('.profile-checkbox').prop('checked', false);

    // 如果使用了映射模块，更新选择状态
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.updateSelection();
    }

    // 更新全选复选框状态
    $('#selectAllProfiles').prop('checked', false);

    // 显示成功通知
    showNotification('success', '已清空所有任务', '已清空所有下单链接并取消所有用户选择');
}

// 启动多线程任务
function startMultiThreadTask() {
    console.log('startMultiThreadTask函数被调用');
    // 获取选中的用户
    let profileIds = [];
    let profileOrderUrls = {}; // 存储每个用户的下单链接

    // 如果映射模块已加载，使用映射模块获取选中的用户
    if (typeof ChromeUsersMapping !== 'undefined') {
        profileIds = ChromeUsersMapping.getSelectedIds();
    } else {
        // 回退到原始方法
        $('.profile-checkbox:checked').each(function() {
            profileIds.push($(this).val());
        });
    }

    if (profileIds.length === 0) {
        showNotification('error', '无法启动任务', '请至少选择一个Chrome用户');
        return;
    }

    // 获取每个选中用户的下单链接
    let hasLinks = false;
    profileIds.forEach(profileId => {
        const linksTextarea = $(`#order-links-${profileId}`);
        if (linksTextarea.length) {
            const links = linksTextarea.val().trim();
            if (links) {
                const urls = links.split('\n')
                    .map(link => link.trim())
                    .filter(link => link !== '');

                if (urls.length > 0) {
                    profileOrderUrls[profileId] = urls;
                    hasLinks = true;
                }
            }
        }
    });

    if (!hasLinks) {
        showNotification('error', '无法启动任务', '请至少为一个选中的用户输入下单链接');
        return;
    }

    // 获取是否随机顺序
    const shuffle = $('#randomOrderCheckTop').prop('checked');

    // 计算总链接数
    let totalLinkCount = 0;
    Object.values(profileOrderUrls).forEach(urls => {
        totalLinkCount += urls.length;
    });

    // 显示确认对话框
    if (confirm(`确定要启动多线程下单任务吗？\n将使用 ${profileIds.length} 个Chrome用户处理 ${totalLinkCount} 个订单链接。\n注意：系统将先结束所有Chrome进程，请保存好其他Chrome窗口中的工作。`)) {
        // 更新UI状态
        isTaskRunning = true;
        updateUIState();

        // 确保所有控件始终可用
        $('.order-links-input').prop('disabled', false).removeClass('bg-gray-100');
        $('.profile-checkbox').prop('disabled', false);
        $('#randomOrderCheckTop').prop('disabled', false);
        $('#maxThreads').prop('disabled', false);
        $('#startMultiTaskBtn').prop('disabled', false);

        // 显示任务状态区域
        $('#runningTasksContainer').fadeIn();
        $(document).trigger('taskStatusChanged', [true]);

        // 更新任务数量
        totalTasks = totalLinkCount;
        pendingTasks = totalTasks;
        completedTasks = 0;
        updateTaskProgress();

        console.log('启动多线程任务:', {
            profileIds: profileIds,
            profileOrderUrls: profileOrderUrls,
            totalLinkCount: totalLinkCount,
            shuffle: shuffle
        });

        try {
            // 获取最大线程数
            const maxThreads = parseInt($('#maxThreads').val()) || 1;

            // 检查是否能使用PyWebView API
            if (window.pywebview && window.pywebview.api) {
                console.log('使用PyWebView API启动任务');

                // 获取自动付款选项状态
                const autoPayment = $('#autoPaymentCheck').prop('checked');

                // 创建任务配置
                const taskConfig = {
                    profileIds: profileIds,
                    profileOrderUrls: profileOrderUrls,
                    shuffle: shuffle,
                    multiThread: true,
                    maxThreads: maxThreads,
                    autoPayment: autoPayment
                };

                window.pywebview.api.start_order_task_with_config(taskConfig)
                    .then(result => {
                        console.log('启动任务结果:', result);
                        if (result.success) {
                            // 任务启动成功后清空所有下单链接
                            clearAllOrderLinks();
                            showNotification('success', '任务启动成功', '已开始处理订单');
                        } else {
                            isTaskRunning = false;
                            updateUIState();
                            showNotification('error', '任务启动失败', result.message || '未知错误');
                        }
                    })
                    .catch(error => {
                        console.error('启动任务API调用失败:', error);
                        isTaskRunning = false;
                        updateUIState();
                        showNotification('error', '任务启动失败', error.toString());
                        // 不使用回退机制，直接显示错误
                        alert('无法启动Chrome浏览器，请检查系统设置或联系管理员。');
                    });
            } else {
                // PyWebView API不可用
                console.error('PyWebView API不可用');
                isTaskRunning = false;
                updateUIState();
                showNotification('error', '系统错误', 'PyWebView API不可用，无法启动浏览器');
                alert('系统错误：无法启动浏览器。请联系管理员。');
            }
        } catch (error) {
            console.error('启动任务时出错:', error);
            isTaskRunning = false;
            updateUIState();
            showNotification('error', '任务启动失败', error.toString());
            // 不使用回退机制，直接显示错误
            alert('启动Chrome浏览器时出错：' + error.toString());
        }
    }
}

// 此函数已不再使用，保留函数签名以避免引用错误
function startTaskViaAjax(profileIds, profileOrderUrls, shuffle, multiThread) {
    console.error('startTaskViaAjax函数已不再使用');
    alert('系统错误：无法启动浏览器。请联系管理员。');

    // 重置UI状态
    isTaskRunning = false;
    updateUIState();
}

// 停止所有进程
function stopAllProcesses() {
    try {
        // 检查是否能使用PyWebView API
        if (window.pywebview && window.pywebview.api) {
            console.log('使用PyWebView API停止所有进程');
            window.pywebview.api.kill_all_processes()
                .then(result => {
                    console.log('停止所有进程结果:', result);
                    if (result.success) {
                        isTaskRunning = false;
                        updateUIState();
                        // 确保所有控件始终可用
                        $('.order-links-input').prop('disabled', false).removeClass('bg-gray-100');
                        $('.profile-checkbox').prop('disabled', false);
                        $('#randomOrderCheckTop').prop('disabled', false);
                        $('#maxThreads').prop('disabled', false);
                        $('#startMultiTaskBtn').prop('disabled', false);
                        showNotification('success', '任务已停止', '所有进程已终止');
                    } else {
                        // 确保所有控件始终可用
                        $('.order-links-input').prop('disabled', false).removeClass('bg-gray-100');
                        $('.profile-checkbox').prop('disabled', false);
                        $('#randomOrderCheckTop').prop('disabled', false);
                        $('#maxThreads').prop('disabled', false);
                        $('#startMultiTaskBtn').prop('disabled', false);
                        showNotification('error', '停止进程失败', result.message || '未知错误');
                    }
                })
                .catch(error => {
                    console.error('停止所有进程API调用失败:', error);
                    // 确保所有控件始终可用
                    $('.order-links-input').prop('disabled', false).removeClass('bg-gray-100');
                    $('.profile-checkbox').prop('disabled', false);
                    $('#randomOrderCheckTop').prop('disabled', false);
                    $('#maxThreads').prop('disabled', false);
                    $('#startMultiTaskBtn').prop('disabled', false);
                    showNotification('error', '停止进程失败', error.toString());
                });
        } else {
            console.error('PyWebView API不可用');
            // 确保所有控件始终可用
            $('.order-links-input').prop('disabled', false).removeClass('bg-gray-100');
            $('.profile-checkbox').prop('disabled', false);
            $('#randomOrderCheckTop').prop('disabled', false);
            $('#maxThreads').prop('disabled', false);
            $('#startMultiTaskBtn').prop('disabled', false);
            showNotification('error', '系统错误', 'PyWebView API不可用，无法停止进程');
        }
    } catch (error) {
        console.error('停止所有进程时出错:', error);
        // 确保所有控件始终可用
        $('.order-links-input').prop('disabled', false).removeClass('bg-gray-100');
        $('.profile-checkbox').prop('disabled', false);
        $('#randomOrderCheckTop').prop('disabled', false);
        $('#maxThreads').prop('disabled', false);
        $('#startMultiTaskBtn').prop('disabled', false);
        showNotification('error', '停止进程失败', error.toString());
    }
}

// 停止任务
function stopOrderTask() {
    if (!isTaskRunning) return;

    if (confirm('确定要停止当前下单任务吗？')) {
        try {
            // 检查是否能使用PyWebView API
            if (window.pywebview && window.pywebview.api) {
                window.pywebview.api.stop_order_task()
                    .then(result => {
                        if (result.success) {
                            isTaskRunning = false;
                            updateUIState();
                            showNotification('success', '任务已停止', '所有下单任务已停止');
                        } else {
                            showNotification('error', '停止任务失败', result.message || '未知错误');
                        }
                    })
                    .catch(error => {
                        console.error('停止任务API调用失败:', error);
                        // 回退到AJAX请求
                        stopTaskViaAjax();
                    });
            } else {
                // 回退到AJAX请求
                stopTaskViaAjax();
            }
        } catch (error) {
            console.error('停止任务时出错:', error);
            // 回退到AJAX请求
            stopTaskViaAjax();
        }
    }
}

// 通过AJAX停止任务(作为备用方法)
function stopTaskViaAjax() {
    // 由于没有对应的API端点，模拟成功响应
    console.log('模拟停止任务成功响应');

    // 更新状态
    isTaskRunning = false;
    updateUIState();

    // 显示成功通知
    showNotification('success', '任务已停止', '所有下单任务已停止');
}

// 更新任务状态
function updateTaskStatus() {
    if (!isTaskRunning) return;

    try {
        // 检查是否能使用PyWebView API
        if (window.pywebview && window.pywebview.api) {
            window.pywebview.api.get_order_status()
                .then(result => {
                    if (result.success) {
                        updateTaskStatusUI(result.data);
                    }
                })
                .catch(error => {
                    console.error('获取任务状态API调用失败:', error);
                    // 回退到AJAX请求
                    updateTaskStatusViaAjax();
                });
        } else {
            // 回退到AJAX请求
            updateTaskStatusViaAjax();
        }
    } catch (error) {
        console.error('更新任务状态时出错:', error);
        // 回退到AJAX请求
        updateTaskStatusViaAjax();
    }
}

// 通过AJAX更新任务状态(作为备用方法)
function updateTaskStatusViaAjax() {
    // 由于没有对应的API端点，模拟任务状态数据
    console.log('模拟任务状态数据');

    // 创建模拟数据
    const mockData = {
        is_running: isTaskRunning,
        total: totalTasks,
        completed: completedTasks,
        pending: pendingTasks,
        active_tasks: [
            {
                user: '测试用户1',
                status: '处理中',
                current_url: 'https://detail.tmall.com/item.htm?id=123456789',
                progress: completedTasks / totalTasks,
                completed: completedTasks,
                total: totalTasks,
                last_update: new Date().toISOString(),
                estimated_time: 120
            }
        ]
    };

    // 更新UI
    updateTaskStatusUI(mockData);
}

// 更新任务状态UI
function updateTaskStatusUI(data) {
    // 如果任务已经完成或已停止
    if (data.is_running === false && isTaskRunning) {
        isTaskRunning = false;
        updateUIState();
        showNotification('success', '任务已完成', '所有下单任务已完成');
    }

    // 确保所有控件始终可用
    $('.order-links-input').prop('disabled', false).removeClass('bg-gray-100');
    $('.profile-checkbox').prop('disabled', false);
    $('#randomOrderCheckTop').prop('disabled', false);
    $('#maxThreads').prop('disabled', false);
    $('#startMultiTaskBtn').prop('disabled', false);

    // 更新任务统计信息
    totalTasks = data.total || totalTasks;
    completedTasks = data.completed || 0;
    pendingTasks = data.pending || 0;

    // 更新任务进度
    updateTaskProgress();

    // 更新活跃任务列表
    updateActiveTasks(data.active_tasks || []);
}

// 更新活跃任务列表
function updateActiveTasks(tasks) {
    const container = $('#runningTasksList');
    container.empty();

    if (tasks.length === 0) {
        container.html('<div class="text-center text-gray-500 py-4">没有正在运行的任务</div>');
        return;
    }

    tasks.forEach(task => {
        const progressPercent = task.progress ? Math.round(task.progress * 100) : 0;
        const statusClass = getStatusColorClass(task.status);
        const timeAgo = task.last_update ? getTimeAgo(task.last_update) : '刚刚';
        const estimatedTime = task.estimated_time ? formatTime(task.estimated_time) : '未知';

        const card = `
        <div class="bg-white rounded-lg shadow-md p-4 mb-4 border-l-4 ${statusClass} transition duration-300 ease-in-out hover:shadow-lg">
            <div class="flex justify-between items-start mb-2">
                <h3 class="text-lg font-semibold">${task.user || '未知用户'}</h3>
                <span class="px-2 py-1 text-xs rounded-full ${statusClass.replace('border-', 'bg-').replace('-500', '-100')} ${statusClass.replace('border-', 'text-')}">
                    ${task.status || '未知状态'}
                </span>
            </div>
            <div class="mb-2">
                <p class="text-sm text-gray-600 truncate" title="${task.current_url || ''}">
                    ${task.current_url ? '正在处理: ' + task.current_url : '等待任务...'}
                </p>
                <p class="text-xs text-gray-500">
                    最后更新: ${timeAgo} | 预计剩余时间: ${estimatedTime}
                </p>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="bg-blue-600 h-2.5 rounded-full" style="width: ${progressPercent}%"></div>
            </div>
            <div class="flex justify-between mt-1">
                <span class="text-xs text-gray-500">进度: ${progressPercent}%</span>
                <span class="text-xs text-gray-500">已完成: ${task.completed || 0}/${task.total || 0}</span>
            </div>
        </div>
        `;

        container.append(card);
    });
}

// 获取状态对应的颜色类
function getStatusColorClass(status) {
    switch (status) {
        case '正在运行':
        case '处理中':
            return 'border-blue-500';
        case '等待中':
            return 'border-yellow-500';
        case '已完成':
            return 'border-green-500';
        case '已失败':
        case '出错':
            return 'border-red-500';
        case '已暂停':
            return 'border-gray-500';
        default:
            return 'border-purple-500';
    }
}

// 更新任务进度
function updateTaskProgress() {
    // 计算完成百分比
    const percent = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    // 更新进度条
    $('#taskProgress').css('width', percent + '%');
    $('#taskProgress').text(percent + '%');

    // 更新任务数量信息
    $('#completedTasks').text(completedTasks);
    $('#totalTasks').text(totalTasks);
    $('#pendingTasks').text(pendingTasks);
}

// 获取已选择的用户ID
function getSelectedProfileIds() {
    const ids = [];
    $('.profile-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 更新UI状态
function updateUIState() {
    if (isTaskRunning) {
        // 不再禁用任何控件，所有控件始终保持可用状态
        // 仅更改开始按钮的视觉样式，但不禁用它
        $('#startMultiTaskBtn').removeClass('bg-blue-600 hover:bg-blue-700').addClass('bg-blue-400');

        // 显示任务状态区域
        $('#runningTasksContainer').fadeIn();
        $(document).trigger('taskStatusChanged', [true]);

        // 更新任务状态文本
        $('#taskStatus').text('运行中');
    } else {
        // 恢复开始按钮的视觉样式
        $('#startMultiTaskBtn').removeClass('bg-gray-400 bg-blue-400').addClass('bg-blue-600 hover:bg-blue-700');

        // 更新任务状态文本
        $('#taskStatus').text('未运行');

        // 隐藏任务监控区域
        $('#runningTasksContainer').fadeOut();
    }

    // 确保所有控件始终可用
    $('.order-links-input').prop('disabled', false).removeClass('bg-gray-100');
    $('.profile-checkbox').prop('disabled', false);
    $('#randomOrderCheckTop').prop('disabled', false);
    $('#maxThreads').prop('disabled', false);
    $('#startMultiTaskBtn').prop('disabled', false);
}

// 显示通知
function showNotification(type, title, message) {
    console.log('显示通知:', type, title, message);

    // 使用window.showNotification函数（在HTML中定义）
    if (window.showNotification) {
        window.showNotification(message, type);
        return;
    }

    // 如果window.showNotification不存在，使用备用方法
    const icon = type === 'success' ? 'check-circle' : 'alert-circle';
    const color = type === 'success' ? 'green' : 'red';

    const notification = `
    <div class="notification ${type} animate-fade-in">
        <div class="flex items-center">
            <i class="ri-${icon}-fill text-${color}-500 text-xl mr-2"></i>
            <div>
                <h4 class="font-bold">${title}</h4>
                <p class="text-sm">${message}</p>
            </div>
        </div>
        <button class="close-btn ml-4">
            <i class="ri-close-line text-gray-500 hover:text-gray-700"></i>
        </button>
    </div>
    `;

    const notifElement = $(notification);
    $('#notificationContainer').append(notifElement);

    // 添加关闭按钮事件
    notifElement.find('.close-btn').click(function() {
        notifElement.addClass('animate-fade-out');
        setTimeout(function() {
            notifElement.remove();
        }, 300);
    });

    // 自动关闭
    setTimeout(function() {
        if (notifElement) {
            notifElement.addClass('animate-fade-out');
            setTimeout(function() {
                notifElement.remove();
            }, 300);
        }
    }, 5000);
}

// 获取时间差描述
function getTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = Math.floor((now - time) / 1000); // 秒数差

    if (diff < 60) return `${diff}秒前`;
    if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
    if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`;
    return `${Math.floor(diff / 86400)}天前`;
}

// 格式化时间（秒）
function formatTime(seconds) {
    if (seconds < 60) return `${Math.round(seconds)}秒`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}分${Math.round(seconds % 60)}秒`;
    return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分`;
}

// 添加快速选择按钮
function addQuickSelectButton() {
    // 检查是否已经存在快速选择按钮
    if ($('#orderQuickSelectContainer').length > 0) {
        return;
    }

    // 创建快速选择容器
    const quickSelectContainer = $(`
        <div id="orderQuickSelectContainer" class="mb-4 p-3 bg-white rounded-lg shadow-sm border border-gray-100">
            <div class="mb-2 text-sm font-medium text-gray-700">快速选择用户</div>
            <div class="flex flex-wrap gap-2 mb-2">
                <div class="flex-1">
                    <input type="text" id="numberRangeInput" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="输入数字范围，如: 1-10" value="1-10">
                </div>
                <button id="selectRangeBtn" class="px-3 py-2 bg-primary text-white rounded-md text-sm hover:bg-primary/90 transition-colors">
                    选择用户
                </button>
                <button id="clearSelectionBtn" class="px-3 py-2 bg-gray-200 text-gray-700 rounded-md text-sm hover:bg-gray-300 transition-colors">
                    清除选择
                </button>
            </div>
            <div class="text-xs text-gray-500 mt-2">
                <strong>说明:</strong> 输入数字范围(如1-10)后点击"选择用户"，将选择用户名中包含该范围内任意数字的用户，但不会选择包含11、12、13的用户。
            </div>
        </div>
    `);

    // 查找Chrome用户列表容器
    const userListContainer = $('.glass-effect.hover-card.rounded-lg.shadow-sm.mb-6.overflow-hidden');

    // 插入到用户列表容器之前
    if (userListContainer.length) {
        quickSelectContainer.insertBefore(userListContainer);

        // 绑定事件
        $('#selectRangeBtn').click(selectUsersByRange);
        $('#clearSelectionBtn').click(clearUserSelection);

        // 绑定回车键事件
        $('#numberRangeInput').keypress(function(e) {
            if (e.which === 13) { // Enter键
                selectUsersByRange();
                e.preventDefault();
            }
        });

        console.log('快速选择按钮已添加到商品下单页面');
    } else {
        console.error('找不到Chrome用户列表容器');
    }
}

// 根据输入的数字范围选择用户，但不包含11、12、13
function selectUsersByRange() {
    // 获取输入的数字范围
    const rangeInput = $('#numberRangeInput').val().trim();
    if (!rangeInput) {
        showNotification('error', '输入错误', '请输入有效的数字范围');
        return;
    }

    // 解析数字范围
    const numbers = parseNumberRange(rangeInput);
    if (numbers.length === 0) {
        showNotification('error', '输入错误', '无法解析数字范围，请使用正确的格式，如：1-10');
        return;
    }

    // 获取所有用户复选框
    const userCheckboxes = $('.profile-checkbox');

    // 先清除所有选择
    userCheckboxes.prop('checked', false);

    // 遍历所有用户
    userCheckboxes.each(function() {
        // 获取用户名
        const row = $(this).closest('tr');
        if (row.length === 0) return;

        const userNameCell = row.find('td:nth-child(3)');
        if (userNameCell.length === 0) return;

        const userName = userNameCell.text().trim();

        // 提取用户名中的所有数字
        const userNumbers = extractNumbersFromUserName(userName);

        // 检查是否包含指定范围内的数字，但不包含11、12、13
        let shouldSelect = false;
        let containsExcluded = false;

        for (const num of userNumbers) {
            // 检查是否包含11、12、13
            if (num === 11 || num === 12 || num === 13) {
                containsExcluded = true;
                break;
            }

            // 检查是否包含指定范围内的数字
            if (numbers.includes(num)) {
                shouldSelect = true;
            }
        }

        // 如果包含指定范围内的数字，且不包含11、12、13，则选中
        if (shouldSelect && !containsExcluded && !$(this).prop('disabled')) {
            $(this).prop('checked', true);

            // 触发change事件
            $(this).trigger('change');
        }
    });

    // 更新全选复选框状态
    updateSelectAllCheckbox();

    // 显示通知
    const selectedCount = $('.profile-checkbox:checked').length;
    const totalCount = $('.profile-checkbox').length;
    showNotification('success', '选择完成', `已选择 ${selectedCount}/${totalCount} 个用户`);
}

// 解析数字范围，支持单个数字、范围和组合
function parseNumberRange(input) {
    const result = [];

    // 分割逗号分隔的部分
    const parts = input.split(',');

    parts.forEach(part => {
        part = part.trim();

        // 检查是否是范围 (如 "1-10")
        if (part.includes('-')) {
            const [start, end] = part.split('-').map(num => parseInt(num.trim(), 10));

            // 验证起始和结束是有效数字
            if (!isNaN(start) && !isNaN(end)) {
                // 添加范围内的所有数字
                for (let i = start; i <= end; i++) {
                    result.push(i);
                }
            }
        }
        // 单个数字
        else {
            const num = parseInt(part, 10);
            if (!isNaN(num)) {
                result.push(num);
            }
        }
    });

    // 去重并排序
    return [...new Set(result)].sort((a, b) => a - b);
}

// 从用户名中提取数字
function extractNumbersFromUserName(userName) {
    const regex = /\b(\d+)\b/g;
    const numbers = [];
    let match;

    while ((match = regex.exec(userName)) !== null) {
        numbers.push(parseInt(match[1], 10));
    }

    return numbers;
}

// 清除用户选择
function clearUserSelection() {
    $('.profile-checkbox').prop('checked', false).trigger('change');
    updateSelectAllCheckbox();
    showNotification('info', '已清除选择', '已取消所有用户的选择');
}

// 更新全选复选框状态
function updateSelectAllCheckbox() {
    const selectedCount = $('.profile-checkbox:checked').length;
    const totalCount = $('.profile-checkbox').length;

    const selectAllCheckbox = $('#selectAllProfiles');
    if (selectAllCheckbox.length) {
        selectAllCheckbox.prop('checked', selectedCount > 0 && selectedCount === totalCount);
        selectAllCheckbox.prop('indeterminate', selectedCount > 0 && selectedCount < totalCount);
    }

    // 如果映射模块已加载，更新选择状态
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.updateSelection();
    }
}