/**
 * 快速批量选择功能
 * 允许用户通过输入范围（如1-100）来快速勾选指定范围内的用户
 */

// 初始化快速选择功能
function initQuickSelect() {
    console.log('初始化快速选择功能');

    // 检查当前页面URL，如果是商品下单页面则不加载快速选择组件
    if (window.location.pathname.includes('/order') && !window.location.pathname.includes('/export')) {
        console.log('当前是商品下单页面，跳过快速选择组件初始化');
        return;
    }

    // 检查是否已经存在快速选择容器
    if (document.getElementById('quickSelectContainer')) {
        console.log('快速选择容器已存在，跳过初始化');
        return;
    }

    // 创建快速选择容器
    const quickSelectContainer = document.createElement('div');
    quickSelectContainer.id = 'quickSelectContainer';
    quickSelectContainer.className = 'quick-select-container p-3 border-t border-gray-100 mt-2';
    quickSelectContainer.innerHTML = `
        <div class="mb-2 text-sm font-medium text-gray-700">快速批量选择</div>
        <div class="flex flex-wrap gap-2 mb-2">
            <div class="flex-1">
                <input type="text" id="rangeInput" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="输入用户名中的数字范围，如: 1-10,15,20-25">
            </div>
            <button id="selectRangeBtn" class="px-3 py-2 bg-primary text-white rounded-md text-sm hover:bg-primary/90 transition-colors">
                按数字选择
            </button>
            <button id="clearSelectionBtn" class="px-3 py-2 bg-gray-200 text-gray-700 rounded-md text-sm hover:bg-gray-300 transition-colors">
                清除选择
            </button>
            <button id="invertSelectionBtn" class="px-3 py-2 bg-gray-200 text-gray-700 rounded-md text-sm hover:bg-gray-300 transition-colors">
                反选
            </button>
        </div>
        <div class="flex flex-wrap gap-1">
            <button class="preset-btn px-2 py-1 bg-gray-200 text-gray-700 rounded text-xs hover:bg-gray-300 transition-colors" data-range="1-10">数字1-10</button>
            <button class="preset-btn px-2 py-1 bg-gray-200 text-gray-700 rounded text-xs hover:bg-gray-300 transition-colors" data-range="1-20">数字1-20</button>
            <button class="preset-btn px-2 py-1 bg-gray-200 text-gray-700 rounded text-xs hover:bg-gray-300 transition-colors" data-range="1-50">数字1-50</button>
            <button class="preset-btn px-2 py-1 bg-gray-200 text-gray-700 rounded text-xs hover:bg-gray-300 transition-colors" data-range="all">全选</button>
            <button class="preset-btn px-2 py-1 bg-gray-200 text-gray-700 rounded text-xs hover:bg-gray-300 transition-colors" data-range="even">偶数</button>
            <button class="preset-btn px-2 py-1 bg-gray-200 text-gray-700 rounded text-xs hover:bg-gray-300 transition-colors" data-range="odd">奇数</button>
        </div>
        <div class="text-xs text-gray-500 mt-1">
            <strong>注意:</strong> 此功能根据用户名中的<strong>完整数字</strong>进行精确匹配，而非按位置选择。
            例如，输入"5"只会选中用户名中包含独立数字5的用户（如"用户5"、"客户5"），不会选中"用户50"、"用户51"等。
            输入"10"只会选中包含数字10的用户，不会选中包含1的用户。
            输入"1-10"会选中用户名中包含1到10中任意一个数字的用户，但不会选中包含其他数字（如11、20等）的用户。
            支持格式: 单个数字(5)、范围(1-10)、组合(1-10,15,20-25)
        </div>
    `;

    // 查找Chrome用户列表容器 - 使用更通用的选择器，适用于所有页面
    // 首先尝试查找确认收货页面的用户列表容器
    let chromeUserCard = document.querySelector('.glass-effect.hover-card.rounded-lg.shadow-sm.overflow-hidden.border');

    // 如果没有找到，尝试查找商品下单页面的用户列表容器
    if (!chromeUserCard) {
        chromeUserCard = document.querySelector('.glass-effect.hover-card.rounded-lg.shadow-sm.mb-6.overflow-hidden');
    }

    // 如果没有找到，尝试查找订单导出页面的用户列表容器
    if (!chromeUserCard) {
        chromeUserCard = document.querySelector('.glass-effect.hover-card.rounded-lg.shadow-sm.p-5.border.border-gray-100\\/50.mb-6');
    }

    // 如果还是没有找到，尝试查找任何包含用户列表的容器
    if (!chromeUserCard) {
        const tables = document.querySelectorAll('table');
        for (const table of tables) {
            if (table.querySelector('th') && table.querySelector('th').textContent.includes('用户')) {
                chromeUserCard = table.closest('.glass-effect') || table.parentElement.closest('div');
                break;
            }
        }
    }

    // 如果还是没有找到，尝试查找任何包含"Chrome用户选择"标题的容器
    if (!chromeUserCard) {
        const headers = document.querySelectorAll('h3');
        for (const header of headers) {
            if (header.textContent.includes('Chrome用户选择')) {
                chromeUserCard = header.closest('.glass-effect');
                break;
            }
        }
    }

    if (chromeUserCard) {
        // 查找Chrome用户列表的头部区域
        let headerContainer = chromeUserCard.querySelector('.p-4');

        // 如果没有找到p-4类的容器，尝试查找其他可能的头部容器
        if (!headerContainer) {
            headerContainer = chromeUserCard.querySelector('.px-4.py-3') ||
                             chromeUserCard.querySelector('div:first-child');
        }

        // 特别处理订单导出页面
        const isExportPage = window.location.pathname.includes('/export');
        if (isExportPage) {
            // 在订单导出页面中，快速选择组件应该添加到用户列表下方的区域
            // 查找p-4类的容器，这通常是用户列表的头部区域
            headerContainer = chromeUserCard.querySelector('.p-4');

            // 如果没有找到，使用整个用户列表容器
            if (!headerContainer) {
                headerContainer = chromeUserCard;
            }
        }

        if (headerContainer) {
            // 插入到用户列表头部区域之后
            headerContainer.appendChild(quickSelectContainer);

            // 绑定事件
            bindQuickSelectEvents();
            console.log('快速选择组件已添加到Chrome用户列表容器中');
        } else {
            console.error('找不到Chrome用户列表头部区域');
            // 如果找不到头部区域，直接插入到用户列表容器的开头
            chromeUserCard.insertBefore(quickSelectContainer, chromeUserCard.firstChild);
            bindQuickSelectEvents();
        }
    } else {
        console.error('找不到Chrome用户列表容器');
    }
}

// 绑定快速选择事件
function bindQuickSelectEvents() {
    // 批量选择按钮
    document.getElementById('selectRangeBtn').addEventListener('click', function() {
        const rangeInput = document.getElementById('rangeInput').value.trim();
        if (!rangeInput) return;

        // 解析输入的范围
        const numbers = parseRangeInput(rangeInput);

        // 获取所有用户项
        const userItems = document.querySelectorAll('.profile-checkbox');

        // 先清除所有选择
        userItems.forEach(checkbox => {
            checkbox.checked = false;
        });

        // 选择用户名中包含精确匹配数字的用户
        userItems.forEach(checkbox => {
            // 获取用户名
            const row = checkbox.closest('tr');
            if (!row) return;

            const userNameCell = row.querySelector('td:nth-child(3)');
            if (!userNameCell) return;

            const userName = userNameCell.textContent.trim();

            // 检查用户名是否包含精确匹配的数字
            let shouldSelect = false;

            // 从用户名中提取所有完整的数字
            const userNumbers = extractNumbersFromString(userName);

            // 检查提取的数字是否与要匹配的数字有交集
            for (const num of numbers) {
                if (userNumbers.includes(num)) {
                    shouldSelect = true;
                    break;
                }
            }

            if (shouldSelect && !checkbox.disabled) {
                checkbox.checked = true;
                // 触发change事件
                const event = new Event('change', { bubbles: true });
                checkbox.dispatchEvent(event);
            }
        });

        // 更新选择状态
        updateSelectionStatus();
    });

    // 清除选择按钮
    document.getElementById('clearSelectionBtn').addEventListener('click', function() {
        const userItems = document.querySelectorAll('.profile-checkbox');
        userItems.forEach(checkbox => {
            checkbox.checked = false;
            // 触发change事件
            const event = new Event('change', { bubbles: true });
            checkbox.dispatchEvent(event);
        });

        // 更新选择状态
        updateSelectionStatus();
    });

    // 反选按钮
    document.getElementById('invertSelectionBtn').addEventListener('click', function() {
        const userItems = document.querySelectorAll('.profile-checkbox');
        userItems.forEach(checkbox => {
            if (!checkbox.disabled) {
                checkbox.checked = !checkbox.checked;
                // 触发change事件
                const event = new Event('change', { bubbles: true });
                checkbox.dispatchEvent(event);
            }
        });

        // 更新选择状态
        updateSelectionStatus();
    });

    // 预设按钮
    document.querySelectorAll('.preset-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const preset = this.getAttribute('data-range');
            const userItems = document.querySelectorAll('.profile-checkbox');
            const totalUsers = userItems.length;

            // 清除所有选择
            userItems.forEach(checkbox => {
                checkbox.checked = false;
            });

            // 根据预设选择用户
            if (preset === 'all') {
                // 全选
                userItems.forEach(checkbox => {
                    if (!checkbox.disabled) {
                        checkbox.checked = true;
                        // 触发change事件
                        const event = new Event('change', { bubbles: true });
                        checkbox.dispatchEvent(event);
                    }
                });
            }
            else if (preset === 'even') {
                // 选择用户名中包含精确匹配偶数的用户
                userItems.forEach(checkbox => {
                    const row = checkbox.closest('tr');
                    if (!row) return;

                    const userNameCell = row.querySelector('td:nth-child(3)');
                    if (!userNameCell) return;

                    const userName = userNameCell.textContent.trim();

                    // 检查用户名是否包含精确匹配的偶数
                    let shouldSelect = false;

                    // 从用户名中提取所有完整的数字
                    const userNumbers = extractNumbersFromString(userName);

                    // 检查是否有偶数
                    for (const num of userNumbers) {
                        if (num % 2 === 0) {
                            shouldSelect = true;
                            break;
                        }
                    }

                    if (shouldSelect && !checkbox.disabled) {
                        checkbox.checked = true;
                        // 触发change事件
                        const event = new Event('change', { bubbles: true });
                        checkbox.dispatchEvent(event);
                    }
                });
            }
            else if (preset === 'odd') {
                // 选择用户名中包含精确匹配奇数的用户
                userItems.forEach(checkbox => {
                    const row = checkbox.closest('tr');
                    if (!row) return;

                    const userNameCell = row.querySelector('td:nth-child(3)');
                    if (!userNameCell) return;

                    const userName = userNameCell.textContent.trim();

                    // 检查用户名是否包含精确匹配的奇数
                    let shouldSelect = false;

                    // 从用户名中提取所有完整的数字
                    const userNumbers = extractNumbersFromString(userName);

                    // 检查是否有奇数
                    for (const num of userNumbers) {
                        if (num % 2 !== 0) {
                            shouldSelect = true;
                            break;
                        }
                    }

                    if (shouldSelect && !checkbox.disabled) {
                        checkbox.checked = true;
                        // 触发change事件
                        const event = new Event('change', { bubbles: true });
                        checkbox.dispatchEvent(event);
                    }
                });
            }
            else {
                // 其他预设，如 "1-10"
                document.getElementById('rangeInput').value = preset;
                document.getElementById('selectRangeBtn').click();
                return;
            }

            // 更新选择状态
            updateSelectionStatus();
        });
    });
}

// 解析范围输入，支持单个数字、范围和组合
function parseRangeInput(input) {
    const result = [];

    // 分割逗号分隔的部分
    const parts = input.split(',');

    parts.forEach(part => {
        part = part.trim();

        // 检查是否是范围 (如 "1-10")
        if (part.includes('-')) {
            const [start, end] = part.split('-').map(num => parseInt(num.trim(), 10));

            // 验证起始和结束是有效数字
            if (!isNaN(start) && !isNaN(end)) {
                // 添加范围内的所有数字
                for (let i = start; i <= end; i++) {
                    result.push(i);
                }
            }
        }
        // 单个数字
        else {
            const num = parseInt(part, 10);
            if (!isNaN(num)) {
                result.push(num);
            }
        }
    });

    // 去重并排序
    return [...new Set(result)].sort((a, b) => a - b);
}

// 从字符串中提取数字
function extractNumbersFromString(str) {
    // 使用正则表达式提取所有数字
    // 我们需要匹配独立的数字，而不是数字的一部分
    // 使用单词边界 \b 来确保匹配的是完整的数字
    const regex = /\b(\d+)\b/g;
    const matches = [];
    let match;

    // 查找所有匹配项
    while ((match = regex.exec(str)) !== null) {
        matches.push(match[1]);
    }

    // 如果没有匹配到数字，返回空数组
    if (matches.length === 0) return [];

    // 将字符串数字转换为整数
    return matches.map(num => parseInt(num, 10));
}

// 更新选择状态
function updateSelectionStatus() {
    // 如果映射模块已加载，使用映射模块更新选择状态
    if (typeof ChromeUsersMapping !== 'undefined') {
        ChromeUsersMapping.updateSelection();
    }

    // 获取选中的用户数量
    const selectedCount = document.querySelectorAll('.profile-checkbox:checked').length;
    const totalCount = document.querySelectorAll('.profile-checkbox').length;

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('selectAllProfiles');
    const checkAllCheckbox = document.getElementById('checkAllProfiles');

    if (selectAllCheckbox) {
        selectAllCheckbox.checked = selectedCount > 0 && selectedCount === totalCount;
        selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalCount;
    }

    if (checkAllCheckbox) {
        checkAllCheckbox.checked = selectedCount > 0 && selectedCount === totalCount;
        checkAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalCount;
    }

    // 显示选择结果
    showNotification(`已选择 ${selectedCount}/${totalCount} 个用户`, 'info');
}

// 显示通知
function showNotification(message, type = 'info') {
    // 检查是否是在递归调用
    if (window._isShowingNotification) {
        // 避免递归调用，只记录日志
        console.log(message);
        return;
    }

    // 如果页面中有通知函数，使用页面的通知函数
    if (typeof window.showNotification === 'function' && window.showNotification !== showNotification) {
        try {
            // 设置标志，防止递归
            window._isShowingNotification = true;
            window.showNotification(message, type);
        } finally {
            // 清除标志
            window._isShowingNotification = false;
        }
        return;
    }

    // 否则使用控制台日志
    console.log(message);
}

// 在页面加载完成后初始化快速选择功能
document.addEventListener('DOMContentLoaded', function() {
    // 等待更长时间，确保Chrome用户列表已经加载
    setTimeout(initQuickSelect, 2000);
});

// 如果页面已经加载完成，延迟初始化
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    // 使用更长的延迟，避免与页面加载冲突
    setTimeout(initQuickSelect, 2000);
}
