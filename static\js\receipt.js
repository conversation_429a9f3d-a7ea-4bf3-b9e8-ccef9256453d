/**
 * 淘宝商品下单管理系统 - 确认收货页面JavaScript
 * 处理确认收货功能的交互
 */

// 全局变量
let isTaskRunning = false;
let totalTasks = 0;
let completedTasks = 0;
let pendingTasks = 0;
let taskInterval = null;
let autoRefresh = true;
let receiptMode = 'manual'; // 确认收货模式：manual-手动输入订单号，auto-自动确认所有订单
let selectedProfiles = [];
let selectedOrders = [];

// DOM加载完成后执行
$(document).ready(function() {
    // 初始化页面
    initPage();
    
    // 绑定事件
    bindEvents();
    
    // 定期刷新任务状态
    setInterval(updateTaskStatus, 2000);
});

// 初始化页面
function initPage() {
    // 加载Chrome用户配置
    loadProfiles();
    
    // 初始化UI状态
    updateUIState();
}

// 绑定事件
function bindEvents() {
    // 开始任务按钮点击事件
    $('#btnStartTask').click(function() {
        if (receiptMode === 'manual') {
            startManualReceipt();
        } else {
            startAutoReceipt();
        }
    });
    
    // 停止任务按钮点击事件
    $('#btnStopTask').click(function() {
        stopReceiptTask();
    });
    
    // 切换模式选项
    $('input[name="receiptMode"]').change(function() {
        setReceiptMode($(this).val());
    });
    
    // 刷新用户按钮点击事件
    $('#btnRefreshProfiles').click(function() {
        refreshProfiles();
    });
    
    // 加载订单号按钮点击事件
    $('#btnLoadWaitingOrders').click(function() {
        loadWaitingOrders();
    });
    
    // 全选/取消全选 - 用户
    $('#checkAllProfiles').change(function() {
        const isChecked = $(this).prop('checked');
        $('.profile-checkbox').prop('checked', isChecked);
    });
    
    // 全选/取消全选 - 订单
    $('#checkAllOrders').change(function() {
        const isChecked = $(this).prop('checked');
        $('.order-checkbox').prop('checked', isChecked);
        updateSelectedOrderCount();
    });
    
    // 随机排序选项
    $('#checkRandomOrder').change(function() {
        // 可以添加一些提示信息
    });
}

// 设置确认收货模式
function setReceiptMode(mode) {
    receiptMode = mode;
    
    if (mode === 'manual') {
        // 显示手动模式界面元素
        $('#manualModeSection').removeClass('hidden');
        $('#autoModeSection').addClass('hidden');
    } else {
        // 显示自动模式界面元素
        $('#manualModeSection').addClass('hidden');
        $('#autoModeSection').removeClass('hidden');
    }
}

// 加载Chrome用户配置
function loadProfiles() {
    try {
        // 检查是否能使用PyWebView API
        if (window.pywebview && window.pywebview.api) {
            window.pywebview.api.get_profiles().then(result => {
                if (result.success) {
                    renderProfilesTable(result.profiles);
                } else {
                    showNotification('error', '加载Chrome用户配置失败', result.message || '未知错误');
                }
            }).catch(error => {
                console.error('API调用失败:', error);
                // 回退到AJAX请求
                loadProfilesViaAjax();
            });
        } else {
            // 回退到AJAX请求
            loadProfilesViaAjax();
        }
    } catch (error) {
        console.error('加载配置文件时出错:', error);
        // 回退到AJAX请求
        loadProfilesViaAjax();
    }
}

// 通过AJAX加载用户配置(作为备用方法)
function loadProfilesViaAjax() {
    $.ajax({
        url: '/api/chrome/profiles',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                renderProfilesTable(response.profiles);
            } else {
                showNotification('error', '加载Chrome用户配置失败', response.message || '未知错误');
            }
        },
        error: function(xhr, status, error) {
            showNotification('error', '加载Chrome用户配置失败', error);
        }
    });
}

// 刷新用户配置
function refreshProfiles() {
    // 显示加载中状态
    $('#profilesTable tbody').html('<tr><td colspan="3" class="text-center py-4">正在刷新用户配置...</td></tr>');
    
    try {
        // 检查是否能使用PyWebView API
        if (window.pywebview && window.pywebview.api) {
            window.pywebview.api.refresh_profiles().then(result => {
                if (result.success) {
                    renderProfilesTable(result.profiles);
                    showNotification('success', '刷新成功', '已更新Chrome用户配置列表');
                } else {
                    showNotification('error', '刷新失败', result.message || '未知错误');
                }
            }).catch(error => {
                console.error('API调用失败:', error);
                // 回退到AJAX请求
                refreshProfilesViaAjax();
            });
        } else {
            // 回退到AJAX请求
            refreshProfilesViaAjax();
        }
    } catch (error) {
        console.error('刷新配置文件时出错:', error);
        // 回退到AJAX请求
        refreshProfilesViaAjax();
    }
}

// 通过AJAX刷新用户配置(作为备用方法)
function refreshProfilesViaAjax() {
    $.ajax({
        url: '/api/chrome/refresh',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                renderProfilesTable(response.profiles);
                showNotification('success', '刷新成功', '已更新Chrome用户配置列表');
            } else {
                showNotification('error', '刷新失败', response.message || '未知错误');
            }
        },
        error: function(xhr, status, error) {
            showNotification('error', '刷新失败', error);
        }
    });
}

// 渲染用户配置表格
function renderProfilesTable(profiles) {
    if (!profiles || profiles.length === 0) {
        $('#profilesTable tbody').html('<tr><td colspan="3" class="text-center py-4">未找到Chrome用户配置</td></tr>');
        return;
    }
    
    let html = '';
    profiles.forEach(function(profile, index) {
        html += `
        <tr class="${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} transition duration-300 ease-in-out hover:bg-yellow-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                <div class="flex items-center">
                    <input type="checkbox" id="profile-${profile.id}" class="profile-checkbox" value="${profile.id}" 
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                    <label for="profile-${profile.id}" class="ml-2 text-sm font-medium text-gray-900">${profile.name}</label>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${profile.path}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${profile.status === '可用' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${profile.status}
                </span>
            </td>
        </tr>
        `;
    });
    
    $('#profilesTable tbody').html(html);
}

// 加载等待确认收货的订单
function loadWaitingOrders() {
    // 显示加载状态
    $('#ordersTable tbody').html('<tr><td colspan="4" class="text-center py-4">正在加载订单数据...</td></tr>');
    
    try {
        // 检查是否能使用PyWebView API
        if (window.pywebview && window.pywebview.api) {
            window.pywebview.api.get_waiting_receipt_orders().then(result => {
                if (result.success) {
                    renderOrdersTable(result.orders);
                    showNotification('success', '订单加载成功', `已找到 ${result.orders.length} 个待确认收货的订单`);
                } else {
                    showNotification('error', '订单加载失败', result.message || '未知错误');
                    // 清空表格
                    $('#ordersTable tbody').html('<tr><td colspan="4" class="text-center py-4">加载订单失败</td></tr>');
                }
            }).catch(error => {
                console.error('API调用失败:', error);
                // 回退到AJAX请求
                loadWaitingOrdersViaAjax();
            });
        } else {
            // 回退到AJAX请求
            loadWaitingOrdersViaAjax();
        }
    } catch (error) {
        console.error('加载订单时出错:', error);
        // 回退到AJAX请求
        loadWaitingOrdersViaAjax();
    }
}

// 通过AJAX加载等待确认收货的订单(作为备用方法)
function loadWaitingOrdersViaAjax() {
    $.ajax({
        url: '/api/receipt/waiting',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                renderOrdersTable(response.orders);
                showNotification('success', '订单加载成功', `已找到 ${response.orders.length} 个待确认收货的订单`);
            } else {
                showNotification('error', '订单加载失败', response.message || '未知错误');
                // 清空表格
                $('#ordersTable tbody').html('<tr><td colspan="4" class="text-center py-4">加载订单失败</td></tr>');
            }
        },
        error: function(xhr, status, error) {
            showNotification('error', '订单加载失败', error);
            // 清空表格
            $('#ordersTable tbody').html('<tr><td colspan="4" class="text-center py-4">加载订单失败</td></tr>');
        }
    });
}

/**
 * 启动手动确认收货任务
 */
function startManualReceipt() {
    // 获取选中的用户
    const profileIds = getSelectedProfileIds();
    if (profileIds.length === 0) {
        showNotification('error', '无法启动任务', '请至少选择一个Chrome用户');
        return;
    }
    
    // 获取选中的订单
    const orderIds = getSelectedOrderIds();
    if (orderIds.length === 0) {
        showNotification('error', '无法启动任务', '请至少选择一个订单');
        return;
    }
    
    // 获取是否随机顺序
    const shuffle = $('#checkRandomOrder').prop('checked');
    
    // 显示确认对话框
    if (confirm(`确定要启动确认收货任务吗？\n将使用 ${profileIds.length} 个Chrome用户处理 ${orderIds.length} 个订单。`)) {
        // 更新UI状态
        isTaskRunning = true;
        updateUIState();
        
        // 显示任务状态区域
        $('#taskStatusArea').removeClass('hidden');
        
        // 更新任务数量
        totalTasks = orderIds.length;
        pendingTasks = totalTasks;
        completedTasks = 0;
        updateTaskProgress();
        
        console.log('启动手动确认收货任务:', {
            profileIds: profileIds,
            orderCount: orderIds.length,
            shuffle: shuffle
        });
        
        try {
            // 检查是否能使用PyWebView API
            if (window.pywebview && window.pywebview.api) {
                window.pywebview.api.start_receipt_task(profileIds, orderIds, shuffle, false)
                    .then(result => {
                        if (result.success) {
                            showNotification('success', '任务启动成功', '已开始处理确认收货');
                        } else {
                            isTaskRunning = false;
                            updateUIState();
                            showNotification('error', '任务启动失败', result.message || '未知错误');
                        }
                    })
                    .catch(error => {
                        console.error('启动任务API调用失败:', error);
                        isTaskRunning = false;
                        updateUIState();
                        showNotification('error', '任务启动失败', error.toString());
                        // 回退到AJAX请求
                        startManualReceiptViaAjax(profileIds, orderIds, shuffle);
                    });
            } else {
                // 回退到AJAX请求
                startManualReceiptViaAjax(profileIds, orderIds, shuffle);
            }
        } catch (error) {
            console.error('启动任务时出错:', error);
            isTaskRunning = false;
            updateUIState();
            showNotification('error', '任务启动失败', error.toString());
            // 回退到AJAX请求
            startManualReceiptViaAjax(profileIds, orderIds, shuffle);
        }
    }
}

// 通过AJAX启动手动确认收货任务(作为备用方法)
function startManualReceiptViaAjax(profileIds, orderIds, shuffle) {
    $.ajax({
        url: '/api/receipt/start',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            profile_ids: profileIds,
            order_ids: orderIds,
            shuffle: shuffle,
            mode: 'manual'
        }),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('success', '任务启动成功', '已开始处理确认收货');
            } else {
                isTaskRunning = false;
                updateUIState();
                showNotification('error', '任务启动失败', response.message || '未知错误');
            }
        },
        error: function(xhr, status, error) {
            isTaskRunning = false;
            updateUIState();
            showNotification('error', '任务启动失败', error);
        }
    });
}

/**
 * 启动自动确认收货任务
 */
function startAutoReceipt() {
    // 获取选中的用户
    const profileIds = getSelectedProfileIds();
    if (profileIds.length === 0) {
        showNotification('error', '无法启动任务', '请至少选择一个Chrome用户');
        return;
    }
    
    // 显示确认对话框
    if (confirm(`确定要启动自动确认收货任务吗？\n将使用 ${profileIds.length} 个Chrome用户自动确认所有待收货订单。`)) {
        // 更新UI状态
        isTaskRunning = true;
        updateUIState();
        
        // 显示任务状态区域
        $('#taskStatusArea').removeClass('hidden');
        
        console.log('启动自动确认收货任务:', {
            profileIds: profileIds
        });
        
        try {
            // 检查是否能使用PyWebView API
            if (window.pywebview && window.pywebview.api) {
                window.pywebview.api.start_receipt_task(profileIds, [], false, true)
                    .then(result => {
                        if (result.success) {
                            // 更新任务数量
                            totalTasks = result.total_orders || 0;
                            pendingTasks = totalTasks;
                            completedTasks = 0;
                            updateTaskProgress();
                            
                            showNotification('success', '任务启动成功', '已开始自动确认收货');
                        } else {
                            isTaskRunning = false;
                            updateUIState();
                            showNotification('error', '任务启动失败', result.message || '未知错误');
                        }
                    })
                    .catch(error => {
                        console.error('启动任务API调用失败:', error);
                        isTaskRunning = false;
                        updateUIState();
                        showNotification('error', '任务启动失败', error.toString());
                        // 回退到AJAX请求
                        startAutoReceiptViaAjax(profileIds);
                    });
            } else {
                // 回退到AJAX请求
                startAutoReceiptViaAjax(profileIds);
            }
        } catch (error) {
            console.error('启动任务时出错:', error);
            isTaskRunning = false;
            updateUIState();
            showNotification('error', '任务启动失败', error.toString());
            // 回退到AJAX请求
            startAutoReceiptViaAjax(profileIds);
        }
    }
}

// 通过AJAX启动自动确认收货任务(作为备用方法)
function startAutoReceiptViaAjax(profileIds) {
    $.ajax({
        url: '/api/receipt/start',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            profile_ids: profileIds,
            mode: 'auto'
        }),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // 更新任务数量
                totalTasks = response.total_orders || 0;
                pendingTasks = totalTasks;
                completedTasks = 0;
                updateTaskProgress();
                
                showNotification('success', '任务启动成功', '已开始自动确认收货');
            } else {
                isTaskRunning = false;
                updateUIState();
                showNotification('error', '任务启动失败', response.message || '未知错误');
            }
        },
        error: function(xhr, status, error) {
            isTaskRunning = false;
            updateUIState();
            showNotification('error', '任务启动失败', error);
        }
    });
}

/**
 * 停止确认收货任务
 */
function stopReceiptTask() {
    if (!isTaskRunning) return;
    
    if (confirm('确定要停止当前确认收货任务吗？')) {
        try {
            // 检查是否能使用PyWebView API
            if (window.pywebview && window.pywebview.api) {
                window.pywebview.api.stop_receipt_task()
                    .then(result => {
                        if (result.success) {
                            isTaskRunning = false;
                            updateUIState();
                            showNotification('success', '任务已停止', '所有确认收货任务已停止');
                        } else {
                            showNotification('error', '停止任务失败', result.message || '未知错误');
                        }
                    })
                    .catch(error => {
                        console.error('停止任务API调用失败:', error);
                        // 回退到AJAX请求
                        stopReceiptTaskViaAjax();
                    });
            } else {
                // 回退到AJAX请求
                stopReceiptTaskViaAjax();
            }
        } catch (error) {
            console.error('停止任务时出错:', error);
            // 回退到AJAX请求
            stopReceiptTaskViaAjax();
        }
    }
}

// 通过AJAX停止确认收货任务(作为备用方法)
function stopReceiptTaskViaAjax() {
    $.ajax({
        url: '/api/receipt/stop',
        method: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                isTaskRunning = false;
                updateUIState();
                showNotification('success', '任务已停止', '所有确认收货任务已停止');
            } else {
                showNotification('error', '停止任务失败', response.message || '未知错误');
            }
        },
        error: function(xhr, status, error) {
            showNotification('error', '停止任务失败', error);
        }
    });
}

/**
 * 更新任务状态
 */
function updateTaskStatus() {
    if (!isTaskRunning) return;
    
    try {
        // 检查是否能使用PyWebView API
        if (window.pywebview && window.pywebview.api) {
            window.pywebview.api.get_receipt_status()
                .then(result => {
                    if (result.success) {
                        updateTaskStatusUI(result.data);
                    }
                })
                .catch(error => {
                    console.error('获取任务状态API调用失败:', error);
                    // 回退到AJAX请求
                    updateTaskStatusViaAjax();
                });
        } else {
            // 回退到AJAX请求
            updateTaskStatusViaAjax();
        }
    } catch (error) {
        console.error('更新任务状态时出错:', error);
        // 回退到AJAX请求
        updateTaskStatusViaAjax();
    }
}

// 通过AJAX更新任务状态(作为备用方法)
function updateTaskStatusViaAjax() {
    $.ajax({
        url: '/api/receipt/status',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                updateTaskStatusUI(response.data);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取任务状态失败:', error);
        }
    });
}

// 更新任务状态UI
function updateTaskStatusUI(data) {
    // 如果任务已经完成或已停止
    if (data.is_running === false && isTaskRunning) {
        isTaskRunning = false;
        updateUIState();
        showNotification('success', '任务已完成', '所有确认收货任务已完成');
    }
    
    // 更新任务统计信息
    totalTasks = data.total || totalTasks;
    completedTasks = data.completed || 0;
    pendingTasks = data.pending || 0;
    
    // 更新任务进度
    updateTaskProgress();
    
    // 更新活跃任务列表
    updateActiveTasks(data.active_tasks || []);
}

// 更新任务进度
function updateTaskProgress() {
    // 计算完成百分比
    const percent = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
    
    // 更新进度条
    $('#taskProgressBar').css('width', percent + '%');
    $('#taskProgressText').text(percent + '%');
    
    // 更新任务数量信息
    $('#completedTaskCount').text(completedTasks);
    $('#totalTaskCount').text(totalTasks);
    $('#pendingTaskCount').text(pendingTasks);
}

// 更新活跃任务列表
function updateActiveTasks(tasks) {
    const container = $('#activeTasksContainer');
    container.empty();
    
    if (tasks.length === 0) {
        container.html('<div class="text-center text-gray-500 py-4">没有正在运行的任务</div>');
        return;
    }
    
    tasks.forEach(task => {
        const progressPercent = task.progress ? Math.round(task.progress * 100) : 0;
        const statusClass = getStatusColorClass(task.status);
        const timeAgo = task.last_update ? getTimeAgo(task.last_update) : '刚刚';
        const estimatedTime = task.estimated_time ? formatTime(task.estimated_time) : '未知';
        
        const card = `
        <div class="bg-white rounded-lg shadow-md p-4 mb-4 border-l-4 ${statusClass} transition duration-300 ease-in-out hover:shadow-lg">
            <div class="flex justify-between items-start mb-2">
                <h3 class="text-lg font-semibold">${task.user || '未知用户'}</h3>
                <span class="px-2 py-1 text-xs rounded-full ${statusClass.replace('border-', 'bg-').replace('-500', '-100')} ${statusClass.replace('border-', 'text-')}">
                    ${task.status || '未知状态'}
                </span>
            </div>
            <div class="mb-2">
                <p class="text-sm text-gray-600 truncate" title="${task.current_order || ''}">
                    ${task.current_order ? '正在处理: ' + task.current_order : '等待任务...'}
                </p>
                <p class="text-xs text-gray-500">
                    最后更新: ${timeAgo} | 预计剩余时间: ${estimatedTime}
                </p>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="bg-blue-600 h-2.5 rounded-full" style="width: ${progressPercent}%"></div>
            </div>
            <div class="flex justify-between mt-1">
                <span class="text-xs text-gray-500">进度: ${progressPercent}%</span>
                <span class="text-xs text-gray-500">已完成: ${task.completed || 0}/${task.total || 0}</span>
            </div>
        </div>
        `;
        
        container.append(card);
    });
}

// 获取已选择的用户ID
function getSelectedProfileIds() {
    const ids = [];
    $('.profile-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 获取已选择的订单ID
function getSelectedOrderIds() {
    const ids = [];
    $('.order-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 更新UI状态
function updateUIState() {
    if (isTaskRunning) {
        // 禁用开始按钮和相关输入
        $('#btnStartTask').prop('disabled', true).addClass('bg-gray-400').removeClass('bg-blue-600 hover:bg-blue-700');
        $('#profilesTable input[type="checkbox"]').prop('disabled', true);
        $('#ordersTable input[type="checkbox"]').prop('disabled', true);
        $('#checkRandomOrder').prop('disabled', true);
        $('input[name="receiptMode"]').prop('disabled', true);
        
        // 启用停止按钮
        $('#btnStopTask').prop('disabled', false).addClass('bg-red-600 hover:bg-red-700').removeClass('bg-gray-400');
        
        // 显示任务状态区域
        $('#taskStatusArea').removeClass('hidden');
        
        // 更新任务状态文本
        $('#taskStatus').text('运行中');
    } else {
        // 启用开始按钮和相关输入
        $('#btnStartTask').prop('disabled', false).removeClass('bg-gray-400').addClass('bg-blue-600 hover:bg-blue-700');
        $('#profilesTable input[type="checkbox"]').prop('disabled', false);
        $('#ordersTable input[type="checkbox"]').prop('disabled', false);
        $('#checkRandomOrder').prop('disabled', false);
        $('input[name="receiptMode"]').prop('disabled', false);
        
        // 禁用停止按钮
        $('#btnStopTask').prop('disabled', true).removeClass('bg-red-600 hover:bg-red-700').addClass('bg-gray-400');
        
        // 更新任务状态文本
        $('#taskStatus').text('未运行');
    }
}

// 获取状态对应的颜色类
function getStatusColorClass(status) {
    switch (status) {
        case '正在运行':
        case '处理中':
            return 'border-blue-500';
        case '等待中':
            return 'border-yellow-500';
        case '已完成':
            return 'border-green-500';
        case '已失败':
        case '出错':
            return 'border-red-500';
        case '已暂停':
            return 'border-gray-500';
        default:
            return 'border-purple-500';
    }
}

// 显示通知
function showNotification(type, title, message) {
    const icon = type === 'success' ? 'check-circle' : 'alert-circle';
    const color = type === 'success' ? 'green' : 'red';
    
    const notification = `
    <div class="notification ${type} animate-fade-in">
        <div class="flex items-center">
            <i class="ri-${icon}-fill text-${color}-500 text-xl mr-2"></i>
            <div>
                <h4 class="font-bold">${title}</h4>
                <p class="text-sm">${message}</p>
            </div>
        </div>
        <button class="close-btn ml-4">
            <i class="ri-close-line text-gray-500 hover:text-gray-700"></i>
        </button>
    </div>
    `;
    
    const notifElement = $(notification);
    $('#notificationContainer').append(notifElement);
    
    // 添加关闭按钮事件
    notifElement.find('.close-btn').click(function() {
        notifElement.addClass('animate-fade-out');
        setTimeout(function() {
            notifElement.remove();
        }, 300);
    });
    
    // 自动关闭
    setTimeout(function() {
        if (notifElement) {
            notifElement.addClass('animate-fade-out');
            setTimeout(function() {
                notifElement.remove();
            }, 300);
        }
    }, 5000);
}

// 格式化日期
function formatDate(dateStr) {
    if (!dateStr) return '';
    
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 获取时间差描述
function getTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = Math.floor((now - time) / 1000); // 秒数差
    
    if (diff < 60) return `${diff}秒前`;
    if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
    if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`;
    return `${Math.floor(diff / 86400)}天前`;
}

// 格式化时间（秒）
function formatTime(seconds) {
    if (seconds < 60) return `${Math.round(seconds)}秒`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}分${Math.round(seconds % 60)}秒`;
    return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分`;
}

// 渲染订单表格
function renderOrdersTable(orders) {
    if (!orders || orders.length === 0) {
        $('#ordersTable tbody').html('<tr><td colspan="4" class="text-center py-4">没有待确认收货的订单</td></tr>');
        return;
    }
    
    let html = '';
    orders.forEach(function(order, index) {
        html += `
        <tr class="${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} transition duration-300 ease-in-out hover:bg-yellow-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                <div class="flex items-center">
                    <input type="checkbox" id="order-${order.id}" class="order-checkbox" value="${order.id}" 
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                    <label for="order-${order.id}" class="ml-2 text-sm font-medium text-gray-900">${order.id}</label>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${order.title || '未知商品'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${order.price || '未知价格'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${formatDate(order.order_time) || '未知时间'}
            </td>
        </tr>
        `;
    });
    
    $('#ordersTable tbody').html(html);
    
    // 绑定复选框事件
    $('.order-checkbox').change(updateSelectedOrderCount);
    
    // 更新选中计数
    updateSelectedOrderCount();
}

// 更新选中的订单数量
function updateSelectedOrderCount() {
    const selectedCount = $('.order-checkbox:checked').length;
    const totalCount = $('.order-checkbox').length;
    
    $('#selectedOrderCount').text(selectedCount);
    $('#totalOrderCount').text(totalCount);
}