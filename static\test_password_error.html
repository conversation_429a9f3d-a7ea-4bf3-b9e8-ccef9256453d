<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试密码错误状态更新</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>测试密码错误状态更新</h1>
    
    <div class="container">
        <label for="profileId">Chrome用户ID:</label>
        <input type="text" id="profileId" placeholder="输入Chrome用户ID">
        
        <button onclick="updatePasswordError()">更新为密码错误状态</button>
    </div>
    
    <div id="result">
        <p>结果将显示在这里...</p>
    </div>
    
    <script>
        function updatePasswordError() {
            const profileId = document.getElementById('profileId').value.trim();
            
            if (!profileId) {
                document.getElementById('result').innerHTML = '<p style="color: red;">请输入Chrome用户ID</p>';
                return;
            }
            
            // 显示加载状态
            document.getElementById('result').innerHTML = '<p>正在更新状态...</p>';
            
            // 发送请求
            fetch('/api/update_password_error', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    profile_id: profileId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('result').innerHTML = `
                        <p style="color: green;">更新成功!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    document.getElementById('result').innerHTML = `
                        <p style="color: red;">更新失败!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('result').innerHTML = `
                    <p style="color: red;">请求出错!</p>
                    <pre>${error.toString()}</pre>
                `;
            });
        }
    </script>
</body>
</html>
