<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能聊天 - 淘宝商品下单管理系统</title>
    <script src="/static/css/tailwind.min.css"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#1e40af", secondary: "#3b82f6" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.loli.net" />
    <link rel="preconnect" href="https://fonts.loli.net" crossorigin />
    <link
      href="https://fonts.loli.net/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <!-- 使用本地Remixicon图标库 -->
    <link rel="stylesheet" href="/static/css/remixicon.css" />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      .glass-effect {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
      }
      .hover-card {
        transition: all 0.3s ease;
      }
      .hover-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }
      .status-success { background-color: rgba(34, 197, 94, 0.1); color: rgb(22, 163, 74); }
      .status-processing { background-color: rgba(59, 130, 246, 0.1); color: rgb(37, 99, 235); }
      .status-pending { background-color: rgba(156, 163, 175, 0.1); color: rgb(107, 114, 128); }
      .status-error { background-color: rgba(239, 68, 68, 0.1); color: rgb(220, 38, 38); }
      .status-inactive { background-color: rgba(156, 163, 175, 0.1); color: rgb(107, 114, 128); }
      .status-password-error { background-color: rgba(251, 191, 36, 0.1); color: rgb(217, 119, 6); }
      
      /* 聊天界面样式 */
      .chat-container {
        height: 500px;
        overflow-y: auto;
      }
      .message {
        margin-bottom: 1rem;
        animation: fadeIn 0.3s ease-in;
      }
      .message.user {
        text-align: right;
      }
      .message.assistant {
        text-align: left;
      }
      .message-bubble {
        display: inline-block;
        max-width: 70%;
        padding: 0.75rem 1rem;
        border-radius: 1rem;
        word-wrap: break-word;
      }
      .message.user .message-bubble {
        background: linear-gradient(135deg, #3b82f6, #1e40af);
        color: white;
      }
      .message.assistant .message-bubble {
        background: #f3f4f6;
        color: #374151;
      }
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      /* 任务卡片样式 */
      .task-card {
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 0.5rem;
        background: white;
        transition: all 0.2s ease;
      }
      .task-card:hover {
        border-color: #3b82f6;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
      }
      .task-card.selected {
        border-color: #3b82f6;
        background: #eff6ff;
      }
    </style>
</head>
<body
    class="min-h-screen flex"
    style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);"
>
    <div class="flex h-screen w-full">
        <!-- 左侧导航栏 -->
        <aside
          class="w-64 glass-effect shadow-lg flex flex-col h-screen fixed border-r border-gray-100"
        >
          <div class="p-4 border-b border-gray-100 flex items-center">
            <div class="text-2xl font-['Pacifico'] text-primary">TaoBao</div>
            <div class="ml-2 font-semibold text-gray-800">下单管理系统</div>
          </div>
          <nav class="flex-1 overflow-y-auto py-4">
            <ul class="space-y-1">
              <li>
                <a
                  href="/"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-home-line"></i>
                  </div>
                  <span>系统首页</span>
                </a>
              </li>
              <li>
                <a
                  href="/order"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-shopping-cart-line"></i>
                  </div>
                  <span>商品下单</span>
                </a>
              </li>
              <li>
                <a
                  href="/confirm"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-check-double-line"></i>
                  </div>
                  <span>确认收货</span>
                </a>
              </li>
              <li>
                <a
                  href="/cookie"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-key-line"></i>
                  </div>
                  <span>获取Cookie</span>
                </a>
              </li>
              <li>
                <a
                  href="/export"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-download-line"></i>
                  </div>
                  <span>订单导出</span>
                </a>
              </li>
              <li>
                <a
                  href="/chat"
                  class="flex items-center px-4 py-3 text-gray-800 bg-gradient-to-r from-blue-50 to-transparent border-r-4 border-primary"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-chat-3-line text-primary"></i>
                  </div>
                  <span>智能聊天</span>
                </a>
              </li>
            </ul>
          </nav>
          <div class="p-4 border-t border-gray-100">
            <div class="flex items-center">
              <div
                class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center"
              >
                <i class="ri-settings-line"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-700">系统设置</p>
                <p class="text-xs text-gray-500">版本 1.0.0</p>
              </div>
            </div>
          </div>
        </aside>

        <!-- 主内容区 -->
        <main class="ml-64 flex-1 flex flex-col min-h-screen">
            <!-- 顶部状态栏 -->
            <header
                class="glass-effect shadow-sm p-4 flex items-center justify-between sticky top-0 z-10"
            >
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-800">智能聊天</h1>
                    <span class="ml-4 text-sm text-gray-500">AI助手帮您管理订单任务</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center text-sm text-gray-600">
                        <div class="w-6 h-6 flex items-center justify-center">
                            <i class="ri-chrome-line"></i>
                        </div>
                        <span class="ml-1" id="profile-count">浏览器用户: <span class="font-medium">0</span></span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <div class="w-2 h-2 rounded-full bg-green-500 mr-1"></div>
                        <span>系统运行正常</span>
                    </div>
                </div>
            </header>

            <!-- 主要内容 -->
            <div class="flex-1 p-6 bg-gray-50">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
                    <!-- 左侧：聊天界面 -->
                    <div class="lg:col-span-2">
                        <div class="glass-effect hover-card rounded-lg shadow-sm border border-gray-100/50 h-full flex flex-col">
                            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100 flex justify-between items-center">
                                <h3 class="font-medium text-gray-800 flex items-center">
                                    <i class="ri-robot-line mr-2 text-primary"></i>
                                    AI助手
                                </h3>
                                <button id="clearChatBtn" class="text-sm text-gray-500 hover:text-red-500 flex items-center">
                                    <i class="ri-delete-bin-line mr-1"></i>
                                    清空对话
                                </button>
                            </div>
                            
                            <!-- 聊天消息区域 -->
                            <div id="chatContainer" class="flex-1 p-4 chat-container">
                                <div class="message assistant">
                                    <div class="message-bubble">
                                        👋 您好！我是您的AI助手。您可以：<br>
                                        • 粘贴商品链接，我会帮您生成下单任务<br>
                                        • 询问系统功能和使用方法<br>
                                        • 查看和管理您的任务<br><br>
                                        请告诉我您需要什么帮助？
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 输入区域 -->
                            <div class="p-4 border-t border-gray-100">
                                <div class="flex space-x-2">
                                    <textarea 
                                        id="messageInput" 
                                        placeholder="输入消息或粘贴商品链接..." 
                                        class="flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                                        rows="2"
                                    ></textarea>
                                    <button 
                                        id="sendBtn" 
                                        class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center"
                                    >
                                        <i class="ri-send-plane-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：Chrome用户选择和任务管理 -->
                    <div class="lg:col-span-1 space-y-6">
                        <!-- Chrome用户选择 -->
                        <div class="glass-effect hover-card rounded-lg shadow-sm border border-gray-100/50">
                            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100 flex justify-between items-center">
                                <h3 class="font-medium text-gray-800">Chrome用户</h3>
                                <button id="refreshProfilesBtn" class="text-sm text-gray-500 hover:text-primary flex items-center">
                                    <i class="ri-refresh-line mr-1"></i>
                                    刷新
                                </button>
                            </div>
                            <div class="p-4">
                                <div class="flex justify-between items-center mb-3">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="checkAllProfiles" class="rounded border-gray-300 text-primary focus:ring-primary/20">
                                        <label for="checkAllProfiles" class="ml-2 text-sm text-gray-700">全选</label>
                                    </div>
                                    <span id="selectedCount" class="text-xs text-gray-500">已选择: 0</span>
                                </div>
                                
                                <!-- 快速选择 -->
                                <div class="mb-3">
                                    <div class="flex space-x-1 mb-2">
                                        <button class="quick-select-btn text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded" data-type="normal">正常</button>
                                        <button class="quick-select-btn text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded" data-type="first5">前5个</button>
                                        <button class="quick-select-btn text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded" data-type="last5">后5个</button>
                                    </div>
                                    <div class="flex space-x-1">
                                        <input type="text" id="quickSelectInput" placeholder="如: 1-10" class="flex-1 text-xs border border-gray-300 rounded px-2 py-1">
                                        <button id="quickSelectBtn" class="text-xs bg-primary text-white px-2 py-1 rounded hover:bg-primary/90">选择</button>
                                    </div>
                                </div>
                                
                                <!-- 用户列表 -->
                                <div id="profilesList" class="space-y-2 max-h-64 overflow-y-auto">
                                    <div class="text-center text-gray-500 py-4">
                                        <i class="ri-loading-line animate-spin text-xl mb-2"></i>
                                        <div>正在加载用户列表...</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 任务管理 -->
                        <div class="glass-effect hover-card rounded-lg shadow-sm border border-gray-100/50">
                            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
                                <h3 class="font-medium text-gray-800">任务管理</h3>
                            </div>
                            <div class="p-4">
                                <div id="tasksList" class="space-y-2 max-h-64 overflow-y-auto">
                                    <div class="text-center text-gray-500 py-4">
                                        <i class="ri-task-line text-xl mb-2"></i>
                                        <div class="text-sm">暂无任务</div>
                                    </div>
                                </div>
                                
                                <div class="mt-4 pt-4 border-t border-gray-100">
                                    <button id="executeTasksBtn" class="w-full bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed" disabled>
                                        <i class="ri-play-line mr-1"></i>
                                        执行选中任务
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 通知组件 -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm">
            <div class="flex items-center">
                <div id="notificationIcon" class="mr-3"></div>
                <div>
                    <div id="notificationTitle" class="font-medium text-gray-900"></div>
                    <div id="notificationMessage" class="text-sm text-gray-600"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/chat.js"></script>
</body>
</html>
