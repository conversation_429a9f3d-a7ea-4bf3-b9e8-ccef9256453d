<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>确认收货 - 淘宝商品下单管理系统</title>

    <!-- TailwindCSS -->
    <script src="/static/css/tailwind.min.css"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#10b981',
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        info: '#3b82f6',
                    },
                    borderRadius: {
                        'lg': '0.5rem'
                    }
                }
            }
        }
    </script>

    <!-- 谷歌字体 -->
    <link rel="preconnect" href="https://fonts.loli.net">
    <link rel="preconnect" href="https://fonts.loli.net" crossorigin>
    <link href="https://fonts.loli.net/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.loli.net/css2?family=Pacifico&display=swap" rel="stylesheet">

    <!-- 使用本地Remixicon图标库 -->
    <link rel="stylesheet" href="/static/css/remixicon.css" />
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">

    <!-- 自定义样式 -->
    <style>
        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #f9fafb;
        }

        /* 状态指示器 */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 6px;
        }
        .status-active { background-color: #10b981; }
        .status-pending { background-color: #f59e0b; }
        .status-inactive { background-color: #9ca3af; }
        .status-error { background-color: #ef4444; }

        /* 自定义开关 */
        .custom-switch {
            position: relative;
            display: inline-block;
            width: 42px;
            height: 22px;
        }
        .custom-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #e2e8f0;
            transition: .4s;
            border-radius: 22px;
        }
        .switch-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .switch-slider {
            background-color: #3b82f6;
        }
        input:checked + .switch-slider:before {
            transform: translateX(20px);
        }

        /* 卡片悬浮效果 */
        .hover-card {
            transition: all 0.3s ease;
        }
        .hover-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        /* 毛玻璃效果 */
        .glass-effect {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
        }

        /* 淡入动画 */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            background: white;
            box-shadow: 4px 0 10px -5px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }
        .sidebar-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: #64748b;
            border-radius: 0.375rem;
            transition: all 0.2s;
            margin: 0.25rem 0;
        }
        .sidebar-link:hover {
            background-color: rgba(59, 130, 246, 0.08);
            color: #3b82f6;
        }
        .sidebar-link.active {
            background-color: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            font-weight: 500;
        }
        .sidebar-link i {
            margin-right: 0.625rem;
            font-size: 1.25rem;
        }

        /* 确认收货页面特定样式 */
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            border-radius: 10px 10px 0 0;
            background-color: #f8f9fa;
            padding: 15px;
        }
        .card-body {
            padding: 20px;
        }
        .btn-primary {
            background-color: #3b82f6;
            border-color: #3b82f6;
        }
        .btn-danger {
            background-color: #ef4444;
            border-color: #ef4444;
        }
        .form-check-input:checked {
            background-color: #3b82f6;
            border-color: #3b82f6;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-normal {
            background-color: #10b981;
            color: white;
        }
        .status-error {
            background-color: #ef4444;
            color: white;
        }
        .status-warning {
            background-color: #f59e0b;
            color: black;
        }
        .status-processing {
            background-color: #3b82f6;
            color: white;
        }
        .status-waiting {
            background-color: #6c757d;
            color: white;
        }
        .profile-card {
            transition: all 0.3s ease;
        }
        .profile-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .profile-card.selected {
            border: 2px solid #3b82f6;
        }
        .counter-card {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            color: white;
        }
        .counter-card h3 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        .counter-card p {
            font-size: 14px;
            margin-bottom: 0;
        }
        .completed-counter {
            background-color: #10b981;
        }
        .skipped-counter {
            background-color: #f59e0b;
            color: black;
        }
        .processing-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #3b82f6;
            margin-right: 5px;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
            100% {
                transform: scale(0.8);
                opacity: 0.8;
            }
        }
        .waiting-indicator {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            text-align: center;
        }
        .review-status {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body
    class="min-h-screen flex"
    style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);"
>
    <div class="flex h-screen">
        <!-- 左侧导航栏 -->
        <aside
          class="w-64 glass-effect shadow-lg flex flex-col h-screen fixed border-r border-gray-100"
        >
          <div class="p-4 border-b border-gray-100 flex items-center">
            <div class="text-2xl font-['Pacifico'] text-primary">TaoBao</div>
            <div class="ml-2 font-semibold text-gray-800">下单管理系统</div>
          </div>
          <nav class="flex-1 overflow-y-auto py-4">
            <ul class="space-y-1">
              <li>
                <a
                  href="/"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-home-line"></i>
                  </div>
                  <span>系统首页</span>
                </a>
              </li>
              <li>
                <a
                  href="/order"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-shopping-cart-line"></i>
                  </div>
                  <span>商品下单</span>
                </a>
              </li>
              <li>
                <a
                  href="/confirm"
                  class="flex items-center px-4 py-3 text-gray-800 bg-gradient-to-r from-blue-50 to-transparent border-r-4 border-primary"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-check-double-line text-primary"></i>
                  </div>
                  <span>确认收货</span>
                </a>
              </li>
              <li>
                <a
                  href="/cookie"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-key-line"></i>
                  </div>
                  <span>获取Cookie</span>
                </a>
              </li>
              <li>
                <a
                  href="/export"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-file-download-line"></i>
                  </div>
                  <span>订单导出</span>
                </a>
              </li>
              <li>
                <a
                  href="/chat"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-chat-3-line"></i>
                  </div>
                  <span>智能聊天</span>
                </a>
              </li>

            </ul>
          </nav>
          <div class="p-4 border-t border-gray-100">
            <div class="flex items-center">
              <div
                class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center"
              >
                <i class="ri-settings-line"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-700">系统设置</p>
                <p class="text-xs text-gray-500">版本 1.0.0</p>
              </div>
            </div>
          </div>
        </aside>

        <!-- 主内容区 -->
        <main class="ml-64 flex-1 flex flex-col min-h-screen">
            <!-- 顶部状态栏 -->
            <header
                class="glass-effect shadow-sm p-4 flex items-center justify-between sticky top-0 z-10"
            >
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-800">确认收货</h1>
                    <span id="current-date" class="ml-4 text-sm text-gray-500"></span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center mr-4">
                        <span class="text-sm text-gray-600 mr-2">自动刷新：</span>
                        <label class="custom-switch">
                            <input type="checkbox" id="autoRefreshToggle" checked>
                            <span class="switch-slider"></span>
                        </label>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <div class="w-2 h-2 rounded-full bg-green-500 mr-1"></div>
                        <span>系统运行正常</span>
                    </div>
                    <button id="refreshBtn" class="flex items-center text-sm text-gray-600 hover:text-primary">
                        <i class="ri-refresh-line mr-1"></i>
                        <span>刷新</span>
                    </button>
                </div>
            </header>

            <!-- 主内容区 -->
            <main class="flex-1 p-6 bg-gray-50">
                <!-- 操作按钮区 -->
                <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
                    <div class="flex items-center gap-3">
                        <button id="startBtn" class="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-5 py-2 rounded-button flex items-center whitespace-nowrap hover:shadow-lg hover:shadow-emerald-500/20 transition-all duration-300">
                            <div class="w-5 h-5 flex items-center justify-center mr-2">
                                <i class="ri-play-line"></i>
                            </div>
                            <span>开始确认收货</span>
                        </button>

                        <button id="stopBtn" class="bg-gradient-to-r from-red-500 to-pink-500 text-white px-5 py-2 rounded-button flex items-center whitespace-nowrap hover:shadow-lg hover:shadow-red-500/20 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <div class="w-5 h-5 flex items-center justify-center mr-2">
                                <i class="ri-stop-line"></i>
                            </div>
                            <span>停止任务</span>
                        </button>
                    </div>
                </div>

                <!-- 信息统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="glass-effect hover-card rounded-lg shadow-sm p-5 border border-gray-100/50">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">已确认订单</p>
                                <p class="text-2xl font-semibold mt-1" id="completedCounter">0</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-green-50 flex items-center justify-center text-green-500">
                                <i class="ri-checkbox-circle-line ri-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="glass-effect hover-card rounded-lg shadow-sm p-5 border border-gray-100/50">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">已跳过订单</p>
                                <p class="text-2xl font-semibold mt-1" id="skippedCounter">0</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-yellow-50 flex items-center justify-center text-yellow-500">
                                <i class="ri-time-line ri-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="glass-effect hover-card rounded-lg shadow-sm p-5 border border-gray-100/50">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">待处理订单</p>
                                <p class="text-2xl font-semibold mt-1" id="pendingCounter">0</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center text-blue-500">
                                <i class="ri-hourglass-line ri-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="glass-effect hover-card rounded-lg shadow-sm p-5 border border-gray-100/50">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">任务状态</p>
                                <p class="text-2xl font-semibold mt-1" id="taskStatus">未运行</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-purple-50 flex items-center justify-center text-purple-500">
                                <i class="ri-equalizer-line ri-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务进度 -->
                <div class="glass-effect hover-card rounded-lg shadow-sm mb-6 p-5 border border-gray-100/50">
                    <h3 class="font-medium text-gray-800 mb-4">任务进度</h3>
                    <div class="w-full bg-gray-100 rounded-full h-4 mb-2">
                        <div id="taskProgress" class="bg-primary h-4 rounded-full text-xs text-white flex items-center justify-center" style="width: 0%">0%</div>
                    </div>
                    <div class="text-sm text-gray-600 mt-2">
                        <span id="processingText">未开始处理</span>
                    </div>
                </div>

                <!-- 等待状态 -->
                <div id="waitingStatus" class="glass-effect hover-card rounded-lg shadow-sm mb-6 p-5 border border-gray-100/50" style="display: none;">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-yellow-50 flex items-center justify-center text-yellow-500 mr-4">
                            <i class="ri-time-line ri-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-800 mb-1">等待中</h3>
                            <p class="text-sm text-gray-600">所有用户处理完毕，等待 <span id="waitingTime" class="font-semibold">60</span> 秒后开始下一轮...</p>
                        </div>
                    </div>
                </div>

                <!-- 自动评价状态 -->
                <div id="reviewStatus" class="glass-effect hover-card rounded-lg shadow-sm mb-6 p-5 border border-gray-100/50" style="display: none;">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center text-blue-500 mr-4">
                            <i class="ri-chat-1-line ri-xl"></i>
                        </div>
                        <h3 class="font-medium text-gray-800">自动评价状态</h3>
                    </div>
                    <div class="space-y-4">
                        <p id="reviewInfo" class="text-sm bg-blue-50 p-3 rounded-lg">准备评价中...</p>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-xs text-gray-500 mb-1">当前订单:</p>
                                <p class="font-medium" id="currentOrderId">-</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-xs text-gray-500 mb-1">商品名称:</p>
                                <p class="font-medium" id="currentOrderTitle">-</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-xs text-gray-500 mb-1">评价状态:</p>
                                <p class="font-medium" id="currentOrderStatus">-</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-xs text-gray-500 mb-1">总订单数:</p>
                                <p class="font-medium" id="totalOrderCount">0</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 确认收货设置 -->
                <div class="glass-effect hover-card rounded-lg shadow-sm p-5 border border-gray-100/50 mb-6">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-100 -mx-5 -mt-5 mb-5">
                        <h3 class="font-medium text-gray-800">自动确认收货设置</h3>
                    </div>
                    <form id="configForm">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="startDate" class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                                <input type="date" class="w-full border border-gray-200 rounded-lg p-2 focus:border-primary focus:ring-1 focus:ring-primary" id="startDate" name="start_date">
                            </div>
                            <div>
                                <label for="endDate" class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                                <input type="date" class="w-full border border-gray-200 rounded-lg p-2 focus:border-primary focus:ring-1 focus:ring-primary" id="endDate" name="end_date">
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="interval" class="block text-sm font-medium text-gray-700 mb-1">间隔时间（秒）</label>
                                <input type="number" class="w-full border border-gray-200 rounded-lg p-2 focus:border-primary focus:ring-1 focus:ring-primary" id="interval" name="interval" min="10" value="60">
                            </div>
                            <div>
                                <label for="maxThreads" class="block text-sm font-medium text-gray-700 mb-1">最大线程数</label>
                                <input type="number" class="w-full border border-gray-200 rounded-lg p-2 focus:border-primary focus:ring-1 focus:ring-primary" id="maxThreads" name="max_threads" min="1" max="10" value="3">
                            </div>
                        </div>
                        <div class="mb-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="autoReviewEnabled" name="auto_review_enabled" class="mr-2 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" checked>
                                <label for="autoReviewEnabled" class="text-sm font-medium text-gray-700">
                                    自动评价（确认收货后自动提交评价）
                                </label>
                            </div>
                        </div>
                        <div class="mb-4" id="reviewTextContainer">
                            <label for="reviewText" class="block text-sm font-medium text-gray-700 mb-1">评价内容（多个评价用换行分隔，随机选择一个）</label>
                            <textarea class="w-full border border-gray-200 rounded-lg p-3 h-[100px] focus:border-primary focus:ring-1 focus:ring-primary" id="reviewText" name="review_text" placeholder="例如：宝贝收到了，很满意，谢谢卖家！"></textarea>
                        </div>
                        <div class="mb-4" id="imageDirectoryContainer">
                            <label for="imageDirectory" class="block text-sm font-medium text-gray-700 mb-1">评价图片目录（可选）</label>
                            <div class="flex gap-2">
                                <input type="text" class="flex-1 border border-gray-200 rounded-lg p-2 focus:border-primary focus:ring-1 focus:ring-primary" id="imageDirectory" name="image_directory" placeholder="选择包含评价图片的文件夹" readonly>
                                <button type="button" id="selectImageDirBtn" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg flex items-center shadow-sm transition-colors">
                                    <i class="ri-folder-line mr-1"></i>
                                    选择目录
                                </button>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">
                                图片文件命名规则：商品ID.png/jpg 或 商品ID-1.png/jpg（多张图片）<br>
                                如果不选择目录，将使用纯文字评价
                            </p>
                        </div>
                        <div class="flex justify-end">
                            <button type="button" id="saveConfigBtn" class="py-2 px-4 bg-primary hover:bg-primary/90 text-white rounded-lg flex items-center shadow-sm transition-colors">
                                <i class="ri-save-line mr-1"></i>
                                保存设置
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Chrome用户选择 -->
                <div class="glass-effect hover-card rounded-lg shadow-sm overflow-hidden border border-gray-100/50 mb-6">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
                        <h3 class="font-medium text-gray-800">选择Chrome用户</h3>
                    </div>
                    <div class="p-4">
                        <div class="flex justify-between items-center mb-3">
                            <div class="flex items-center">
                                <input type="checkbox" id="checkAllProfiles" class="rounded border-gray-300 text-primary focus:ring-primary/20">
                                <label for="checkAllProfiles" class="ml-2 text-sm text-gray-700">全选</label>
                            </div>
                            <button id="btnRefreshProfiles" class="flex items-center text-sm text-gray-600 hover:text-primary">
                                <i class="ri-refresh-line mr-1"></i>
                                <span>刷新用户列表</span>
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto" style="max-height: 400px; overflow-y: auto;">
                        <table class="w-full">
                            <thead class="sticky top-0 bg-white z-10">
                                <tr class="bg-gray-50 text-left">
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm w-12">
                                        <input type="checkbox" id="selectAllProfiles" class="rounded border-gray-300 text-primary focus:ring-primary/20" />
                                    </th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm">ID</th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm">用户名</th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm">支付密码</th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm">状态</th>
                                </tr>
                            </thead>
                            <tbody id="profilesTableBody">
                                <!-- 用户列表将通过JavaScript动态加载 -->
                                <tr>
                                    <td colspan="4" class="py-4 text-center text-gray-500">
                                        <div class="flex flex-col items-center">
                                            <i class="ri-user-search-line text-3xl mb-2"></i>
                                            <span>正在加载Chrome用户列表...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 消息通知 -->
    <div id="notification" class="fixed top-4 right-4 opacity-0 transform translate-y-[-20px] transition-all duration-300 z-50 glass-effect shadow-lg rounded-lg px-4 py-3 max-w-md bg-white border-l-4 border-green-500 flex items-center" style="pointer-events: none;">
      <div class="text-green-500 mr-3">
        <i class="ri-check-line ri-lg"></i>
      </div>
      <div>
        <p class="font-medium text-gray-800" id="notificationMessage">操作成功</p>
      </div>
    </div>

    <!-- 警告消息容器 -->
    <div id="alertContainer" class="fixed top-4 right-4 z-50" style="max-width: 400px;"></div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script>
      // 自定义通知函数，替换原来的 Toast
      window.showNotification = function(message, type = 'success') {
        const notification = document.getElementById('notification');
        const notificationMsg = document.getElementById('notificationMessage');

        // 设置消息
        notificationMsg.textContent = message;

        // 设置样式
        notification.classList.remove('border-green-500', 'border-red-500', 'border-blue-500');
        const iconContainer = notification.querySelector('div:first-child');
        iconContainer.classList.remove('text-green-500', 'text-red-500', 'text-blue-500');

        // 更新 icon
        const icon = iconContainer.querySelector('i');

        if (type === 'error') {
          notification.classList.add('border-red-500');
          iconContainer.classList.add('text-red-500');
          icon.className = 'ri-error-warning-line ri-lg';
        } else if (type === 'info') {
          notification.classList.add('border-blue-500');
          iconContainer.classList.add('text-blue-500');
          icon.className = 'ri-information-line ri-lg';
        } else { // success
          notification.classList.add('border-green-500');
          iconContainer.classList.add('text-green-500');
          icon.className = 'ri-check-line ri-lg';
        }

        // 显示通知
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';

        // 自动隐藏
        setTimeout(() => {
          notification.style.opacity = '0';
          notification.style.transform = 'translateY(-20px)';
        }, 3000);
      };
    </script>
    <script src="/static/js/chrome-users-mapping.js"></script>
    <script src="/static/js/confirm.js"></script>
    <script src="/static/js/quick-select.js"></script>
</body>
</html>
