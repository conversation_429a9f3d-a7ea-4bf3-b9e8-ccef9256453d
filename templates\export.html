<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单导出 - 淘宝商品下单管理系统</title>

    <!-- TailwindCSS -->
    <script src="/static/css/tailwind.min.css"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#10b981',
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        info: '#3b82f6',
                    },
                    borderRadius: {
                        'lg': '0.5rem'
                    }
                }
            }
        }
    </script>

    <!-- Remixicon (本地版本) -->
    <link href="/static/css/remixicon.css" rel="stylesheet">

    <!-- jQuery -->
    <script src="/static/js/jquery.min.js"></script>

    <style>
        .glass-effect {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }
        .hover-card {
            transition: all 0.3s ease;
        }
        .hover-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.01);
        }
    </style>
</head>
<body
    class="min-h-screen flex"
    style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);"
>
    <div class="flex h-screen w-full">
        <!-- 左侧导航栏 -->
        <aside
          class="w-64 glass-effect shadow-lg flex flex-col h-screen fixed border-r border-gray-100"
        >
          <div class="p-4 border-b border-gray-100 flex items-center">
            <div class="text-2xl font-['Pacifico'] text-primary">TaoBao</div>
            <div class="ml-2 font-semibold text-gray-800">下单管理系统</div>
          </div>
          <nav class="flex-1 overflow-y-auto py-4">
            <ul class="space-y-1">
              <li>
                <a
                  href="/"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-home-line"></i>
                  </div>
                  <span>系统首页</span>
                </a>
              </li>
              <li>
                <a
                  href="/order"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-shopping-cart-line"></i>
                  </div>
                  <span>商品下单</span>
                </a>
              </li>
              <li>
                <a
                  href="/confirm"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-check-double-line"></i>
                  </div>
                  <span>确认收货</span>
                </a>
              </li>
              <li>
                <a
                  href="/cookie"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-key-line"></i>
                  </div>
                  <span>获取Cookie</span>
                </a>
              </li>
              <li>
                <a
                  href="/export"
                  class="flex items-center px-4 py-3 text-primary bg-blue-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-file-download-line"></i>
                  </div>
                  <span>订单导出</span>
                </a>
              </li>
              <li>
                <a
                  href="/chat"
                  class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-6 h-6 flex items-center justify-center mr-3">
                    <i class="ri-chat-3-line"></i>
                  </div>
                  <span>智能聊天</span>
                </a>
              </li>
            </ul>
          </nav>
        </aside>

        <!-- 主内容区 -->
        <div class="flex-1 ml-64 w-full">
            <!-- 主内容区 -->
            <main class="flex-1 p-6 bg-gray-50 w-full max-w-full">
                <div class="glass-effect hover-card rounded-lg shadow-sm p-5 border border-gray-100/50 mb-6 w-full">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-100 -mx-5 -mt-5 mb-5">
                        <h3 class="font-medium text-gray-800">订单导出设置</h3>
                    </div>
                    <form id="exportForm" class="w-full">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="startDate" class="block text-sm font-medium text-gray-700 mb-2">开始日期</label>
                                <input type="date" class="w-full border border-gray-200 rounded-lg p-3 focus:border-primary focus:ring-1 focus:ring-primary" id="startDate" name="start_date">
                            </div>
                            <div>
                                <label for="endDate" class="block text-sm font-medium text-gray-700 mb-2">结束日期</label>
                                <input type="date" class="w-full border border-gray-200 rounded-lg p-3 focus:border-primary focus:ring-1 focus:ring-primary" id="endDate" name="end_date">
                            </div>
                        </div>

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">订单类型</label>
                            <div class="flex flex-wrap gap-4">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="order_type" value="all" class="text-primary focus:ring-primary" checked>
                                    <span class="ml-2">全部订单</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="order_type" value="waitPay" class="text-primary focus:ring-primary">
                                    <span class="ml-2">待付款</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="order_type" value="waitSend" class="text-primary focus:ring-primary">
                                    <span class="ml-2">待发货</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="order_type" value="waitConfirm" class="text-primary focus:ring-primary">
                                    <span class="ml-2">待收货</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="order_type" value="waitRate" class="text-primary focus:ring-primary">
                                    <span class="ml-2">待评价</span>
                                </label>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">导出格式</label>
                            <div class="flex gap-6">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="export_format" value="csv" class="text-primary focus:ring-primary" checked>
                                    <span class="ml-2">CSV</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="export_format" value="excel" class="text-primary focus:ring-primary">
                                    <span class="ml-2">Excel</span>
                                </label>
                            </div>
                        </div>

                        <div class="flex justify-between mt-8">
                            <div>
                                <button type="button" id="previewBtn" class="px-6 py-2.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium">
                                    <i class="ri-eye-line mr-1"></i> 预览数据
                                </button>
                                <button type="button" id="stopExportBtn" class="px-6 py-2.5 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors font-medium ml-2 hidden">
                                    <i class="ri-stop-line mr-1"></i> 停止导出
                                </button>
                            </div>
                            <button type="button" id="exportBtn" class="px-6 py-2.5 bg-primary text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
                                <i class="ri-download-line mr-1"></i> 导出订单
                            </button>
                        </div>

                        <!-- 导出结果区域 -->
                        <div id="exportResults" class="mt-4"></div>

                        <!-- 当前导出状态 -->
                        <div id="currentExportStatus" class="mt-2 text-sm text-gray-600"></div>
                    </form>
                </div>

                <!-- Chrome用户选择 -->
                <div class="glass-effect hover-card rounded-lg shadow-sm p-5 border border-gray-100/50 mb-6">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-100 -mx-5 -mt-5 mb-5 flex justify-between items-center">
                        <h3 class="font-medium text-gray-800">Chrome用户选择</h3>
                        <div>
                            <button id="selectAllBtn" class="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded hover:bg-gray-200">全选</button>
                            <button id="unselectAllBtn" class="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded hover:bg-gray-200 ml-1">取消全选</button>
                        </div>
                    </div>

                    <div class="p-4">
                        <div class="flex justify-between items-center mb-3">
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="checkAllProfiles" class="rounded border-gray-300 text-primary focus:ring-primary/20">
                                    <label for="checkAllProfiles" class="ml-2 text-sm text-gray-700">全选</label>
                                </div>
                                <button id="selectNormalBtn" class="flex items-center text-sm text-green-600 hover:text-green-700 bg-green-50 px-2 py-1 rounded">
                                    <i class="ri-user-follow-line mr-1"></i>
                                    <span>选择所有正常用户</span>
                                </button>
                                <button id="selectLoginFailedBtn" class="flex items-center text-sm text-red-600 hover:text-red-700 bg-red-50 px-2 py-1 rounded">
                                    <i class="ri-user-unfollow-line mr-1"></i>
                                    <span>选择所有登录失效用户</span>
                                </button>
                            </div>
                            <button id="btnRefreshProfiles" class="flex items-center text-sm text-gray-600 hover:text-primary">
                                <i class="ri-refresh-line mr-1"></i>
                                <span>刷新用户列表</span>
                            </button>
                        </div>

                        <!-- 快速选择组件将由JavaScript动态加载 -->
                    </div>

                    <div class="overflow-x-auto w-full" style="max-height: 300px; overflow-y: auto;">
                        <table class="w-full table-auto">
                            <thead class="sticky top-0 bg-white z-10">
                                <tr class="bg-gray-50 text-left">
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm w-12">
                                        <input type="checkbox" id="selectAllProfiles" class="rounded border-gray-300 text-primary focus:ring-primary/20" />
                                    </th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm w-1/6">ID</th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm w-2/5">用户名</th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm w-1/5">状态</th>
                                </tr>
                            </thead>
                            <tbody id="profilesTableBody">
                                <!-- 用户列表将通过JavaScript动态加载 -->
                                <tr>
                                    <td colspan="4" class="py-4 text-center text-gray-500">
                                        <div class="flex flex-col items-center">
                                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
                                            <span>正在加载用户列表...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 预览数据区域 -->
                <div id="previewArea" class="glass-effect hover-card rounded-lg shadow-sm p-5 border border-gray-100/50 mb-6 hidden">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-100 -mx-5 -mt-5 mb-5">
                        <h3 class="font-medium text-gray-800">数据预览</h3>
                    </div>
                    <div class="overflow-x-auto w-full" style="max-height: 400px; overflow-y: auto;">
                        <table class="w-full table-auto">
                            <thead class="sticky top-0 bg-white z-10">
                                <tr class="bg-gray-50 text-left">
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm w-1/6">订单号</th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm w-1/4">商品名称</th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm w-1/6">用户名</th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm w-1/12">金额</th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm w-1/8">状态</th>
                                    <th class="py-3 px-4 font-medium text-gray-600 text-sm w-1/6">创建时间</th>
                                </tr>
                            </thead>
                            <tbody id="previewTableBody">
                                <tr>
                                    <td colspan="6" class="py-4 text-center text-gray-500">点击"预览数据"按钮查看符合条件的订单</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 消息通知 -->
    <div id="notification" class="fixed top-4 right-4 opacity-0 transform translate-y-[-20px] transition-all duration-300 z-50 glass-effect shadow-lg rounded-lg px-4 py-3 max-w-md bg-white border-l-4 border-green-500 flex items-center" style="pointer-events: none;">
      <div class="text-green-500 mr-3">
        <i class="ri-check-line ri-lg"></i>
      </div>
      <div>
        <p class="font-medium text-gray-800" id="notificationMessage">操作成功</p>
      </div>
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script>
      // 页面加载状态指示
      document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM已加载完成');
      });

      window.addEventListener('load', function() {
        console.log('页面完全加载完成');
        document.body.classList.add('page-loaded');
      });

      // 自定义通知函数，替换原来的 Toast
      window.showNotification = function(message, type = 'success') {
        const notification = document.getElementById('notification');
        const notificationMsg = document.getElementById('notificationMessage');

        // 设置消息
        notificationMsg.textContent = message;

        // 设置样式
        notification.classList.remove('border-green-500', 'border-red-500', 'border-blue-500');
        const iconContainer = notification.querySelector('div:first-child');
        iconContainer.classList.remove('text-green-500', 'text-red-500', 'text-blue-500');

        // 更新 icon
        const icon = iconContainer.querySelector('i');

        if (type === 'error') {
          notification.classList.add('border-red-500');
          iconContainer.classList.add('text-red-500');
          icon.className = 'ri-error-warning-line ri-lg';
        } else if (type === 'info') {
          notification.classList.add('border-blue-500');
          iconContainer.classList.add('text-blue-500');
          icon.className = 'ri-information-line ri-lg';
        } else { // success
          notification.classList.add('border-green-500');
          iconContainer.classList.add('text-green-500');
          icon.className = 'ri-check-line ri-lg';
        }

        // 显示通知
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';

        // 自动隐藏
        setTimeout(() => {
          notification.style.opacity = '0';
          notification.style.transform = 'translateY(-20px)';
        }, 3000);
      };
    </script>
    <!-- 异步加载非关键JavaScript文件 -->
    <script src="/static/js/chrome-users-mapping.js" defer></script>
    <script src="/static/js/export.js" defer></script>
    <script src="/static/js/quick-select.js" defer></script>
</body>
</html>
