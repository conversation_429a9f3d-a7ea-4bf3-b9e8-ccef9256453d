<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>淘宝商品下单管理系统</title>
    <script src="/static/css/tailwind.min.css"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#1e40af", secondary: "#3b82f6" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.loli.net" />
    <link rel="preconnect" href="https://fonts.loli.net" crossorigin />
    <link
      href="https://fonts.loli.net/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <!-- 使用本地Remixicon图标库 -->
    <link rel="stylesheet" href="/static/css/remixicon.css" />
    <style>
      body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
      }
      .status-success { background-color: rgba(34, 197, 94, 0.1); color: rgb(22, 163, 74); }
      .status-processing { background-color: rgba(59, 130, 246, 0.1); color: rgb(37, 99, 235); }
      .status-pending { background-color: rgba(156, 163, 175, 0.1); color: rgb(107, 114, 128); }
      .status-error { background-color: rgba(239, 68, 68, 0.1); color: rgb(220, 38, 38); }
      .custom-switch {
      position: relative;
      display: inline-block;
      width: 40px;
      height: 20px;
      }
      .custom-switch input {
      opacity: 0;
      width: 0;
      height: 0;
      }
      .switch-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 20px;
      }
      .switch-slider:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 2px;
      bottom: 2px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
      }
      input:checked + .switch-slider {
      background-color: #1e40af;
      }
      input:checked + .switch-slider:before {
      transform: translateX(20px);
      }
      .glass-effect {
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
      }
      .hover-card {
        transition: all 0.3s ease;
      }
      .hover-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }
    </style>
</head>
<body
  class="min-h-screen flex"
  style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);"
>
    <!-- 左侧导航栏 -->
    <aside
      class="w-64 glass-effect shadow-lg flex flex-col h-screen fixed border-r border-gray-100"
    >
      <div class="p-4 border-b border-gray-100 flex items-center">
        <div class="text-2xl font-['Pacifico'] text-primary">TaoBao</div>
        <div class="ml-2 font-semibold text-gray-800">下单管理系统</div>
      </div>
      <nav class="flex-1 overflow-y-auto py-4">
        <ul class="space-y-1">
          <li>
            <a
              href="/"
              class="flex items-center px-4 py-3 text-gray-800 bg-gradient-to-r from-blue-50 to-transparent border-r-4 border-primary"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-home-line text-primary"></i>
              </div>
              <span>系统首页</span>
            </a>
          </li>
          <li>
            <a
              href="/order"
              class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-shopping-cart-line"></i>
              </div>
              <span>商品下单</span>
            </a>
          </li>
          <li>
            <a
              href="/confirm"
              class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-check-double-line"></i>
              </div>
              <span>确认收货</span>
            </a>
          </li>
          <li>
            <a
              href="/cookie"
              class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              <span>获取Cookie</span>
            </a>
          </li>
          <li>
            <a
              href="/export"
              class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-file-download-line"></i>
              </div>
              <span>订单导出</span>
            </a>
          </li>
          <li>
            <a
              href="/chat"
              class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-chat-3-line"></i>
              </div>
              <span>智能聊天</span>
            </a>
          </li>

        </ul>
      </nav>
      <div class="p-4 border-t border-gray-100">
        <div class="flex items-center">
          <div
            class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center"
          >
            <i class="ri-settings-line"></i>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-700">系统设置</p>
            <p class="text-xs text-gray-500">版本 1.0.0</p>
          </div>
        </div>
      </div>
    </aside>
    <!-- 主内容区 -->
    <main class="ml-64 flex-1 flex flex-col min-h-screen">
      <!-- 顶部状态栏 -->
      <header
        class="glass-effect shadow-sm p-4 flex items-center justify-between sticky top-0 z-10"
      >
        <div class="flex items-center">
          <h1 class="text-xl font-semibold text-gray-800">系统首页</h1>
          <span id="current-date" class="ml-4 text-sm text-gray-500"></span>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex items-center text-sm text-gray-600">
            <div class="w-6 h-6 flex items-center justify-center">
              <i class="ri-chrome-line"></i>
            </div>
            <span class="ml-1" id="profile-count">浏览器用户: <span class="font-medium">0</span></span>
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <div class="w-2 h-2 rounded-full bg-green-500 mr-1"></div>
            <span>系统运行正常</span>
          </div>
        </div>
      </header>
      <!-- 主要内容 -->
      <div class="flex-1 p-6 bg-gray-50">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div
            class="glass-effect hover-card rounded-lg shadow-sm p-5 border border-gray-100/50"
          >
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500">总Chrome用户数</p>
                <p class="text-2xl font-semibold mt-1" id="total-profiles">0</p>
              </div>
              <div
                class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center text-primary"
              >
                <i class="ri-user-line ri-xl"></i>
              </div>
            </div>
          </div>
          <div class="glass-effect hover-card rounded-lg shadow-sm p-5">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500">最大并发浏览器</p>
                <p class="text-2xl font-semibold mt-1" id="max-threads">3</p>
              </div>
              <div
                class="w-12 h-12 rounded-full bg-green-50 flex items-center justify-center text-green-500"
              >
                <i class="ri-chrome-line ri-xl"></i>
              </div>
            </div>
          </div>
          <div class="glass-effect hover-card rounded-lg shadow-sm p-5">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500">下单间隔时间</p>
                <p class="text-2xl font-semibold mt-1"><span id="order-interval">60</span> 秒</p>
              </div>
              <div
                class="w-12 h-12 rounded-full bg-yellow-50 flex items-center justify-center text-yellow-500"
              >
                <i class="ri-time-line ri-xl"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Chrome用户列表 -->
        <div class="glass-effect hover-card rounded-lg shadow-sm mb-6 overflow-hidden border border-gray-100/50">
          <div class="px-4 py-3 bg-gray-50 border-b border-gray-100 flex justify-between items-center">
            <h3 class="font-medium text-gray-800">Chrome用户管理</h3>
            <div class="flex space-x-2">
              <button id="cancelOrdersBtn" class="bg-white border border-gray-300 text-red-600 px-3 py-1.5 rounded-button flex items-center text-sm hover:border-red-500 hover:bg-red-50 transition-colors">
                <i class="ri-close-circle-line mr-1"></i>
                取消待付款订单
              </button>
              <button id="refreshProfilesBtn" class="bg-white border border-gray-300 text-gray-700 px-3 py-1.5 rounded-button flex items-center text-sm hover:border-primary hover:text-primary transition-colors">
                <i class="ri-refresh-line mr-1"></i>
                刷新用户列表
              </button>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead>
                <tr class="bg-gray-50 text-left">
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm w-12">
                    <input
                      type="checkbox"
                      id="selectAllProfiles"
                      class="rounded border-gray-300 text-primary focus:ring-primary/20"
                    />
                  </th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">ID</th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">用户名</th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">配置文件</th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">状态</th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">待付款</th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">待发货</th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">待收货</th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">待评价</th>
                </tr>
              </thead>
              <tbody id="profilesTableBody">
                <!-- 用户列表将通过JavaScript动态加载 -->
                <tr>
                  <td colspan="5" class="py-4 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                      <i class="ri-user-search-line text-3xl mb-2"></i>
                      <span>正在加载Chrome用户列表...</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 系统设置 -->
        <div class="glass-effect hover-card rounded-lg shadow-sm p-5 mb-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="font-medium text-gray-800">系统配置</h3>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="maxThreads" class="block text-sm font-medium text-gray-700 mb-1">最大并发浏览器数</label>
              <div class="relative">
                <input type="number" id="maxThreads" min="1" max="10" value="3" class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-button text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400">
                  <i class="ri-chrome-line"></i>
                </div>
              </div>
            </div>
            <div>
              <label for="orderInterval" class="block text-sm font-medium text-gray-700 mb-1">下单间隔时间(秒)</label>
              <div class="relative">
                <input type="number" id="orderInterval" min="1" value="60" class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-button text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400">
                  <i class="ri-time-line"></i>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <button id="saveConfigBtn" class="bg-gradient-to-r from-primary to-blue-500 text-white px-4 py-2 rounded-button flex items-center hover:shadow-lg hover:shadow-primary/20 transition-all duration-300">
              <i class="ri-save-line mr-1"></i>
              保存配置
            </button>
          </div>
        </div>
      </div>
    </main>

    <!-- 消息通知 -->
    <div id="notification" class="fixed top-4 right-4 opacity-0 transform translate-y-[-20px] transition-all duration-300 z-50 glass-effect shadow-lg rounded-lg px-4 py-3 max-w-md bg-white border-l-4 border-green-500 flex items-center" style="pointer-events: none;">
      <div class="text-green-500 mr-3">
        <i class="ri-check-line ri-lg"></i>
      </div>
      <div>
        <p class="font-medium text-gray-800" id="notificationMessage">操作成功</p>
      </div>
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/chrome-users-mapping.js"></script>
    <script src="/static/js/main.js"></script>
    <script>
      // 显示当前日期
      document.addEventListener('DOMContentLoaded', function() {
        const dateOptions = {
          weekday: 'long',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        };
        const now = new Date();
        document.getElementById('current-date').textContent = now.toLocaleDateString('zh-CN', dateOptions);

        // 自定义通知函数，替换原来的 Toast
        window.showNotification = function(message, type = 'success') {
          const notification = document.getElementById('notification');
          const notificationMsg = document.getElementById('notificationMessage');

          // 设置消息
          notificationMsg.textContent = message;

          // 设置样式
          notification.classList.remove('border-green-500', 'border-red-500', 'border-blue-500');
          const iconContainer = notification.querySelector('div:first-child');
          iconContainer.classList.remove('text-green-500', 'text-red-500', 'text-blue-500');

          // 更新 icon
          const icon = iconContainer.querySelector('i');

          if (type === 'error') {
            notification.classList.add('border-red-500');
            iconContainer.classList.add('text-red-500');
            icon.className = 'ri-error-warning-line ri-lg';
          } else if (type === 'info') {
            notification.classList.add('border-blue-500');
            iconContainer.classList.add('text-blue-500');
            icon.className = 'ri-information-line ri-lg';
          } else { // success
            notification.classList.add('border-green-500');
            iconContainer.classList.add('text-green-500');
            icon.className = 'ri-check-line ri-lg';
          }

          // 显示通知
          notification.style.opacity = '1';
          notification.style.transform = 'translateY(0)';

          // 自动隐藏
          setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
          }, 3000);
        };
      });
    </script>
</body>
</html>