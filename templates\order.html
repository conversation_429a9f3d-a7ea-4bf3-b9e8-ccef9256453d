<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>商品下单 - 淘宝商品下单管理系统</title>
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <script src="/static/css/tailwind.min.css"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#1e40af", secondary: "#3b82f6" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.loli.net" />
    <link rel="preconnect" href="https://fonts.loli.net" crossorigin />
    <link
      href="https://fonts.loli.net/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <!-- 使用本地Remixicon图标库 -->
    <link rel="stylesheet" href="/static/css/remixicon.css" />
    <style>
      body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
      }
      .status-success { background-color: rgba(34, 197, 94, 0.1); color: rgb(22, 163, 74); }
      .status-processing { background-color: rgba(59, 130, 246, 0.1); color: rgb(37, 99, 235); }
      .status-pending { background-color: rgba(156, 163, 175, 0.1); color: rgb(107, 114, 128); }
      .status-error { background-color: rgba(239, 68, 68, 0.1); color: rgb(220, 38, 38); }
      .custom-switch {
      position: relative;
      display: inline-block;
      width: 40px;
      height: 20px;
      }
      .custom-switch input {
      opacity: 0;
      width: 0;
      height: 0;
      }
      .switch-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 20px;
      }
      .switch-slider:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 2px;
      bottom: 2px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
      }
      input:checked + .switch-slider {
      background-color: #1e40af;
      }
      input:checked + .switch-slider:before {
      transform: translateX(20px);
      }
      .glass-effect {
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
      }
      .hover-card {
        transition: all 0.3s ease;
      }
      .hover-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }
    </style>
  </head>
  <body
    class="min-h-screen flex"
    style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);"
  >
    <!-- 左侧导航栏 -->
    <aside
      class="w-64 glass-effect shadow-lg flex flex-col h-screen fixed border-r border-gray-100"
    >
      <div class="p-4 border-b border-gray-100 flex items-center">
        <div class="text-2xl font-['Pacifico'] text-primary">TaoBao</div>
        <div class="ml-2 font-semibold text-gray-800">下单管理系统</div>
      </div>
      <nav class="flex-1 overflow-y-auto py-4">
        <ul class="space-y-1">
          <li>
            <a
              href="/"
              class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-home-line"></i>
              </div>
              <span>系统首页</span>
            </a>
          </li>
          <li>
            <a
              href="/order"
              class="flex items-center px-4 py-3 text-gray-800 bg-gradient-to-r from-blue-50 to-transparent border-r-4 border-primary"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-shopping-cart-line text-primary"></i>
              </div>
              <span>商品下单</span>
            </a>
          </li>
          <li>
            <a
              href="/confirm"
              class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-check-double-line"></i>
              </div>
              <span>确认收货</span>
            </a>
          </li>
          <li>
            <a
              href="/cookie"
              class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              <span>获取Cookie</span>
            </a>
          </li>
          <li>
            <a
              href="/export"
              class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-file-download-line"></i>
              </div>
              <span>订单导出</span>
            </a>
          </li>
          <li>
            <a
              href="/chat"
              class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50"
            >
              <div class="w-6 h-6 flex items-center justify-center mr-3">
                <i class="ri-chat-3-line"></i>
              </div>
              <span>智能聊天</span>
            </a>
          </li>

        </ul>
      </nav>
      <div class="p-4 border-t border-gray-100">
        <div class="flex items-center">
          <div
            class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center"
          >
            <i class="ri-settings-line"></i>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-700">系统设置</p>
            <p class="text-xs text-gray-500">版本 1.0.0</p>
          </div>
        </div>
      </div>
    </aside>
    <!-- 主内容区 -->
    <main class="ml-64 flex-1 flex flex-col min-h-screen">
      <!-- 顶部状态栏 -->
      <header
        class="glass-effect shadow-sm p-4 flex items-center justify-between sticky top-0 z-10"
      >
        <div class="flex items-center">
          <h1 class="text-xl font-semibold text-gray-800">商品下单</h1>
          <span id="current-date" class="ml-4 text-sm text-gray-500"></span>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex items-center text-sm text-gray-600">
            <div class="w-6 h-6 flex items-center justify-center">
              <i class="ri-chrome-line"></i>
            </div>
            <span class="ml-1" id="profile-count">浏览器用户: <span class="font-medium">0</span></span>
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <div class="w-2 h-2 rounded-full bg-green-500 mr-1"></div>
            <span>系统运行正常</span>
          </div>
        </div>
      </header>
      <!-- 主要内容 -->
      <div class="flex-1 p-6 bg-gray-50">
        <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
          <div class="flex items-center gap-3">
            <a href="javascript:void(0)" id="startMultiTaskBtn" class="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-5 py-2 rounded-button flex items-center whitespace-nowrap hover:shadow-lg hover:shadow-emerald-500/20 transition-all duration-300" onclick="window.startMultiThreadTask(); return false;">
              <div class="w-5 h-5 flex items-center justify-center mr-2">
                <i class="ri-rocket-line"></i>
              </div>
              <span>启动多线程下单</span>
            </a>
            <a href="javascript:void(0)" id="stopTaskBtn" class="bg-gradient-to-r from-red-500 to-pink-500 text-white px-5 py-2 rounded-button flex items-center whitespace-nowrap hover:shadow-lg hover:shadow-red-500/20 transition-all duration-300" onclick="if(confirm('确定要停止所有任务吗？这将结束所有Chrome进程和confirm_receipt.exe进程。')) { window.stopAllProcesses(); } return false;">
              <div class="w-5 h-5 flex items-center justify-center mr-2">
                <i class="ri-stop-line"></i>
              </div>
              <span>停止所有任务</span>
            </a>
            <a href="javascript:void(0)" id="clearTaskBtn" class="bg-gradient-to-r from-orange-500 to-amber-500 text-white px-5 py-2 rounded-button flex items-center whitespace-nowrap hover:shadow-lg hover:shadow-orange-500/20 transition-all duration-300" onclick="if(confirm('确定要清空所有任务吗？这将清空所有下单链接并取消所有用户选择。')) { window.clearAllTasks(); } return false;">
              <div class="w-5 h-5 flex items-center justify-center mr-2">
                <i class="ri-delete-bin-line"></i>
              </div>
              <span>清空所有任务</span>
            </a>
            <div class="flex items-center">
              <label for="maxThreads" class="text-sm text-gray-700 mr-2">最大线程数:</label>
              <input type="number" id="maxThreads" min="1" max="10" value="2" class="w-16 px-2 py-1 border border-gray-300 rounded-button focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" />
            </div>
            <div style="display: none;">
              <label class="custom-switch mr-2">
                <input type="checkbox" id="randomOrderCheckTop">
                <span class="switch-slider"></span>
              </label>
              <span class="text-sm text-gray-600">随机打乱下单顺序</span>
            </div>
            <div>
              <label class="custom-switch mr-2">
                <input type="checkbox" id="autoPaymentCheck">
                <span class="switch-slider"></span>
              </label>
              <span class="text-sm text-gray-600">提交订单后自动付款</span>
            </div>
            <button id="refreshProfilesBtn" class="bg-white border border-gray-300 text-gray-700 w-10 h-10 rounded-button flex items-center justify-center hover:border-primary hover:text-primary transition-colors">
              <i class="ri-refresh-line"></i>
            </button>
          </div>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div
            class="glass-effect hover-card rounded-lg shadow-sm p-5 border border-gray-100/50"
          >
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500">总任务数</p>
                <p class="text-2xl font-semibold mt-1" id="totalTasks">0</p>
              </div>
              <div
                class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center text-primary"
              >
                <i class="ri-todo-line ri-xl"></i>
              </div>
            </div>
          </div>
          <div class="glass-effect hover-card rounded-lg shadow-sm p-5">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500">完成任务</p>
                <p class="text-2xl font-semibold mt-1" id="completedTasks">0</p>
              </div>
              <div
                class="w-12 h-12 rounded-full bg-green-50 flex items-center justify-center text-green-500"
              >
                <i class="ri-check-line ri-xl"></i>
              </div>
            </div>
          </div>
          <div class="glass-effect hover-card rounded-lg shadow-sm p-5">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500">等待任务</p>
                <p class="text-2xl font-semibold mt-1" id="pendingTasks">0</p>
              </div>
              <div
                class="w-12 h-12 rounded-full bg-yellow-50 flex items-center justify-center text-yellow-500"
              >
                <i class="ri-time-line ri-xl"></i>
              </div>
            </div>
          </div>
          <div class="glass-effect hover-card rounded-lg shadow-sm p-5">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500">任务状态</p>
                <p class="text-2xl font-semibold mt-1" id="taskStatus">未运行</p>
              </div>
              <div
                class="w-12 h-12 rounded-full bg-purple-50 flex items-center justify-center text-purple-500"
              >
                <i class="ri-equalizer-line ri-xl"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 任务进度 -->
        <div class="glass-effect hover-card rounded-lg shadow-sm mb-6 p-5 border border-gray-100/50">
          <h3 class="font-medium text-gray-800 mb-4">任务进度</h3>
          <div class="w-full bg-gray-100 rounded-full h-4 mb-2">
            <div id="taskProgress" class="bg-primary h-4 rounded-full text-xs text-white flex items-center justify-center" style="width: 0%">0%</div>
          </div>
        </div>

        <!-- Chrome用户选择 -->
        <div class="glass-effect hover-card rounded-lg shadow-sm mb-6 overflow-hidden border border-gray-100/50">
          <div class="px-4 py-3 bg-gray-50 border-b border-gray-100">
            <h3 class="font-medium text-gray-800">选择Chrome用户</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-white z-10">
                <tr class="bg-gray-50 text-left">
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm w-12">
                    <input
                      type="checkbox"
                      id="selectAllProfiles"
                      class="rounded border-gray-300 text-primary focus:ring-primary/20"
                    />
                  </th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">ID</th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">用户名</th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">配置文件</th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">状态</th>
                  <th class="py-3 px-4 font-medium text-gray-600 text-sm">下单链接</th>
                </tr>
              </thead>
              <tbody id="profilesTableBody">
                <!-- 用户列表将通过JavaScript动态加载 -->
                <tr>
                  <td colspan="6" class="py-4 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                      <i class="ri-user-search-line text-3xl mb-2"></i>
                      <span>正在加载Chrome用户列表...</span>
                    </div>
                  </td>
                </tr>
                <!-- 测试用户将通过JavaScript动态加载 -->
              </tbody>
            </table>
          </div>
        </div>



        <!-- 执行中任务监控 -->
        <div class="glass-effect hover-card rounded-lg shadow-sm p-5" id="runningTasksContainer" style="display: none;">
          <div class="flex items-center justify-between mb-4">
            <h3 class="font-medium text-gray-800">执行中任务监控</h3>
            <div class="flex items-center">
              <label class="custom-switch mr-2">
                <input type="checkbox" checked id="autoRefreshSwitch" />
                <span class="switch-slider"></span>
              </label>
              <span class="text-sm text-gray-600">自动刷新</span>
            </div>
          </div>
          <div class="space-y-4" id="runningTasksList">
            <!-- 执行中任务将通过JavaScript动态加载 -->
          </div>
        </div>


      </div>
    </main>

    <!-- 消息通知 -->
    <div id="notification" class="fixed top-4 right-4 opacity-0 transform translate-y-[-20px] transition-all duration-300 z-50 glass-effect shadow-lg rounded-lg px-4 py-3 max-w-md bg-white border-l-4 border-green-500 flex items-center" style="pointer-events: none;">
      <div class="text-green-500 mr-3">
        <i class="ri-check-line ri-lg"></i>
      </div>
      <div>
        <p class="font-medium text-gray-800" id="notificationMessage">操作成功</p>
      </div>
    </div>

    <!-- 警告消息容器 -->
    <div id="alertContainer" class="fixed top-4 right-4 z-50" style="max-width: 400px;"></div>

    <!-- 回到顶部和底部按钮 -->
    <div class="fixed right-6 bottom-6 flex flex-col space-y-2 z-50">
      <button id="scrollToTop" class="w-10 h-10 rounded-full bg-emerald-500 text-white flex items-center justify-center shadow-lg hover:bg-emerald-600 transition-all">
        <span class="text-lg font-bold">&#9650;</span>
      </button>
      <button id="scrollToBottom" class="w-10 h-10 rounded-full bg-amber-500 text-white flex items-center justify-center shadow-lg hover:bg-amber-600 transition-all">
        <span class="text-lg font-bold">&#9660;</span>
      </button>
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script>
      // 确保jQuery加载完成
      if (typeof jQuery === 'undefined') {
        alert('jQuery未加载，页面可能无法正常工作');
      } else {
        console.log('jQuery已加载，版本:', jQuery.fn.jquery);
      }

      // 删除HTML中的startMultiThreadTask函数，避免冲突

      // 为了兼容性，保留testButtonClick函数
      function testButtonClick() {
        if (typeof window.startMultiThreadTask === 'function') {
          window.startMultiThreadTask();
        } else {
          alert('系统尚未完全加载，请稍后再试');
        }
      }
    </script>
    <script src="/static/js/chrome-users-mapping.js"></script>
    <script src="/static/js/main.js"></script>
    <script src="/static/js/order.js"></script>
    <script src="/static/js/quick-select.js"></script>
    <script>
      // 删除这行代码，避免冲突
    </script>
    <script>
      // 显示当前日期
      document.addEventListener('DOMContentLoaded', function() {
        // 不再在这里绑定事件，避免重复绑定
        const dateOptions = {
          weekday: 'long',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        };
        const now = new Date();
        document.getElementById('current-date').textContent = now.toLocaleDateString('zh-CN', dateOptions);

        // 绑定回到顶部和底部按钮事件
        document.getElementById('scrollToTop').addEventListener('click', function() {
          window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        document.getElementById('scrollToBottom').addEventListener('click', function() {
          window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
        });

        // 自定义通知函数，替换原来的 Toast
        window.showNotification = function(message, type = 'success') {
          const notification = document.getElementById('notification');
          const notificationMsg = document.getElementById('notificationMessage');

          // 设置消息
          notificationMsg.textContent = message;

          // 设置样式
          notification.classList.remove('border-green-500', 'border-red-500', 'border-blue-500');
          const iconContainer = notification.querySelector('div:first-child');
          iconContainer.classList.remove('text-green-500', 'text-red-500', 'text-blue-500');

          // 更新 icon
          const icon = iconContainer.querySelector('i');

          if (type === 'error') {
            notification.classList.add('border-red-500');
            iconContainer.classList.add('text-red-500');
            icon.className = 'ri-error-warning-line ri-lg';
          } else if (type === 'info') {
            notification.classList.add('border-blue-500');
            iconContainer.classList.add('text-blue-500');
            icon.className = 'ri-information-line ri-lg';
          } else { // success
            notification.classList.add('border-green-500');
            iconContainer.classList.add('text-green-500');
            icon.className = 'ri-check-line ri-lg';
          }

          // 显示通知
          notification.style.opacity = '1';
          notification.style.transform = 'translateY(0)';

          // 自动隐藏
          setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
          }, 3000);
        };

        // 监听任务状态变化，显示/隐藏任务监控区域
        $(document).on('taskStatusChanged', function(e, isRunning) {
          if (isRunning) {
            $('#runningTasksContainer').fadeIn();
          } else {
            $('#runningTasksContainer').fadeOut();
          }
        });

        // 自动刷新开关
        $('#autoRefreshSwitch').on('change', function() {
          const isChecked = $(this).prop('checked');
          console.log('自动刷新状态:', isChecked);
          // 触发自定义事件，让order.js中的轮询机制知道是否需要继续
          $(document).trigger('autoRefreshChanged', [isChecked]);
        });
      });
    </script>
  </body>
</html>