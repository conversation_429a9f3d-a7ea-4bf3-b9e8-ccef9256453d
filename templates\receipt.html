<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>确认收货 - 淘宝商品下单管理系统</title>

    <!-- TailwindCSS -->
    <script src="/static/css/tailwind.min.css"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#10b981',
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        info: '#3b82f6',
                    },
                    borderRadius: {
                        'lg': '0.5rem'
                    }
                }
            }
        }
    </script>

    <!-- 谷歌字体 -->
    <link rel="preconnect" href="https://fonts.loli.net">
    <link rel="preconnect" href="https://fonts.loli.net" crossorigin>
    <link href="https://fonts.loli.net/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Remix Icon -->
    <link href="/static/css/remixicon.min.css" rel="stylesheet">

    <!-- 自定义样式 -->
    <style>
        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #f9fafb;
        }

        /* 状态指示器 */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 6px;
        }
        .status-active { background-color: #10b981; }
        .status-pending { background-color: #f59e0b; }
        .status-inactive { background-color: #9ca3af; }
        .status-error { background-color: #ef4444; }

        /* 自定义开关 */
        .custom-switch {
            position: relative;
            display: inline-block;
            width: 42px;
            height: 22px;
        }
        .custom-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #e2e8f0;
            transition: .4s;
            border-radius: 22px;
        }
        .switch-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .switch-slider {
            background-color: #3b82f6;
        }
        input:checked + .switch-slider:before {
            transform: translateX(20px);
        }

        /* 卡片悬浮效果 */
        .hover-card {
            background-color: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .hover-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* 淡入动画 */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            background: white;
            box-shadow: 4px 0 10px -5px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }
        .sidebar-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: #64748b;
            border-radius: 0.375rem;
            transition: all 0.2s;
            margin: 0.25rem 0;
        }
        .sidebar-link:hover {
            background-color: rgba(59, 130, 246, 0.08);
            color: #3b82f6;
        }
        .sidebar-link.active {
            background-color: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            font-weight: 500;
        }
        .sidebar-link i {
            margin-right: 0.625rem;
            font-size: 1.25rem;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <div class="flex h-screen">
        <!-- 侧边栏导航 -->
        <div class="sidebar h-screen flex flex-col py-6 fixed left-0 top-0 bottom-0 z-10">
            <div class="px-6 mb-8">
                <h1 class="text-xl font-bold flex items-center">
                    <i class="ri-shopping-cart-fill text-primary mr-3"></i>
                    <span>淘宝下单系统</span>
                </h1>
            </div>

            <div class="flex-1 px-3">
                <a href="/index" class="sidebar-link">
                    <i class="ri-dashboard-line"></i>
                    <span>系统首页</span>
                </a>
                <a href="/order" class="sidebar-link">
                    <i class="ri-shopping-bag-line"></i>
                    <span>商品下单</span>
                </a>
                <a href="/receipt" class="sidebar-link active">
                    <i class="ri-checkbox-circle-line"></i>
                    <span>确认收货</span>
                </a>
                <a href="/export" class="sidebar-link">
                    <i class="ri-file-download-line"></i>
                    <span>订单导出</span>
                </a>
            </div>

            <div class="px-6 mt-auto">
                <div class="border-t border-gray-100 pt-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white font-medium">
                                M
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">管理员</p>
                                <p class="text-xs text-gray-500"><EMAIL></p>
                            </div>
                        </div>
                        <button class="text-gray-400 hover:text-primary">
                            <i class="ri-settings-4-line"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="ml-[260px] flex-1 flex flex-col h-screen">
            <!-- 顶部状态栏 -->
            <header class="bg-white border-b border-gray-100 py-4 px-8 shadow-sm">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <h2 class="text-lg font-semibold">确认收货</h2>
                        <div class="ml-4 flex items-center bg-blue-50 text-primary rounded-full px-3 py-1 text-xs">
                            <span class="status-indicator status-active"></span>
                            <span>系统状态：正常</span>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="flex items-center mr-4">
                            <span class="text-sm text-gray-600 mr-2">自动刷新：</span>
                            <label class="custom-switch">
                                <input type="checkbox" id="autoRefreshToggle" checked>
                                <span class="switch-slider"></span>
                            </label>
                        </div>

                        <button id="refreshBtn" class="flex items-center text-sm text-gray-600 hover:text-primary">
                            <i class="ri-refresh-line mr-1"></i>
                            <span>刷新</span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- 主内容区 -->
            <main class="flex-1 p-8 overflow-auto">
                <!-- 操作按钮区 -->
                <div class="flex space-x-4 mb-6">
                    <button id="btnStartTask" class="py-2 px-6 bg-primary hover:bg-primary/90 text-white rounded-lg flex items-center shadow-sm transition-colors">
                        <i class="ri-play-line mr-2"></i>
                        开始确认收货
                    </button>

                    <button id="btnStopTask" class="py-2 px-6 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg flex items-center shadow-sm transition-colors opacity-50 cursor-not-allowed" disabled>
                        <i class="ri-stop-line mr-2"></i>
                        停止任务
                    </button>
                </div>

                <!-- 信息统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="border border-gray-100/50 hover-card rounded-lg p-5 hover:border-primary/20 transition-colors duration-300 bg-white">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-md bg-blue-50 flex items-center justify-center text-primary">
                                <i class="ri-list-check-2"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-sm font-medium text-gray-500">待确认订单</h3>
                                <p class="mt-1 text-xl font-semibold" id="totalTasks">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="border border-gray-100/50 hover-card rounded-lg p-5 hover:border-primary/20 transition-colors duration-300 bg-white">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-md bg-green-50 flex items-center justify-center text-success">
                                <i class="ri-checkbox-circle-line"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-sm font-medium text-gray-500">已确认订单</h3>
                                <p class="mt-1 text-xl font-semibold" id="completedTasks">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="border border-gray-100/50 hover-card rounded-lg p-5 hover:border-primary/20 transition-colors duration-300 bg-white">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-md bg-yellow-50 flex items-center justify-center text-warning">
                                <i class="ri-time-line"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-sm font-medium text-gray-500">等待确认</h3>
                                <p class="mt-1 text-xl font-semibold" id="pendingTasks">0</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务进度 -->
                <div class="border border-gray-100/50 hover-card rounded-lg p-5 hover:border-primary/20 transition-colors duration-300 bg-white mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="text-sm font-medium text-gray-700">任务进度</h3>
                        <span class="text-xs text-gray-500">
                            <span id="taskStatus">未运行</span>
                        </span>
                    </div>
                    <div class="w-full bg-gray-100 rounded-full h-2.5">
                        <div class="bg-primary h-2.5 rounded-full" id="taskProgress" style="width: 0%">0%</div>
                    </div>
                </div>

                <!-- 模式选择 -->
                <div class="border border-gray-100/50 hover-card rounded-lg p-5 hover:border-primary/20 transition-colors duration-300 bg-white mb-6">
                    <h3 class="text-sm font-medium text-gray-700 mb-4">选择确认收货模式</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="border border-gray-200 hover:border-primary rounded-lg p-4 cursor-pointer transition-colors" id="modeManual">
                            <div class="flex items-center">
                                <input type="radio" name="receiptMode" id="radioManual" class="mr-3" value="manual" checked>
                                <div>
                                    <h4 class="font-medium">手动输入订单</h4>
                                    <p class="text-xs text-gray-500 mt-1">通过手动输入订单号进行确认收货</p>
                                </div>
                            </div>
                        </div>

                        <div class="border border-gray-200 hover:border-primary rounded-lg p-4 cursor-pointer transition-colors" id="modeAuto">
                            <div class="flex items-center">
                                <input type="radio" name="receiptMode" id="radioAuto" class="mr-3" value="auto">
                                <div>
                                    <h4 class="font-medium">自动确认所有待收货订单</h4>
                                    <p class="text-xs text-gray-500 mt-1">系统自动检索所有待收货订单并确认</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户选择与订单输入 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Chrome用户选择 -->
                    <div class="border border-gray-100/50 hover-card rounded-lg p-5 hover:border-primary/20 transition-colors duration-300 bg-white">
                        <h3 class="text-sm font-medium text-gray-700 mb-4">选择Chrome用户（执行确认收货）</h3>

                        <div class="flex justify-between items-center mb-3">
                            <div class="flex items-center">
                                <input type="checkbox" id="checkAllProfiles" class="mr-2">
                                <label for="checkAllProfiles" class="text-sm cursor-pointer">全选</label>
                            </div>
                            <button id="btnRefreshProfiles" class="text-xs text-primary flex items-center">
                                <i class="ri-refresh-line mr-1"></i>
                                刷新用户列表
                            </button>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4 max-h-[250px] overflow-y-auto" id="profilesContainer">
                            <div class="text-center py-8 text-gray-500">
                                <i class="ri-loader-4-line text-xl animate-spin mb-2"></i>
                                <p>加载Chrome用户中...</p>
                            </div>
                        </div>
                    </div>

                    <!-- 订单输入 -->
                    <div class="border border-gray-100/50 hover-card rounded-lg p-5 hover:border-primary/20 transition-colors duration-300 bg-white" id="manualInputSection">
                        <h3 class="text-sm font-medium text-gray-700 mb-4">输入需要确认收货的订单号</h3>

                        <div class="mb-4">
                            <textarea id="orderIds" class="w-full border border-gray-200 rounded-lg p-3 h-[150px] focus:border-primary focus:ring-1 focus:ring-primary" placeholder="请输入订单号，每行一个&#10;例如：&#10;2023102612345678&#10;2023102687654321"></textarea>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm">
                                <input type="checkbox" id="checkRandomOrder" class="mr-2">
                                <label for="checkRandomOrder" class="cursor-pointer">随机打乱订单顺序</label>
                            </div>
                            <button id="btnLoadWaitingOrders" class="text-sm text-primary flex items-center">
                                <i class="ri-download-line mr-1"></i>
                                加载待收货订单
                            </button>
                        </div>
                    </div>

                    <!-- 自动模式提示 -->
                    <div class="border border-gray-100/50 hover-card rounded-lg p-5 hover:border-primary/20 transition-colors duration-300 bg-white hidden" id="autoModeSection">
                        <h3 class="text-sm font-medium text-gray-700 mb-4">自动确认收货说明</h3>

                        <div class="bg-blue-50 text-primary p-4 rounded-lg">
                            <div class="flex">
                                <i class="ri-information-line mr-2"></i>
                                <div>
                                    <p class="font-medium">自动模式将执行以下操作：</p>
                                    <ul class="list-disc ml-5 mt-2 text-sm">
                                        <li>登录所选Chrome用户的淘宝账号</li>
                                        <li>自动检索所有"待收货"状态的订单</li>
                                        <li>依次确认收货</li>
                                        <li>记录确认结果</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务执行列表 -->
                <div id="runningTasksSection" class="mt-6 hidden">
                    <h3 class="text-sm font-medium text-gray-700 mb-4">任务执行状态</h3>
                    <div id="runningTasksList" class="space-y-4"></div>
                </div>
            </main>
        </div>
    </div>

    <!-- jQuery -->
    <script src="/static/js/jquery.min.js"></script>
    <!-- 通用JS -->
    <script src="/static/js/main.js"></script>
    <!-- Chrome用户映射JS -->
    <script src="/static/js/chrome-users-mapping.js"></script>
    <!-- 快速选择JS -->
    <script src="/static/js/quick-select.js"></script>
    <!-- 确认收货页面JS -->
    <script src="/static/js/receipt.js"></script>
</body>
</html>