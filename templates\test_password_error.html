<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试密码错误状态更新</title>
    <link rel="stylesheet" href="/static/css/tailwind.min.css">
    <link rel="stylesheet" href="/static/css/remixicon.css">
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn-primary {
            background-color: #4f46e5;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-primary:hover {
            background-color: #4338ca;
        }
        .result-box {
            background-color: #f3f4f6;
            border-radius: 4px;
            padding: 16px;
            margin-top: 16px;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-2xl font-bold mb-4">测试密码错误状态更新</h1>
        
        <div class="card">
            <h2 class="text-lg font-semibold mb-4">更新用户状态为密码错误</h2>
            
            <div class="mb-4">
                <label for="profileId" class="block text-sm font-medium text-gray-700 mb-1">Chrome用户ID:</label>
                <input type="text" id="profileId" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="输入Chrome用户ID">
            </div>
            
            <button id="updateBtn" class="btn-primary">
                <i class="ri-error-warning-line mr-1"></i>
                更新为密码错误状态
            </button>
            
            <div id="result" class="result-box">
                <p class="text-gray-500">结果将显示在这里...</p>
            </div>
        </div>
        
        <div class="card">
            <h2 class="text-lg font-semibold mb-4">Chrome用户列表</h2>
            <button id="refreshBtn" class="btn-primary mb-4">
                <i class="ri-refresh-line mr-1"></i>
                刷新用户列表
            </button>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户名</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="profilesTableBody" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                                <p>加载中...</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载Chrome用户列表
            loadProfiles();
            
            // 绑定更新按钮事件
            document.getElementById('updateBtn').addEventListener('click', function() {
                const profileId = document.getElementById('profileId').value.trim();
                if (!profileId) {
                    showResult('请输入Chrome用户ID', 'error');
                    return;
                }
                updatePasswordError(profileId);
            });
            
            // 绑定刷新按钮事件
            document.getElementById('refreshBtn').addEventListener('click', loadProfiles);
        });
        
        // 加载Chrome用户列表
        function loadProfiles() {
            fetch('/api/profiles')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderProfiles(data.profiles);
                    } else {
                        showResult('加载用户列表失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('加载用户列表失败:', error);
                    showResult('加载用户列表失败', 'error');
                });
        }
        
        // 渲染用户列表
        function renderProfiles(profiles) {
            const tableBody = document.getElementById('profilesTableBody');
            tableBody.innerHTML = '';
            
            if (!profiles || profiles.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                            <p>没有找到Chrome用户</p>
                        </td>
                    </tr>
                `;
                return;
            }
            
            profiles.forEach(profile => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                
                const statusClass = profile.status === '密码错误' ? 'text-red-600 bg-red-100' : 
                                   profile.status === '正常' ? 'text-green-600 bg-green-100' : 
                                   'text-gray-600 bg-gray-100';
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${profile.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${profile.name || '未命名'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                            ${profile.status || '正常'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button class="text-indigo-600 hover:text-indigo-900" onclick="updatePasswordError('${profile.id}')">
                            设为密码错误
                        </button>
                    </td>
                `;
                
                tableBody.appendChild(row);
            });
        }
        
        // 更新用户状态为密码错误
        function updatePasswordError(profileId) {
            showResult('正在更新状态...', 'info');
            
            fetch('/api/update_password_error', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    profile_id: profileId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult('用户状态已更新为密码错误', 'success');
                    // 刷新用户列表
                    loadProfiles();
                } else {
                    showResult('更新状态失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('更新状态请求失败:', error);
                showResult('更新状态请求失败', 'error');
            });
        }
        
        // 显示结果
        function showResult(message, type = 'info') {
            const resultElement = document.getElementById('result');
            let bgColor = 'bg-gray-100';
            let textColor = 'text-gray-800';
            
            if (type === 'success') {
                bgColor = 'bg-green-100';
                textColor = 'text-green-800';
            } else if (type === 'error') {
                bgColor = 'bg-red-100';
                textColor = 'text-red-800';
            } else if (type === 'info') {
                bgColor = 'bg-blue-100';
                textColor = 'text-blue-800';
            }
            
            resultElement.className = `result-box ${bgColor} ${textColor}`;
            resultElement.innerHTML = `<p>${message}</p>`;
        }
    </script>
</body>
</html>
