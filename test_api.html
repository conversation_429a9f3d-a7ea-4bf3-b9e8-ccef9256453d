<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>API测试页面</h1>
    <button id="testBtn">测试用户列表API</button>
    <div id="result"></div>

    <script>
        $('#testBtn').click(function() {
            console.log('开始测试API...');
            $('#result').html('正在加载...');
            
            $.ajax({
                url: '/api/profiles',
                method: 'GET',
                timeout: 10000,
                success: function(data) {
                    console.log('API响应成功:', data);
                    console.log('数据类型:', typeof data);
                    console.log('profiles存在:', !!data.profiles);
                    console.log('profiles长度:', data.profiles ? data.profiles.length : 'undefined');
                    
                    if (data.profiles && Array.isArray(data.profiles)) {
                        $('#result').html(`
                            <h3>成功加载 ${data.profiles.length} 个用户</h3>
                            <pre>${JSON.stringify(data.profiles[0], null, 2)}</pre>
                        `);
                    } else {
                        $('#result').html(`
                            <h3>数据格式错误</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('API请求失败:', status, error);
                    $('#result').html(`
                        <h3>请求失败</h3>
                        <p>状态: ${status}</p>
                        <p>错误: ${error}</p>
                    `);
                }
            });
        });
    </script>
</body>
</html>
