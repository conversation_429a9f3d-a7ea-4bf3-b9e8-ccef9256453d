import json
import uuid

def create_image_data(image_url, goods_id, pub_session=None):
    """创建完整的图片数据结构，与淘宝评价系统完全兼容 - 精确匹配实际POST数据格式"""

    # 如果没有提供pub_session，生成一个新的（每张图片不同）
    if not pub_session:
        pub_session = str(uuid.uuid4())

    # 确保URL格式正确，移除多余空格，让JSON序列化自动处理转义
    image_url = image_url.replace(" ", "")
    print(f"处理后的URL: {image_url}")
    
    # 按照实际POST数据的精确格式构建statInfo
    # 注意：这里的转义层级必须与实际数据完全匹配

    # 第一层：构建最内层的配置JSON字符串
    music_frame_config_str = json.dumps({
        "type": "music",
        "frameResolutionSize": 256,
        "frameCount": 2,
        "frameInterval": 0.5,
        "frameQuality": 50,
        "frameDecodeTimeout": 1000,
        "frameUploadTimeout": 1500
    }, separators=(',', ':'))

    topic_frame_config_str = json.dumps({
        "type": "topic",
        "frameResolutionSize": 256,
        "frameCount": 5,
        "frameInterval": 0.2,
        "frameQuality": 70,
        "frameDecodeTimeout": 5000,
        "frameUploadTimeout": 4000
    }, separators=(',', ':'))

    # 第二层：构建ab_test_info JSON字符串
    ab_test_info_str = json.dumps({
        "musicFrameConfig": music_frame_config_str,
        "topicFrameConfig": topic_frame_config_str
    }, separators=(',', ':'))

    # 第三层：构建完整的statInfo结构（按实际数据字段顺序）
    stat_info = {
        "ab_test_info": ab_test_info_str,
        "camera_rotation": 0,
        "filter": [{}],
        "fun_id": {},
        "goods_id": str(goods_id),
        "is_hq_record": False,
        "itemsticker_items": [],
        "pub_session": pub_session,
        "source": "user_record",
        "additionalInfo": {
            "imageSource": "1",
            "containExif": False,
            "isScreenshot": False,
            "OS": "Android"
        }
    }

    # 关键修复：增加额外的JSON序列化层以匹配正确的转义层级
    # 第一次序列化
    stat_info_json = json.dumps(stat_info, separators=(',', ':'))
    # 第二次序列化以达到正确的转义层级
    final_stat_info = json.dumps(stat_info_json, separators=(',', ':'))

    # 返回完整的图片数据结构
    return {
        "fileSourceTag": "taobao_camera",
        "height": 1440,
        "statInfo": final_stat_info,  # 使用双重序列化的结果
        "url": image_url,
        "width": 1080
    }

# 测试函数
if __name__ == "__main__":
    # 测试数据
    test_urls = [
        "https://img.alicdn.com/imgextra/i4/O1CN01vxUpjm1qV3QLI59Ql_!!4611686018427387244-0-rate.jpg",
        "https://img.alicdn.com/imgextra/i3/O1CN01DuVN6T1qV3QM1g7me_!!4611686018427387244-0-rate.jpg"
    ]
    
    goods_id = "821648750509"
    
    # 生成图片数据
    images_data = []
    for url in test_urls:
        image_data = create_image_data(url, goods_id)
        images_data.append(image_data)
    
    # 输出结果
    print("\n生成的图片数据结构:")
    print(json.dumps(images_data, separators=(',', ':'), indent=2))
    
    # 检查pub_session是否不同
    pub_sessions = [img['statInfo'] for img in images_data]
    print(f"\n第一张图片的pub_session在statInfo中: {pub_sessions[0].count('pub_session')}")
    print(f"第二张图片的pub_session在statInfo中: {pub_sessions[1].count('pub_session')}")
    
    # 检查转义层级
    first_stat_info = images_data[0]['statInfo']
    print(f"\n第一张图片statInfo的转义层级示例:")
    print(f"包含的转义模式: {first_stat_info[:200]}...")
