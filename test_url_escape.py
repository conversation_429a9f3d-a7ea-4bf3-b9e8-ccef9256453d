import json

def test_url_escaping():
    """测试URL转义的正确性"""
    
    # 原始URL
    original_url = "https://img.alicdn.com/imgextra/i4/O1CN01vxUpjm1qV3QLI59Ql_!!4611686018427387244-0-rate.jpg"
    
    # 错误的方法（会产生双重转义）
    wrong_escaped = original_url.replace("/", "\\/")
    print(f"错误方法结果: {wrong_escaped}")
    
    # 正确的方法
    correct_escaped = original_url.replace("/", "\/")
    print(f"正确方法结果: {correct_escaped}")
    
    # 目标格式（从1.txt）
    target_format = "https:\/\/img.alicdn.com\/imgextra\/i4\/O1CN01vxUpjm1qV3QLI59Ql_!!4611686018427387244-0-rate.jpg"
    print(f"目标格式: {target_format}")
    
    # 检查是否匹配
    print(f"是否匹配目标格式: {correct_escaped == target_format}")
    
    # 测试在JSON中的表现
    test_data = {"url": correct_escaped}
    json_result = json.dumps(test_data, separators=(',', ':'))
    print(f"JSON序列化后: {json_result}")
    
    return correct_escaped

if __name__ == "__main__":
    test_url_escaping()
